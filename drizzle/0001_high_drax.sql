CREATE TYPE "public"."repetetion" AS ENUM('singleTime', 'daily', 'weekly', 'monthly', 'yearly', 'untilSuccess', 'other');--> statement-breakpoint
CREATE TYPE "public"."state" AS ENUM('planned', 'inProgress', 'finished', 'late');--> statement-breakpoint
CREATE TABLE "audit_answers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"audit_instance_id" uuid,
	"question_answers" jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "audit_instances" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"audit_type_id" uuid NOT NULL,
	"responsible_person_id" uuid NOT NULL,
	"repetetion_plan" "repetetion" NOT NULL,
	"expected_duration" interval NOT NULL,
	"planned_date" date NOT NULL,
	"real_date" date,
	"status" "state" DEFAULT 'planned' NOT NULL,
	"questions" jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "audit_types" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL
);
--> statement-breakpoint
ALTER TABLE "audit_answers" ADD CONSTRAINT "audit_answers_audit_instance_id_audit_instances_id_fk" FOREIGN KEY ("audit_instance_id") REFERENCES "public"."audit_instances"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_instances" ADD CONSTRAINT "audit_instances_audit_type_id_audit_types_id_fk" FOREIGN KEY ("audit_type_id") REFERENCES "public"."audit_types"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_instances" ADD CONSTRAINT "audit_instances_responsible_person_id_users_id_fk" FOREIGN KEY ("responsible_person_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;