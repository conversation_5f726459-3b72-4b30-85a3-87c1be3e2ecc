ALTER TABLE "workspaces" RENAME TO "workplaces";--> statement-breakpoint
ALTER TABLE "audit_instances" DROP CONSTRAINT "audit_instances_workspace_id_workspaces_id_fk";
--> statement-breakpoint
ALTER TABLE "users_work_info" DROP CONSTRAINT "users_work_info_plant_id_workspaces_id_fk";
--> statement-breakpoint
ALTER TABLE "workplaces" DROP CONSTRAINT "workspaces_responsible_person_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "audit_instances" ADD CONSTRAINT "audit_instances_workspace_id_workplaces_id_fk" FOREIGN KEY ("workspace_id") REFERENCES "public"."workplaces"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users_work_info" ADD CONSTRAINT "users_work_info_plant_id_workplaces_id_fk" FOREIGN KEY ("plant_id") REFERENCES "public"."workplaces"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "workplaces" ADD CONSTRAINT "workplaces_responsible_person_id_users_id_fk" FOREIGN KEY ("responsible_person_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;