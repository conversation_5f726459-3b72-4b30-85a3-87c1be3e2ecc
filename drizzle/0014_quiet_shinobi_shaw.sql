ALTER TABLE "audit_instances" DROP CONSTRAINT "audit_instances_responsible_person_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "audit_types" ADD COLUMN "responsible_person_id" uuid;
--> statement-breakpoint
ALTER TABLE "audit_types" ADD COLUMN "repetetion_plan" "repetetion";
--> statement-breakpoint
UPDATE "audit_types" SET "repetetion_plan" = 'singleTime';
--> statement-breakpoint
ALTER TABLE "audit_types" ALTER COLUMN "repetetion_plan" SET NOT NULL;
--> statement-breakpoint
ALTER TABLE "audit_types" ADD COLUMN "expected_duration" integer DEFAULT 60 NOT NULL;
--> statement-breakpoint
ALTER TABLE "audit_types" ADD CONSTRAINT "audit_types_responsible_person_id_users_id_fk" FOREIGN KEY ("responsible_person_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "audit_instances" DROP COLUMN "responsible_person_id";
--> statement-breakpoint
ALTER TABLE "audit_instances" DROP COLUMN "repetetion_plan";
--> statement-breakpoint
ALTER TABLE "audit_instances" DROP COLUMN "expected_duration";
