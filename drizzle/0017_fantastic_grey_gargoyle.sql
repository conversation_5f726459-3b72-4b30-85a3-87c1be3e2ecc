ALTER TABLE "audit_instances" ADD COLUMN "responsible_person_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "audit_instances" ADD CONSTRAINT "audit_instances_responsible_person_id_users_id_fk" FOREIGN KEY ("responsible_person_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_types" DROP COLUMN "template_type";--> statement-breakpoint
DROP TYPE "public"."audit_type";