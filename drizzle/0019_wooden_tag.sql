ALTER TABLE "audit_instances" RENAME TO "audits";--> statement-breakpoint
ALTER TABLE "audit_types" RENAME TO "templates";--> statement-breakpoint
ALTER TABLE "audit_answers" DROP CONSTRAINT "audit_answers_audit_instance_id_audit_instances_id_fk";
--> statement-breakpoint
ALTER TABLE "audits" DROP CONSTRAINT "audit_instances_audit_type_id_audit_types_id_fk";
--> statement-breakpoint
ALTER TABLE "audits" DROP CONSTRAINT "audit_instances_workspace_id_workplaces_id_fk";
--> statement-breakpoint
ALTER TABLE "audits" DROP CONSTRAINT "audit_instances_responsible_person_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "templates" DROP CONSTRAINT "audit_types_responsible_person_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "audit_answers" ADD CONSTRAINT "audit_answers_audit_instance_id_audits_id_fk" FOREIGN KEY ("audit_instance_id") REFERENCES "public"."audits"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audits" ADD CONSTRAINT "audits_audit_type_id_templates_id_fk" FOREIGN KEY ("audit_type_id") REFERENCES "public"."templates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audits" ADD CONSTRAINT "audits_workspace_id_workplaces_id_fk" FOREIGN KEY ("workspace_id") REFERENCES "public"."workplaces"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audits" ADD CONSTRAINT "audits_responsible_person_id_users_id_fk" FOREIGN KEY ("responsible_person_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "templates" ADD CONSTRAINT "templates_responsible_person_id_users_id_fk" FOREIGN KEY ("responsible_person_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;