CREATE TABLE "questions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"text" text NOT NULL,
	"subtext" text
);
--> statement-breakpoint
ALTER TABLE "audit_answers" RENAME COLUMN "audit_instance_id" TO "audit_id";--> statement-breakpoint
ALTER TABLE "audit_answers" DROP CONSTRAINT "audit_answers_audit_instance_id_audits_id_fk";
--> statement-breakpoint
ALTER TABLE "audit_answers" ADD COLUMN "question_id" uuid;--> statement-breakpoint
ALTER TABLE "audit_answers" ADD COLUMN "evaluation_value" text DEFAULT 'Not visited' NOT NULL;--> statement-breakpoint
ALTER TABLE "audit_answers" ADD COLUMN "note" text;--> statement-breakpoint
ALTER TABLE "audit_answers" ADD CONSTRAINT "audit_answers_question_id_questions_id_fk" FOREIGN KEY ("question_id") REFERENCES "public"."questions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_answers" ADD CONSTRAINT "audit_answers_audit_id_audits_id_fk" FOREIGN KEY ("audit_id") REFERENCES "public"."audits"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_answers" DROP COLUMN "question_answers";--> statement-breakpoint
ALTER TABLE "audits" DROP COLUMN "progress";--> statement-breakpoint
ALTER TABLE "audits" DROP COLUMN "questions";