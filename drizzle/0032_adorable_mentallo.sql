ALTER TABLE "users_workspace_roles" RENAME TO "user_plants";--> statement-breakpoint
ALTER TABLE "users_work_info" DROP CONSTRAINT "users_work_info_plant_id_workplaces_id_fk";
--> statement-breakpoint
ALTER TABLE "user_plants" DROP CONSTRAINT "users_workspace_roles_user_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "user_plants" DROP CONSTRAINT "users_workspace_roles_plant_id_plants_id_fk";
--> statement-breakpoint
ALTER TABLE "users_work_info" ADD CONSTRAINT "users_work_info_plant_id_plants_id_fk" FOREIGN KEY ("plant_id") REFERENCES "public"."plants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_plants" ADD CONSTRAINT "user_plants_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_plants" ADD CONSTRAINT "user_plants_plant_id_plants_id_fk" FOREIGN KEY ("plant_id") REFERENCES "public"."plants"("id") ON DELETE no action ON UPDATE no action;