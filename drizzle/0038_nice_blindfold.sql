CREATE TABLE "plants_configuration" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"plant_id" uuid NOT NULL,
	"points_range_min" integer DEFAULT 0 NOT NULL,
	"points_range_max" integer DEFAULT 10 NOT NULL
);
--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "number_of_licenses" integer DEFAULT 10 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants" ADD COLUMN "number_of_licenses" integer DEFAULT 10 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_configuration" ADD CONSTRAINT "plants_configuration_plant_id_plants_id_fk" FOREIGN KEY ("plant_id") REFERENCES "public"."plants"("id") ON DELETE no action ON UPDATE no action;