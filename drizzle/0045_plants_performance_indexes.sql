-- Index for company slug lookup (most common operation)
CREATE INDEX idx_companies_slug ON companies(slug);

-- Composite index for plant lookup within company (critical for multi-tenant)
CREATE INDEX idx_plants_company_slug ON plants(company_id, slug);

-- Index for getting all plants in a company
CREATE INDEX idx_plants_company_id ON plants(company_id);

-- Optional: Index for plant code lookup within company
CREATE INDEX idx_plants_company_code ON plants(company_id, code);
