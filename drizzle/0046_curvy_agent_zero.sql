CREATE TABLE "plants_evaluation_configuration" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"plant_id" uuid NOT NULL,
	"points_range_min" integer DEFAULT 0 NOT NULL,
	"points_range_max" integer DEFAULT 10 NOT NULL,
	"audit_threshold_average" integer DEFAULT 70,
	"audit_threshold_success" integer DEFAULT 90
);
--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD CONSTRAINT "plants_evaluation_configuration_plant_id_plants_id_fk" FOREIGN KEY ("plant_id") REFERENCES "public"."plants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "plants_configuration" DROP COLUMN "points_range_min";--> statement-breakpoint
ALTER TABLE "plants_configuration" DROP COLUMN "points_range_max";