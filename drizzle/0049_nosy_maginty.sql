CREATE TABLE "audit_instance_evaluation_configuration" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"audit_type_eval_config_id" uuid NOT NULL,
	"plant_eval_config_id" uuid NOT NULL,
	"evaluation_mode" text DEFAULT 'percentage' NOT NULL,
	"eval_weight_w_reservations" numeric NOT NULL,
	"points_range_min" integer NOT NULL,
	"points_range_max" integer NOT NULL,
	"percentage_threshold_average" integer NOT NULL,
	"percentage_threshold_success" integer NOT NULL,
	"audit_threshold_average" integer NOT NULL,
	"audit_threshold_success" integer NOT NULL,
	"rules_average_average_audit_answers" integer NOT NULL,
	"rules_not_successful_average_answers" integer NOT NULL,
	"rules_not_successful_bad_answers" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "audit_type_evaluation_configuration" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"audit_type_id" uuid NOT NULL,
	"evaluation_mode" text DEFAULT 'percentage' NOT NULL,
	"eval_weight_w_reservations" numeric,
	"points_range_min" integer,
	"points_range_max" integer,
	"percentage_threshold_average" integer,
	"percentage_threshold_success" integer,
	"audit_threshold_average" integer,
	"audit_threshold_success" integer,
	"rules_average_average_audit_answers" integer,
	"rules_not_successful_average_answers" integer,
	"rules_not_successful_bad_answers" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "answers" ALTER COLUMN "normalized_value" SET DEFAULT '0';--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ALTER COLUMN "audit_threshold_average" SET DEFAULT 45;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ALTER COLUMN "audit_threshold_average" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ALTER COLUMN "audit_threshold_success" SET DEFAULT 70;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ALTER COLUMN "audit_threshold_success" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "audits" ADD COLUMN "evaluation_config_id" uuid;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "eval_weight_w_reservations" numeric DEFAULT '0.5' NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "percentage_threshold_average" integer DEFAULT 45 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "percentage_threshold_success" integer DEFAULT 70 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "rules_average_average_audit_answers" integer DEFAULT 2 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "rules_not_successful_average_answers" integer DEFAULT 4 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "rules_not_successful_bad_answers" integer DEFAULT 6 NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "created_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "plants_evaluation_configuration" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "audit_instance_evaluation_configuration" ADD CONSTRAINT "audit_instance_evaluation_configuration_audit_type_eval_config_id_audit_type_evaluation_configuration_id_fk" FOREIGN KEY ("audit_type_eval_config_id") REFERENCES "public"."audit_type_evaluation_configuration"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_instance_evaluation_configuration" ADD CONSTRAINT "audit_instance_evaluation_configuration_plant_eval_config_id_plants_evaluation_configuration_id_fk" FOREIGN KEY ("plant_eval_config_id") REFERENCES "public"."plants_evaluation_configuration"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audit_type_evaluation_configuration" ADD CONSTRAINT "audit_type_evaluation_configuration_audit_type_id_templates_id_fk" FOREIGN KEY ("audit_type_id") REFERENCES "public"."templates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audits" ADD CONSTRAINT "audits_evaluation_config_id_audit_instance_evaluation_configuration_id_fk" FOREIGN KEY ("evaluation_config_id") REFERENCES "public"."audit_instance_evaluation_configuration"("id") ON DELETE no action ON UPDATE no action;