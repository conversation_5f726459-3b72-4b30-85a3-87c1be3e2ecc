ALTER TABLE "audit_type_evaluation_configuration" RENAME TO "template_evaluation_configuration";--> statement-breakpoint
ALTER TABLE "audit_instance_evaluation_configuration" DROP CONSTRAINT "audit_instance_evaluation_configuration_audit_type_eval_config_id_audit_type_evaluation_configuration_id_fk";
--> statement-breakpoint
ALTER TABLE "template_evaluation_configuration" DROP CONSTRAINT "audit_type_evaluation_configuration_audit_type_id_templates_id_fk";
--> statement-breakpoint
ALTER TABLE "audit_instance_evaluation_configuration" ADD CONSTRAINT "audit_instance_evaluation_configuration_audit_type_eval_config_id_template_evaluation_configuration_id_fk" FOREIGN KEY ("audit_type_eval_config_id") REFERENCES "public"."template_evaluation_configuration"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "template_evaluation_configuration" ADD CONSTRAINT "template_evaluation_configuration_audit_type_id_templates_id_fk" FOREIGN KEY ("audit_type_id") REFERENCES "public"."templates"("id") ON DELETE no action ON UPDATE no action;