ALTER TABLE "companies" DROP CONSTRAINT "companies_slug_unique";--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "deleted" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "companies" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "plants" ADD COLUMN "deleted" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "plants" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint

-- indxes for companies and plants that are not soft deleted
CREATE UNIQUE INDEX companies_slug_unique_not_deleted
ON companies (slug)
WHERE deleted = false;--> statement-breakpoint


CREATE UNIQUE INDEX plants_slug_unique_not_deleted
ON plants (slug)
WHERE deleted = false;