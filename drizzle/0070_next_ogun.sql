ALTER TABLE "plant_roles" DROP CONSTRAINT "plant_roles_role_id_roles_id_fk";
--> statement-breakpoint
ALTER TABLE "user_role_plants" DROP CONSTRAINT "user_role_plants_role_id_roles_id_fk";
--> statement-breakpoint
ALTER TABLE "plant_roles" ALTER COLUMN "plant_id" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user_role_plants" ADD CONSTRAINT "user_role_plants_role_id_plant_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."plant_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "plant_roles" DROP COLUMN "role_id";