{"id": "ff0c0918-fdd3-4880-ad66-6e0b9fa8801e", "prevId": "286cbd43-87bc-41a0-8ada-9ee2cb2d7103", "version": "7", "dialect": "postgresql", "tables": {"public.answers_attachments": {"name": "answers_attachments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "answer_id": {"name": "answer_id", "type": "uuid", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"answers_attachments_answer_id_answers_id_fk": {"name": "answers_attachments_answer_id_answers_id_fk", "tableFrom": "answers_attachments", "tableTo": "answers", "columnsFrom": ["answer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.answers": {"name": "answers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_id": {"name": "question_id", "type": "uuid", "primaryKey": false, "notNull": true}, "audit_id": {"name": "audit_id", "type": "uuid", "primaryKey": false, "notNull": true}, "evaluation_value": {"name": "evaluation_value", "type": "text", "primaryKey": false, "notNull": true, "default": "'NotVisited'"}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"answers_question_id_questions_id_fk": {"name": "answers_question_id_questions_id_fk", "tableFrom": "answers", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "answers_audit_id_audits_id_fk": {"name": "answers_audit_id_audits_id_fk", "tableFrom": "answers", "tableTo": "audits", "columnsFrom": ["audit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audits": {"name": "audits", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "audit_type_id": {"name": "audit_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "responsible_person_id": {"name": "responsible_person_id", "type": "uuid", "primaryKey": false, "notNull": false}, "real_duration": {"name": "real_duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "planned_date": {"name": "planned_date", "type": "date", "primaryKey": false, "notNull": true}, "completion_date": {"name": "completion_date", "type": "date", "primaryKey": false, "notNull": false}, "questions": {"name": "questions", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audits_audit_type_id_templates_id_fk": {"name": "audits_audit_type_id_templates_id_fk", "tableFrom": "audits", "tableTo": "templates", "columnsFrom": ["audit_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audits_workspace_id_workplaces_id_fk": {"name": "audits_workspace_id_workplaces_id_fk", "tableFrom": "audits", "tableTo": "workplaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audits_responsible_person_id_users_id_fk": {"name": "audits_responsible_person_id_users_id_fk", "tableFrom": "audits", "tableTo": "users", "columnsFrom": ["responsible_person_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.templates": {"name": "templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "responsible_person_id": {"name": "responsible_person_id", "type": "uuid", "primaryKey": false, "notNull": true}, "repetetion_plan": {"name": "repetetion_plan", "type": "repetetion", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expected_duration": {"name": "expected_duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "specification": {"name": "specification", "type": "text", "primaryKey": false, "notNull": false}, "questions": {"name": "questions", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"templates_responsible_person_id_users_id_fk": {"name": "templates_responsible_person_id_users_id_fk", "tableFrom": "templates", "tableTo": "users", "columnsFrom": ["responsible_person_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.questions": {"name": "questions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "subtext": {"name": "subtext", "type": "text", "primaryKey": false, "notNull": false}, "evaluation_type": {"name": "evaluation_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'oknok'"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_auths": {"name": "users_auths", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "password_reset": {"name": "password_reset", "type": "text", "primaryKey": false, "notNull": false}, "password_reset_exp": {"name": "password_reset_exp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_auths_user_id_users_id_fk": {"name": "users_auths_user_id_users_id_fk", "tableFrom": "users_auths", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_auths_user_id_unique": {"name": "users_auths_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_code_unique": {"name": "companies_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plants": {"name": "plants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "uuid", "primaryKey": false, "notNull": true}, "gps_location": {"name": "gps_location", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"plants_company_id_companies_id_fk": {"name": "plants_company_id_companies_id_fk", "tableFrom": "plants", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_company_id_companies_id_fk": {"name": "users_company_id_companies_id_fk", "tableFrom": "users", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_work_info": {"name": "users_work_info", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "card_number": {"name": "card_number", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"users_work_info_user_id_users_id_fk": {"name": "users_work_info_user_id_users_id_fk", "tableFrom": "users_work_info", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_work_info_plant_id_workplaces_id_fk": {"name": "users_work_info_plant_id_workplaces_id_fk", "tableFrom": "users_work_info", "tableTo": "workplaces", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_workspace_roles": {"name": "users_workspace_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"users_workspace_roles_user_id_users_id_fk": {"name": "users_workspace_roles_user_id_users_id_fk", "tableFrom": "users_workspace_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_workspace_roles_plant_id_plants_id_fk": {"name": "users_workspace_roles_plant_id_plants_id_fk", "tableFrom": "users_workspace_roles", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workplaces": {"name": "workplaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "responsible_person_id": {"name": "responsible_person_id", "type": "uuid", "primaryKey": false, "notNull": false}, "ekaizen_workstation_id": {"name": "ekaizen_workstation_id", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workplaces_responsible_person_id_users_id_fk": {"name": "workplaces_responsible_person_id_users_id_fk", "tableFrom": "workplaces", "tableTo": "users", "columnsFrom": ["responsible_person_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.repetetion": {"name": "repetetion", "schema": "public", "values": ["singleTime", "daily", "weekly", "monthly", "yearly", "untilSuccess", "other"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}