{"id": "fc2166db-b6a7-45a5-b796-5d57ad540bc4", "prevId": "8b18b7d6-a47a-4c1d-815c-fbc905035421", "version": "7", "dialect": "postgresql", "tables": {"public.answers_attachments": {"name": "answers_attachments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "answer_id": {"name": "answer_id", "type": "uuid", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "presigned_url": {"name": "presigned_url", "type": "text", "primaryKey": false, "notNull": false}, "presigned_url_generated_at": {"name": "presigned_url_generated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"answers_attachments_answer_id_answers_id_fk": {"name": "answers_attachments_answer_id_answers_id_fk", "tableFrom": "answers_attachments", "tableTo": "answers", "columnsFrom": ["answer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.answers": {"name": "answers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_id": {"name": "question_id", "type": "uuid", "primaryKey": false, "notNull": true}, "audit_id": {"name": "audit_id", "type": "uuid", "primaryKey": false, "notNull": true}, "evaluation_value": {"name": "evaluation_value", "type": "text", "primaryKey": false, "notNull": true, "default": "'NotVisited'"}, "normalized_value": {"name": "normalized_value", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"answers_question_id_questions_id_fk": {"name": "answers_question_id_questions_id_fk", "tableFrom": "answers", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "answers_audit_id_audits_id_fk": {"name": "answers_audit_id_audits_id_fk", "tableFrom": "answers", "tableTo": "audits", "columnsFrom": ["audit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_instance_evaluation_configuration": {"name": "audit_instance_evaluation_configuration", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "audit_type_eval_config_id": {"name": "audit_type_eval_config_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plant_eval_config_id": {"name": "plant_eval_config_id", "type": "uuid", "primaryKey": false, "notNull": true}, "evaluation_mode": {"name": "evaluation_mode", "type": "text", "primaryKey": false, "notNull": true, "default": "'percentage'"}, "eval_weight_w_reservations": {"name": "eval_weight_w_reservations", "type": "numeric", "primaryKey": false, "notNull": true}, "points_range_min": {"name": "points_range_min", "type": "integer", "primaryKey": false, "notNull": true}, "points_range_max": {"name": "points_range_max", "type": "integer", "primaryKey": false, "notNull": true}, "points_avg_threshold": {"name": "points_avg_threshold", "type": "integer", "primaryKey": false, "notNull": true}, "points_success_threshold": {"name": "points_success_threshold", "type": "integer", "primaryKey": false, "notNull": true}, "percentage_threshold_average": {"name": "percentage_threshold_average", "type": "integer", "primaryKey": false, "notNull": true}, "percentage_threshold_success": {"name": "percentage_threshold_success", "type": "integer", "primaryKey": false, "notNull": true}, "audit_threshold_average": {"name": "audit_threshold_average", "type": "integer", "primaryKey": false, "notNull": true}, "audit_threshold_success": {"name": "audit_threshold_success", "type": "integer", "primaryKey": false, "notNull": true}, "rules_average_average_audit_answers": {"name": "rules_average_average_audit_answers", "type": "integer", "primaryKey": false, "notNull": true}, "rules_average_bad_audit_answers": {"name": "rules_average_bad_audit_answers", "type": "integer", "primaryKey": false, "notNull": true}, "rules_not_successful_average_answers": {"name": "rules_not_successful_average_answers", "type": "integer", "primaryKey": false, "notNull": true}, "rules_not_successful_bad_answers": {"name": "rules_not_successful_bad_answers", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_instance_evaluation_configuration_audit_type_eval_config_id_template_evaluation_configuration_id_fk": {"name": "audit_instance_evaluation_configuration_audit_type_eval_config_id_template_evaluation_configuration_id_fk", "tableFrom": "audit_instance_evaluation_configuration", "tableTo": "template_evaluation_configuration", "columnsFrom": ["audit_type_eval_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audit_instance_evaluation_configuration_plant_eval_config_id_plants_evaluation_configuration_id_fk": {"name": "audit_instance_evaluation_configuration_plant_eval_config_id_plants_evaluation_configuration_id_fk", "tableFrom": "audit_instance_evaluation_configuration", "tableTo": "plants_evaluation_configuration", "columnsFrom": ["plant_eval_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audits": {"name": "audits", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "audit_type_id": {"name": "audit_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "responsible_person_id": {"name": "responsible_person_id", "type": "uuid", "primaryKey": false, "notNull": false}, "real_duration": {"name": "real_duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "planned_date": {"name": "planned_date", "type": "date", "primaryKey": false, "notNull": true}, "completion_date": {"name": "completion_date", "type": "date", "primaryKey": false, "notNull": false}, "questions": {"name": "questions", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "success_rate": {"name": "success_rate", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rules_result": {"name": "rules_result", "type": "text", "primaryKey": false, "notNull": false}, "evaluation_config_id": {"name": "evaluation_config_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audits_audit_type_id_templates_id_fk": {"name": "audits_audit_type_id_templates_id_fk", "tableFrom": "audits", "tableTo": "templates", "columnsFrom": ["audit_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audits_workspace_id_workplaces_id_fk": {"name": "audits_workspace_id_workplaces_id_fk", "tableFrom": "audits", "tableTo": "workplaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audits_responsible_person_id_users_id_fk": {"name": "audits_responsible_person_id_users_id_fk", "tableFrom": "audits", "tableTo": "users", "columnsFrom": ["responsible_person_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audits_evaluation_config_id_audit_instance_evaluation_configuration_id_fk": {"name": "audits_evaluation_config_id_audit_instance_evaluation_configuration_id_fk", "tableFrom": "audits", "tableTo": "audit_instance_evaluation_configuration", "columnsFrom": ["evaluation_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audits_created_by_users_id_fk": {"name": "audits_created_by_users_id_fk", "tableFrom": "audits", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.template_evaluation_configuration": {"name": "template_evaluation_configuration", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "audit_type_id": {"name": "audit_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "evaluation_mode": {"name": "evaluation_mode", "type": "text", "primaryKey": false, "notNull": true, "default": "'percentage'"}, "eval_weight_w_reservations": {"name": "eval_weight_w_reservations", "type": "numeric", "primaryKey": false, "notNull": false}, "points_range_min": {"name": "points_range_min", "type": "integer", "primaryKey": false, "notNull": false}, "points_range_max": {"name": "points_range_max", "type": "integer", "primaryKey": false, "notNull": false}, "points_avg_threshold": {"name": "points_avg_threshold", "type": "integer", "primaryKey": false, "notNull": false}, "points_success_threshold": {"name": "points_success_threshold", "type": "integer", "primaryKey": false, "notNull": false}, "percentage_threshold_average": {"name": "percentage_threshold_average", "type": "integer", "primaryKey": false, "notNull": false}, "percentage_threshold_success": {"name": "percentage_threshold_success", "type": "integer", "primaryKey": false, "notNull": false}, "audit_threshold_average": {"name": "audit_threshold_average", "type": "integer", "primaryKey": false, "notNull": false}, "audit_threshold_success": {"name": "audit_threshold_success", "type": "integer", "primaryKey": false, "notNull": false}, "rules_average_average_audit_answers": {"name": "rules_average_average_audit_answers", "type": "integer", "primaryKey": false, "notNull": false}, "rules_average_bad_audit_answers": {"name": "rules_average_bad_audit_answers", "type": "integer", "primaryKey": false, "notNull": false}, "rules_not_successful_average_answers": {"name": "rules_not_successful_average_answers", "type": "integer", "primaryKey": false, "notNull": false}, "rules_not_successful_bad_answers": {"name": "rules_not_successful_bad_answers", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"template_evaluation_configuration_audit_type_id_templates_id_fk": {"name": "template_evaluation_configuration_audit_type_id_templates_id_fk", "tableFrom": "template_evaluation_configuration", "tableTo": "templates", "columnsFrom": ["audit_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.templates": {"name": "templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "responsible_person_id": {"name": "responsible_person_id", "type": "uuid", "primaryKey": false, "notNull": true}, "repetetion_plan": {"name": "repetetion_plan", "type": "repetetion", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expected_duration": {"name": "expected_duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "specification": {"name": "specification", "type": "text", "primaryKey": false, "notNull": false}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "questions": {"name": "questions", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"templates_responsible_person_id_users_id_fk": {"name": "templates_responsible_person_id_users_id_fk", "tableFrom": "templates", "tableTo": "users", "columnsFrom": ["responsible_person_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "templates_plant_id_plants_id_fk": {"name": "templates_plant_id_plants_id_fk", "tableFrom": "templates", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.question_tags": {"name": "question_tags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_id": {"name": "question_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"question_tags_question_id_questions_id_fk": {"name": "question_tags_question_id_questions_id_fk", "tableFrom": "question_tags", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "question_tags_tag_id_tags_id_fk": {"name": "question_tags_tag_id_tags_id_fk", "tableFrom": "question_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.questions": {"name": "questions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "subtext": {"name": "subtext", "type": "text", "primaryKey": false, "notNull": false}, "evaluation_type": {"name": "evaluation_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'oknok'"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"questions_plant_id_plants_id_fk": {"name": "questions_plant_id_plants_id_fk", "tableFrom": "questions", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3B82F6'"}, "text_color": {"name": "text_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'white'"}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tags_plant_id_plants_id_fk": {"name": "tags_plant_id_plants_id_fk", "tableFrom": "tags", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_auths": {"name": "users_auths", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "password_reset": {"name": "password_reset", "type": "text", "primaryKey": false, "notNull": false}, "password_reset_exp": {"name": "password_reset_exp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_auths_user_id_users_id_fk": {"name": "users_auths_user_id_users_id_fk", "tableFrom": "users_auths", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_auths_user_id_unique": {"name": "users_auths_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "number_of_licenses": {"name": "number_of_licenses", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_code_unique": {"name": "companies_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plants_configuration": {"name": "plants_configuration", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "planning_horizon_months": {"name": "planning_horizon_months", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "supported_languages": {"name": "supported_languages", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{\"en\"}'"}, "default_language": {"name": "default_language", "type": "text", "primaryKey": false, "notNull": true, "default": "'en'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"plants_configuration_plant_id_plants_id_fk": {"name": "plants_configuration_plant_id_plants_id_fk", "tableFrom": "plants_configuration", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plants_evaluation_configuration": {"name": "plants_evaluation_configuration", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "eval_weight_w_reservations": {"name": "eval_weight_w_reservations", "type": "numeric", "primaryKey": false, "notNull": true, "default": "'0.5'"}, "points_range_min": {"name": "points_range_min", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "points_range_max": {"name": "points_range_max", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "points_avg_threshold": {"name": "points_avg_threshold", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "points_success_threshold": {"name": "points_success_threshold", "type": "integer", "primaryKey": false, "notNull": true, "default": 8}, "percentage_threshold_average": {"name": "percentage_threshold_average", "type": "integer", "primaryKey": false, "notNull": true, "default": 45}, "percentage_threshold_success": {"name": "percentage_threshold_success", "type": "integer", "primaryKey": false, "notNull": true, "default": 70}, "audit_threshold_average": {"name": "audit_threshold_average", "type": "integer", "primaryKey": false, "notNull": true, "default": 45}, "audit_threshold_success": {"name": "audit_threshold_success", "type": "integer", "primaryKey": false, "notNull": true, "default": 70}, "rules_average_average_audit_answers": {"name": "rules_average_average_audit_answers", "type": "integer", "primaryKey": false, "notNull": true, "default": 2}, "rules_average_bad_audit_answers": {"name": "rules_average_bad_audit_answers", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "rules_not_successful_average_answers": {"name": "rules_not_successful_average_answers", "type": "integer", "primaryKey": false, "notNull": true, "default": 4}, "rules_not_successful_bad_answers": {"name": "rules_not_successful_bad_answers", "type": "integer", "primaryKey": false, "notNull": true, "default": 6}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"plants_evaluation_configuration_plant_id_plants_id_fk": {"name": "plants_evaluation_configuration_plant_id_plants_id_fk", "tableFrom": "plants_evaluation_configuration", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plants": {"name": "plants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "ekaizen_form_url": {"name": "ekaizen_form_url", "type": "text", "primaryKey": false, "notNull": false}, "tasks_enabled": {"name": "tasks_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "uuid", "primaryKey": false, "notNull": true}, "gps_location": {"name": "gps_location", "type": "text", "primaryKey": false, "notNull": false}, "number_of_licenses": {"name": "number_of_licenses", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"plants_company_id_companies_id_fk": {"name": "plants_company_id_companies_id_fk", "tableFrom": "plants", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"plants_ekaizen_form_url_unique": {"name": "plants_ekaizen_form_url_unique", "nullsNotDistinct": false, "columns": ["ekaizen_form_url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_plants": {"name": "user_plants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_plants_user_id_users_id_fk": {"name": "user_plants_user_id_users_id_fk", "tableFrom": "user_plants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_plants_plant_id_plants_id_fk": {"name": "user_plants_plant_id_plants_id_fk", "tableFrom": "user_plants", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "company_id": {"name": "company_id", "type": "uuid", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_company_id_companies_id_fk": {"name": "users_company_id_companies_id_fk", "tableFrom": "users", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_work_info": {"name": "users_work_info", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "card_number": {"name": "card_number", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"users_work_info_user_id_users_id_fk": {"name": "users_work_info_user_id_users_id_fk", "tableFrom": "users_work_info", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_work_info_company_id_companies_id_fk": {"name": "users_work_info_company_id_companies_id_fk", "tableFrom": "users_work_info", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_work_info_plant_id_plants_id_fk": {"name": "users_work_info_plant_id_plants_id_fk", "tableFrom": "users_work_info", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workplaces": {"name": "workplaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "responsible_person_id": {"name": "responsible_person_id", "type": "uuid", "primaryKey": false, "notNull": false}, "plant_id": {"name": "plant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "ekaizen_workstation_id": {"name": "ekaizen_workstation_id", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workplaces_responsible_person_id_users_id_fk": {"name": "workplaces_responsible_person_id_users_id_fk", "tableFrom": "workplaces", "tableTo": "users", "columnsFrom": ["responsible_person_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workplaces_plant_id_plants_id_fk": {"name": "workplaces_plant_id_plants_id_fk", "tableFrom": "workplaces", "tableTo": "plants", "columnsFrom": ["plant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.repetetion": {"name": "repetetion", "schema": "public", "values": ["singleTime", "daily", "weekly", "monthly", "yearly", "untilSuccess", "other"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}