{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && node scripts/copy-templates.js", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "copy-templates": "node scripts/copy-templates.js", "migrate:plant-roles": "tsx scripts/migrateToPlantRoles.ts"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.513.0", "@sveltejs/adapter-node": "^5.2.11", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.1.7", "@types/d3-scale": "^4.0.9", "@types/d3-shape": "^3.1.7", "@types/node": "^22.13.17", "@types/nodemailer": "^6.4.17", "@types/pug": "^2.0.10", "autoprefixer": "^10.4.20", "bits-ui": "^2.5.0", "clsx": "^2.1.1", "drizzle-kit": "^0.30.2", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^2.0.1", "globals": "^16.0.0", "layerchart": "2.0.0-next.10", "mode-watcher": "^1.0.7", "paneforge": "1.0.0-next.5", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.33.3", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.4", "svelte-toolbelt": "^0.9.0", "sveltekit-superforms": "^2.24.0", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vaul-svelte": "1.0.0-next.7", "vite": "^6.0.0", "zod": "^3.24.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@badrap/result": "^0.2.13", "@dnd-kit-svelte/modifiers": "^0.0.8", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@tanstack/table-core": "^8.21.2", "@types/pdfmake": "^0.2.11", "@types/qrcode": "^1.5.5", "@types/sortablejs": "^1.15.8", "amplify-adapter": "^1.1.0", "bcryptjs": "^3.0.2", "d3-array": "^3.2.4", "d3-scale": "^4.0.2", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.40.0", "nodemailer": "^7.0.3", "pdfmake": "^0.2.20", "pnpm": "^10.11.0", "postgres": "^3.4.5", "pug": "^3.0.3", "qrcode": "^1.5.4", "sortablejs": "^1.15.6", "sveltekit-i18n": "^2.4.2", "ts-pattern": "^5.7.0", "uuid": "^11.1.0"}, "pnpm": {"onlyBuiltDependencies": ["bcrypt"]}}