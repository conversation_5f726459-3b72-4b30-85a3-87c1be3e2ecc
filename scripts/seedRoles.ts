import { rolesTable } from '../src/lib/db/schema/roles';
import { db } from '../src/lib/db/db.server'; // Add this import for the db instance

async function seedRoles() {
	const roles = [
		{
			name: 'auditor',
			permissions: {
				audits: [
					'audits:viewAudits',
					'audits:viewAuditResults',
					'audits:evaluateAudit',
					'audits:exportAudit'
				],
				workplaces: ['workplaces:viewWorkplaces', 'workplaces:viewWorkplaceDetails'],

				auditTypes: [],
				plantSettings: [],
				plantEvalTypes: [],
				tags: []
			}
		},
		{
			name: 'workplaceManager',
			permissions: {
				audits: [
					'audits:viewAudits',
					'audits:createAudit',
					'audits:editAudit',
					'audits:deleteAudit',
					'audits:viewAuditResults',
					'audits:exportAudit'
				],
				workplaces: [
					'workplaces:viewWorkplaces',
					'workplaces:editWorkplace',
					'workplaces:viewWorkplaceDetails'
				],

				auditTypes: [],
				plantSettings: [],
				plantEvalTypes: [],
				tags: []
			}
		},
		{
			name: 'manager',
			permissions: {
				audits: ['audits:viewAudits', 'audits:viewAuditResults', 'audits:exportAudit'],
				workplaces: [
					'workplaces:viewWorkplaces',
					'workplaces:editWorkplace',
					'workplaces:viewWorkplaceDetails'
				],
				auditTypes: [],
				plantSettings: [],
				plantEvalTypes: [],
				tags: []
			}
		},
		{
			name: 'admin',
			permissions: {
				audits: ['audits:*'],
				workplaces: ['workplaces:*'],
				auditTypes: ['auditTypes:*'],
				plantSettings: ['plantSettings:*'],
				plantEvalTypes: ['plantEvalTypes:*'],
				tags: ['tags:*']
			}
		}
	];

	for (const role of roles) {
		await db
			.insert(rolesTable)
			.values({
				name: role.name,
				permissions: role.permissions
			})
			.onConflictDoNothing();
	}

	console.log('Seeding finished ✅');
}

seedRoles()
	.catch((error) => {
		console.error('Error seeding roles:', error);
		process.exit(1);
	})
	.finally(() => process.exit(0));
