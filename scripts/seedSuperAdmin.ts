import { createSuperAdminRole } from '../src/lib/server/services/roles/plantRoles';

async function seedSuperAdmin() {
	try {
		await createSuperAdminRole();
		console.log('✅ Superadmin role seeding completed');
	} catch (error) {
		console.error('❌ Error seeding superadmin role:', error);
		process.exit(1);
	}
}

seedSuperAdmin()
	.catch((error) => {
		console.error('Error seeding superadmin role:', error);
		process.exit(1);
	})
	.finally(() => process.exit(0));
