// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces

import type { Session, AppUser } from '$lib/models/authModel';
import type { PlantRole } from '$lib/models/roleModel';
import type { Authorization } from '$lib/server/utils/authorization';

declare global {
	namespace App {
		interface Error {
			message: string;
			code?: number;
			details?: Record<string, unknown>;
		}
		interface Locals {
			user: AppUser | null;
			session: Session | null;
			plantRoles: Array<{ roleId: string; plantId: string | null }> | null; // Available roles for the current user in the context of all plants
			plantRole: PlantRole | null; // Current user's role w permissions
			authorization: Authorization | null;

			plantDefaultLanguage?: string; // Default language for the current plant context - for emails and notifications
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
