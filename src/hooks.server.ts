import { validateSessionToken } from '$lib/server/services/auth/authAPI';
import { deleteSessionCookies, setSessionCookies } from '$lib/utils/session';
import { redirect, type Handle } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import whitelist from '$lib/constants/whitelist.json';
import { CompanyService, PlantsService, PlantLanguageService } from '$lib/server/services/tenants';

export const authenticateUser: Handle = async ({ event, resolve }) => {
	const token = event.cookies.get('session') ?? null;

	if (!token) {
		event.locals.user = null;
		event.locals.session = null;
		return await resolve(event);
	}

	const { session, user, plantRoles } = await validateSessionToken(token);
	if (session != null) {
		setSessionCookies(event.cookies, token, session.expiresAt);
	} else {
		deleteSessionCookies(event.cookies);
	}

	event.locals.user = user;
	event.locals.session = session;
	event.locals.plantRoles = plantRoles;

	return await resolve(event);
};

export const setPlantLanguage: Handle = async ({ event, resolve }) => {
	const { pathname } = event.url;

	// Extract companySlug and plantSlug from URL pattern /{companySlug}/{plantSlug}/...
	const pathSegments = pathname.split('/').filter(Boolean);
	if (pathSegments.length >= 2 && event.locals.user) {
		const companySlug = pathSegments[0];
		const plantSlug = pathSegments[1];

		try {
			const company = await CompanyService.getCompanyBySlug(companySlug);
			if (!company) {
				return await resolve(event);
			}

			const plant = await PlantsService.getPlantBySlugAndCompany(plantSlug, company.id);
			if (plant && event.locals.user.plants.includes(plant.id)) {
				const plantDefaultLanguage = await PlantLanguageService.getPlantDefaultLanguage(plant.id);
				event.locals.plantDefaultLanguage = plantDefaultLanguage;
			}
		} catch (error) {
			console.warn('Failed to get plant default language:', error);
		}
	}

	return await resolve(event);
};

export const protectedRoutes: Handle = async ({ event, resolve }) => {
	const { pathname } = event.url;
	const user = event.locals.user;

	if (event.url.pathname.startsWith('/.well-known/')) {
		return new Response('', { status: 204 });
	}
	// Public routes
	if (pathname.startsWith('/login') || pathname.startsWith('/register') || pathname.startsWith('/forgot-password') || pathname.startsWith('/reset-password')) {
		if (user && user.plants.length > 0 && user.companyId) {
			// Get company and plant slugs for redirect
			const [company, mainPlant] = await Promise.all([
				CompanyService.getCompanyById(user.companyId),
				PlantsService.getPlantById(user.plants[0])
			]);

			if (company && mainPlant) {
				return redirect(302, `/${company.slug}/${mainPlant.slug}/dashboard`);
			}
		}
		return await resolve(event);
	}

	// Logout and other auth actions
	if (pathname.startsWith('/logout')) {
		return await resolve(event);
	}

	// Static assets, API routes, and DevTools requests
	if (
		pathname.startsWith('/api') ||
		pathname.startsWith('/_app') ||
		pathname.includes('favicon') ||
		pathname.startsWith('/.well-known/')
	) {
		return await resolve(event);
	}

	// All other routes require authentication
	if (!user) {
		return redirect(302, '/login');
	}

	// If user has no plants, redirect to login
	if (user.plants.length === 0) {
		return redirect(302, '/login');
	}

	return await resolve(event);
};

export const adminRoutes: Handle = async ({ event, resolve }) => {
	const adminPaths = ['/admin-panel'];

	const isAdminRoute = adminPaths.some((path) => event.url.pathname.startsWith(path));

	if (isAdminRoute) {
		const user = event.locals.user;

		if (!user) {
			return redirect(302, '/login');
		}

		const isInWhitelist = whitelist.canSeeAll.includes(user.email);

		if (!isInWhitelist) {
			// Get company and plant slugs for redirect
			const [company, mainPlant] = await Promise.all([
				CompanyService.getCompanyById(user.companyId),
				PlantsService.getPlantById(user.plants[0])
			]);

			if (company && mainPlant) {
				return redirect(302, `/${company.slug}/${mainPlant.slug}/dashboard`);
			}
			return redirect(302, '/login');
		}
	}

	return await resolve(event);
};

export const handle = sequence(authenticateUser, setPlantLanguage, protectedRoutes, adminRoutes);
