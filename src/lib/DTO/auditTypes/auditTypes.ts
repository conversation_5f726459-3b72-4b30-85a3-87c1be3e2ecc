import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export type AuditTypeDTO = {
	id: string;
	code: string;
	name: string;
	responsiblePerson: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	expectedDuration: number;
	repetetionPlan: string;
	specification?: string | null;
	active: boolean;
	evaluationMode: string | null;
	evaluation: {
		rules: {
			average_averageAuditAnswers: number | null;
			notSuccessful_averageAnswers: number | null;
			notSuccessful_badAnswers: number | null;
		};
		successThreshold: {
			auditThreshold_average: number | null;
			auditThreshold_success: number | null;
		};
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	questions?: TemplateQuestions | Record<string, any>;
};
