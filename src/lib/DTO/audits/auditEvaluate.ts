import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export type AuditEvaluateDTO = {
	id: string;
	code: string;
	workplace: {
		id: string;
		name: string;
	} | null;
	auditType: {
		id: string;
		name: string;
	} | null;
	responsiblePerson: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	auditTypeResponsiblePerson: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	workplaceResponsiblePerson: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	expectedDuration?: number;
	realDuration?: number;
	plannedEvaluationDate: string;
	completionDate?: string | null;
	questions: TemplateQuestions;
	progress: number;
};
