import type { PlantEvaluationConfig } from '$lib/db/schema/company';
import type { TemplateQuestion } from '$lib/schemas/audits/auditQuestions';

/**
 * Interface for attachment
 */
export interface AttachmentFile {
	id?: string; // Optional attachment ID for cache optimization
	url: string;
	filename: string;
	type: string;
	previewUrl?: string; // Optional preview URL for newly uploaded files
}

/**
 * Interface for answer to question
 */
export interface QuestionAnswer {
	evaluationValue: string | null;
	note: string;
	files: AttachmentFile[];
	questionId: string;
	auditId: string | null;
}

/**
 * Extended question type for audit
 */
export interface AuditQuestion extends TemplateQuestion {
	id?: string;
	text: string;
	subtext?: string | null;
	evaluationType: string;
}

/**
 * Interface for file validation
 */
export interface FileValidationResult {
	valid: boolean;
	message?: string;
}

/**
 * Interface for props AttachmentForm
 */
export interface AttachmentFormProps {
	question: AuditQuestion;
	files: AttachmentFile[];
	onFileAdd: (file: AttachmentFile) => void;
	onFileRemove: (index: number) => Promise<void>;
	trackUpload?: (uploadId: string) => void;
	untrackUpload?: (uploadId: string) => void;
}

/**
 * Interface for props QuestionForm
 */
export interface QuestionFormProps {
	question: AuditQuestion;
	selectedValue: string | null;
	note: string;
	plantEvaluationConfig: PlantEvaluationConfig;
	onAnswerChange: (value: string | null, note: string) => void;
}

/**
 * Auto-save status for UI feedback
 */
export type AutoSaveStatus = 'idle' | 'saving' | 'saved' | 'error' | 'retrying';
