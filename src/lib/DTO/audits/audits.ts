import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export type AuditDefaultValues = {
	auditTypeId: string;
	code: string;
	progress: number;
	questions: TemplateQuestions;
};

export type AuditFormDTO = {
	id?: string;
	auditTypeId: string;
	workplaceId: string;
	responsiblePersonId: string;
	plannedDate: string;
};

export function mapAuditToFormDTO(audit: AuditDTO): AuditFormDTO {
	return {
		id: audit.id,
		auditTypeId: audit.auditType?.id || '',
		workplaceId: audit.workplace?.id || '',
		responsiblePersonId: audit.responsiblePerson?.id || '',
		plannedDate: audit.plannedEvaluationDate || ''
	};
}

export type AuditDTO = {
	id?: string;
	auditType: {
		id: string;
		name: string;
		evaluationMode?: string | null;
	} | null;
	code: string;
	workplace: {
		id: string;
		name: string;
		code: string;
	} | null;
	responsiblePerson: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	createdBy: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	createdAt: Date | null;
	date?: string;
	expectedDuration?: number;
	realDuration?: number;
	plannedEvaluationDate: string;
	completionDate?: string | null;
	progress?: number;
	questions?: TemplateQuestions;
	successRate?: number | null;
	rulesResult?: string | null;
	auditInstanceConfig?: {
		auditThreshold_average?: number | null;
		auditThreshold_success?: number | null;
		average_averageAuditAnswers?: number | null;
		notSuccessful_averageAnswers?: number | null;
		notSuccessful_badAnswers?: number | null;
	} | null;
};

export type AuditListDTO = {
	id: string;
	code: string;
	auditType: {
		id: string;
		name: string;
		evaluationMode?: string | null;
	} | null;
	workplace: {
		id: string;
		name: string;
		code: string;
	} | null;
	auditor: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	createdBy: {
		id: string;
		firstName: string | null;
		lastName: string | null;
	} | null;
	createdAt: Date | null;
	plannedDate: string;
	completionDate: string | null;
	progress: number;
	successRate?: number | null;
	rulesResult?: string | null;
	auditInstanceConfig?: {
		auditThreshold_average?: number | null;
		auditThreshold_success?: number | null;
		average_averageAuditAnswers?: number | null;
		notSuccessful_averageAnswers?: number | null;
		notSuccessful_badAnswers?: number | null;
	} | null;
};

export function mapAuditToListType(audit: AuditDTO): AuditListDTO {
	return {
		id: audit.id || '',
		code: audit.code,
		auditType: audit.auditType
			? {
					id: audit.auditType.id,
					name: audit.auditType.name,
					evaluationMode: audit.auditType.evaluationMode || null
				}
			: null,
		workplace: audit.workplace
			? {
					id: audit.workplace.id,
					name: audit.workplace.name,
					code: audit.workplace.code || ''
				}
			: null,
		auditor: {
			id: audit.responsiblePerson?.id || '',
			firstName: audit.responsiblePerson?.firstName || null,
			lastName: audit.responsiblePerson?.lastName || null
		},
		createdBy: audit.createdBy,
		createdAt: audit.createdAt,
		plannedDate: audit.plannedEvaluationDate,
		completionDate: audit.completionDate || null,
		progress: audit.progress || 0,
		successRate: audit.successRate || null,
		rulesResult: audit.rulesResult || null,
		auditInstanceConfig: audit.auditInstanceConfig || null
	};
}

export function mapListTypeToAudit(auditList: AuditListDTO): AuditDTO {
	return {
		id: auditList.id,
		code: auditList.code,
		auditType: auditList.auditType,
		workplace: auditList.workplace,
		responsiblePerson: {
			id: auditList.auditor?.id || '',
			firstName: auditList.auditor?.firstName || null,
			lastName: auditList.auditor?.lastName || null
		},
		createdBy: auditList.createdBy,
		createdAt: auditList.createdAt,
		plannedEvaluationDate: auditList.plannedDate,
		completionDate: auditList.completionDate,
		progress: auditList.progress,
		successRate: auditList.successRate,
		rulesResult: auditList.rulesResult
	};
}
