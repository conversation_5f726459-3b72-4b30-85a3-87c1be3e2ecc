<script lang="ts">
	import {
		CalendarDate,
		getLocalTimeZone,
		startOfMonth,
		endOfMonth,
		isEqualDay,
		today
	} from '@internationalized/date';
	import type { DateValue } from '@internationalized/date';
	import ChevronLeft from '@lucide/svelte/icons/chevron-left';
	import ChevronRight from '@lucide/svelte/icons/chevron-right';
	import { locale } from '$lib/translations';
	import { cn } from '$lib/utils';

	let {
		value = $bindable(),
		class: className = '',
		onSelect
	} = $props<{
		value?: DateValue;
		class?: string;
		onSelect?: (date: DateValue) => void;
	}>();

	// Current displayed month/year
	let currentMonth = $state(value?.month || today(getLocalTimeZone()).month);
	let currentYear = $state(value?.year || today(getLocalTimeZone()).year);

	// Month/year navigation
	function previousMonth() {
		if (currentMonth === 1) {
			currentMonth = 12;
			currentYear--;
		} else {
			currentMonth--;
		}
	}

	function nextMonth() {
		if (currentMonth === 12) {
			currentMonth = 1;
			currentYear++;
		} else {
			currentMonth++;
		}
	}

	// Generate calendar data
	const calendarData = $derived.by(() => {
		const firstDayOfMonth = new CalendarDate(currentYear, currentMonth, 1);
		const lastDayOfMonth = endOfMonth(firstDayOfMonth);

		// Get first day of week (0 = Sunday, 1 = Monday, etc.)
		const firstDayWeekday = firstDayOfMonth.toDate(getLocalTimeZone()).getDay();
		// Convert to Monday = 0 format
		const startDay = firstDayWeekday === 0 ? 6 : firstDayWeekday - 1;

		const weeks: (CalendarDate | null)[][] = [];
		let currentWeek: (CalendarDate | null)[] = [];

		// Add empty cells for days before month starts
		for (let i = 0; i < startDay; i++) {
			currentWeek.push(null);
		}

		// Add all days of the month
		for (let day = 1; day <= lastDayOfMonth.day; day++) {
			if (currentWeek.length === 7) {
				weeks.push(currentWeek);
				currentWeek = [];
			}
			currentWeek.push(new CalendarDate(currentYear, currentMonth, day));
		}

		// Fill the last week with empty cells
		while (currentWeek.length < 7) {
			currentWeek.push(null);
		}
		weeks.push(currentWeek);

		return weeks;
	});

	// Month name
	const monthName = $derived.by(() => {
		const date = new Date(currentYear, currentMonth - 1, 1);
		return new Intl.DateTimeFormat($locale === 'cs' ? 'cs-CZ' : 'en-US', {
			month: 'long'
		}).format(date);
	});

	// Weekday names
	const weekdays = $derived.by(() => {
		const names = [];
		const date = new Date(2023, 0, 2); // Monday, January 2, 2023
		for (let i = 0; i < 7; i++) {
			names.push(
				new Intl.DateTimeFormat($locale === 'cs' ? 'cs-CZ' : 'en-US', {
					weekday: 'short'
				}).format(new Date(date.getTime() + i * 24 * 60 * 60 * 1000))
			);
		}
		return names;
	});

	function selectDate(date: CalendarDate) {
		if (!isPastDate(date)) {
			value = date;
			onSelect?.(date);
		}
	}

	function isSelected(date: CalendarDate) {
		return value && isEqualDay(value, date);
	}

	function isToday(date: CalendarDate) {
		return isEqualDay(date, today(getLocalTimeZone()));
	}

	function isPastDate(date: CalendarDate) {
		const todayDate = today(getLocalTimeZone());
		return date.compare(todayDate) < 0;
	}
</script>

<div class={cn('w-fit rounded-lg border bg-popover p-4 shadow-md', className)}>
	<!-- Header -->
	<div class="mb-4 flex items-center justify-between">
		<button
			type="button"
			onclick={previousMonth}
			class="inline-flex h-9 w-9 items-center justify-center rounded-md bg-transparent text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
		>
			<ChevronLeft class="h-4 w-4" />
		</button>

		<div class="text-sm font-semibold capitalize text-foreground">
			{monthName}
			{currentYear}
		</div>

		<button
			type="button"
			onclick={nextMonth}
			class="inline-flex h-9 w-9 items-center justify-center rounded-md bg-transparent text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
		>
			<ChevronRight class="h-4 w-4" />
		</button>
	</div>

	<!-- Weekday headers -->
	<div class="mb-3 grid grid-cols-7 gap-1">
		{#each weekdays as weekday}
			<div
				class="flex h-9 w-9 items-center justify-center text-xs font-medium uppercase text-muted-foreground"
			>
				{weekday.slice(0, 2)}
			</div>
		{/each}
	</div>

	<!-- Calendar grid -->
	<div class="grid grid-cols-7 gap-1">
		{#each calendarData as week}
			{#each week as date}
				{#if date}
					<button
						type="button"
						onclick={() => selectDate(date)}
						disabled={isPastDate(date)}
						class={cn(
							'relative flex h-9 w-9 items-center justify-center rounded-md p-0 text-sm font-normal outline-none transition-all focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2',
							isPastDate(date)
								? 'cursor-not-allowed opacity-50 text-muted-foreground'
								: isSelected(date)
									? 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90'
									: 'hover:bg-accent hover:text-accent-foreground',
							isToday(date) && !isSelected(date) && !isPastDate(date)
								? 'bg-accent text-accent-foreground font-medium ring-1 ring-primary/20'
								: ''
						)}
					>
						{date.day}
					</button>
				{:else}
					<div class="h-9 w-9"></div>
				{/if}
			{/each}
		{/each}
	</div>
</div>
