<script lang="ts" generics="TData, TValue">
	import {
		type ColumnDef,
		getCoreRowModel,
		getFilteredRowModel,
		getPaginationRowModel,
		getSortedRowModel,
		type PaginationState,
		type SortingState,
		type FilterFn,
		type ColumnSizingState
	} from '@tanstack/table-core';
	import { createSvelteTable, FlexRender } from '$lib/components/ui/data-table/index.js';
	import * as Table from '$lib/components/ui/table';
	import Button from '../button/button.svelte';
	import { ChevronLeft, ChevronRight } from '@lucide/svelte';

	type DataTableProps<TData, TValue> = {
		columns: ColumnDef<TData, TValue>[];
		data: TData[];
		globalFilter?: string;
		pageSize?: number;
		pagination?: boolean;
		rowSize?: 'normal' | 'compact' | 'large';
	};

	let {
		data,
		columns,
		globalFilter = '',
		pageSize = 8,
		pagination = true,
		rowSize = 'normal'
	}: DataTableProps<TData, TValue> = $props();

	let paginationState = $state<PaginationState>({ pageIndex: 0, pageSize });
	let sorting = $state<SortingState>([]);
	let columnSizing = $state<ColumnSizingState>({});

	$effect(() => {
		if (table) {
			table.setGlobalFilter(globalFilter);
		}
	});

	const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
		if (!value) return true;
		const searchValue = value.toLowerCase();

		if (columnId === 'actions') return true;

		if (columnId === 'workplace') {
			const workplace = row.original.workplace;
			if (!workplace) return false;
			const result = workplace.name.toLowerCase().includes(searchValue);

			return result;
		}

		const cellValue = row.getValue(columnId);
		if (cellValue === null || cellValue === undefined) return false;
		return String(cellValue).toLowerCase().includes(searchValue);
	};

	const table = createSvelteTable({
		get data() {
			return data;
		},
		columns,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: pagination ? getPaginationRowModel() : undefined,
		getFilteredRowModel: getFilteredRowModel(),
		getSortedRowModel: getSortedRowModel(),
		state: {
			get sorting() {
				return sorting;
			},
			get pagination() {
				return paginationState;
			},
			get globalFilter() {
				return globalFilter;
			},
			get columnSizing() {
				return columnSizing;
			}
		},
		onSortingChange: (updater) => {
			if (typeof updater === 'function') {
				sorting = updater(sorting);
			} else {
				sorting = updater;
			}
		},
		onPaginationChange: (updater) => {
			if (typeof updater === 'function') {
				paginationState = updater(paginationState);
			} else {
				paginationState = updater;
			}
		},
		onGlobalFilterChange: (updater) => {
			if (typeof updater === 'function') {
				globalFilter = updater(globalFilter);
			} else {
				globalFilter = updater;
			}
		},
		globalFilterFn: fuzzyFilter
	});

	function generatePageNumbers(currentPage: number, totalPages: number) {
		const numbers = [];

		if (totalPages <= 5) {
			for (let i = 1; i <= totalPages; i++) {
				numbers.push(i);
			}
			return numbers;
		}

		numbers.push(1);

		if (currentPage <= 2) {
			numbers.push(2, 3);
			numbers.push('...');
			numbers.push(totalPages);
			return numbers;
		}

		if (currentPage >= totalPages - 1) {
			numbers.push('...');
			numbers.push(totalPages - 2, totalPages - 1, totalPages);
			return numbers;
		}

		numbers.push('...');
		numbers.push(currentPage);
		numbers.push('...');
		numbers.push(totalPages);

		return numbers;
	}
</script>

<div class="flex h-full flex-col">
	<div class="flex-1 overflow-auto">
		<Table.Root>
			<Table.Header class="sticky top-0 z-10 bg-white [&_tr]:border-b-0">
				{#each table.getHeaderGroups() as headerGroup}
					<Table.Row>
						{#each headerGroup.headers as header}
							<Table.Head class={(header.column.columnDef.meta as any)?.headerClass || ''}>
								{#if !header.isPlaceholder}
									<FlexRender
										content={header.column.columnDef.header}
										context={header.getContext()}
									/>
								{/if}
							</Table.Head>
						{/each}
					</Table.Row>
				{/each}
			</Table.Header>
			<Table.Body>
				{#each table.getRowModel().rows as row}
					<Table.Row
						data-state={row.getIsSelected() && 'selected'}
						class={rowSize === 'compact' ? 'h-8' : rowSize === 'large' ? 'h-16' : 'h-12'}
					>
						{#each row.getVisibleCells() as cell}
							<Table.Cell
								class={`${rowSize === 'compact' ? '' : 'py-4'} ${(cell.column.columnDef.meta as any)?.cellClass || ''}`}
							>
								<FlexRender content={cell.column.columnDef.cell} context={cell.getContext()} />
							</Table.Cell>
						{/each}
					</Table.Row>
				{/each}
			</Table.Body>
		</Table.Root>
	</div>

	{#if pagination}
		<div
			class="mt-2 flex shrink-0 justify-end gap-2 px-2 py-2 md:mt-4 md:justify-end md:gap-4 md:px-0 md:py-0"
		>
			<div class="flex items-center">
				<Button
					variant="ghost"
					class="text-secondary h-8 w-8 rounded-full p-0 [&_svg]:size-8 {table.getCanPreviousPage()
						? ''
						: 'opacity-50'}"
					disabled={!table.getCanPreviousPage()}
					onclick={() => table.previousPage()}
				>
					<ChevronLeft class="h-4 w-4" />
				</Button>

				{#each generatePageNumbers(table.getState().pagination.pageIndex + 1, table.getPageCount()) as pageNumber}
					{#if pageNumber === '...'}
						<span
							class="text-muted-foreground flex h-8 w-8 items-center justify-center text-sm select-none"
						>
							&#8230;
						</span>
					{:else}
						<Button
							variant={pageNumber === table.getState().pagination.pageIndex + 1
								? 'default'
								: 'ghost'}
							class="h-8 w-8 rounded-full p-0  {pageNumber ===
							table.getState().pagination.pageIndex + 1
								? 'bg-secondary hover:bg-secondary/80'
								: 'font-titillium hover:bg-primary text-[#6A6A70] hover:text-white'}"
							onclick={() => table.setPageIndex(Number(pageNumber) - 1)}
						>
							{pageNumber}
						</Button>
					{/if}
				{/each}

				<Button
					variant="ghost"
					class="text-secondary h-8 w-8 rounded-full p-0 [&_svg]:size-8 {table.getCanNextPage()
						? ''
						: 'opacity-50'}"
					disabled={!table.getCanNextPage()}
					onclick={() => table.nextPage()}
				>
					<ChevronRight class="h-4 w-4" />
				</Button>
			</div>
		</div>
	{/if}
</div>
