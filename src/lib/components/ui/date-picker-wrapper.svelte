<script lang="ts">
	import CalendarIcon from '@lucide/svelte/icons/calendar';
	import * as Popover from '$lib/components/ui/popover';
	import { Button } from '$lib/components/ui/button';
	import CustomCalendar from '$lib/components/ui/custom-calendar/custom-calendar.svelte';
	import {
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		CalendarDate,
		CalendarDateTime,
		ZonedDateTime
	} from '@internationalized/date';
	import { locale } from '$lib/translations';
	import { cn } from '$lib/utils';

	let {
		value = $bindable(),
		onValueChange,
		class: className = '',
		placeholder = 'Vyberte datum'
	} = $props<{
		value?: DateValue;
		onValueChange: (value: DateValue | undefined) => void;
		class?: string;
		placeholder?: string;
	}>();

	let popoverOpen = $state(false);

	// Derive formatted display value using $derived
	const displayValue = $derived.by(() => {
		if (value) {
			const df = new DateFormatter($locale === 'cs' ? 'cs-CZ' : 'en-US', {
				dateStyle: 'medium'
			});
			return df.format(value.toDate(getLocalTimeZone()));
		}
		return placeholder;
	});

	function handleDateSelect(newDate: DateValue) {
		value = newDate;
		onValueChange(newDate);
		popoverOpen = false;
	}
</script>

<Popover.Root bind:open={popoverOpen}>
	<Popover.Trigger
		class={cn(
			'focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50',
			'w-full justify-start text-left font-normal',
			!value && 'text-muted-foreground',
			className
		)}
	>
		<CalendarIcon class="mr-2 h-4 w-4" />
		{displayValue}
	</Popover.Trigger>
	<Popover.Content class="w-auto p-0" align="start">
		<CustomCalendar {value} onSelect={handleDateSelect} />
	</Popover.Content>
</Popover.Root>
