<script lang="ts">
	import { Progress as ProgressPrimitive, type WithoutChildrenOrChild } from 'bits-ui';
	import { cn } from '$lib/utils.js';

	let {
		ref = $bindable(null),
		class: className,
		indicatorClass = '',
		max = 100,
		value,
		showLabel = false,
		...restProps
	}: WithoutChildrenOrChild<ProgressPrimitive.RootProps> & {
		indicatorClass?: string;
		showLabel?: boolean;
	} = $props();

	let percentage = $derived(Math.round(value ?? 0));
</script>

<ProgressPrimitive.Root
	bind:ref
	class={cn('relative h-4 w-full overflow-hidden rounded-full', className)}
	{value}
	{max}
	{...restProps}
>
	<div
		class={cn('relative h-full flex-1 rounded-md transition-all', indicatorClass)}
		style="width: {(100 * (value ?? 0)) / (max ?? 1)}%"
	>
		{#if showLabel && percentage > 0}
			<div class="absolute inset-0 flex items-center justify-center">
				<span class="text-sm font-semibold text-white select-none lg:text-xs lg:font-medium">
					{percentage}%
				</span>
			</div>
		{/if}
	</div>
</ProgressPrimitive.Root>
