const auditsAvailablePermissions = {
	name: 'Audits',
	code: 'audits',
	availableActions: [
		'viewAudits',
		'createAudit',
		'editAudit',
		'deleteAudit',
		'evaluateAudit',
		'viewAuditResults',
		'exportAudit'
	]
} as const;

const workplacesAvailablePermissions = {
	name: 'Workplaces',
	code: 'workplaces',
	availableActions: [
		'viewWorkplaces',
		'createWorkplace',
		'editWorkplace',
		'deleteWorkplace',
		'toggleActiveWorkplace',
		'viewWorkplaceDetails'
	]
} as const;

const checkpointsAvailablePermissions = {
	name: 'Checkpoints',
	code: 'checkpoints',
	availableActions: ['viewCheckpoints', 'createCheckpoint', 'editCheckpoint', 'deleteCheckpoint']
} as const;

const plantSettingsAvailablePermissions = {
	name: 'Plant Settings ',
	code: 'plantSettings',
	availableActions: ['viewPlantSettings', 'editPlantSettings']
} as const;

const plantEvalTypesAvailablePermissions = {
	name: 'Plant Evaluation Types',
	code: 'plantEvalTypes',
	availableActions: ['viewPlantEvalTypes', 'editPlantEvalTypes']
} as const;

const auditTypesAvailablePermissions = {
	name: 'Audit Types',
	code: 'auditTypes',
	availableActions: [
		'viewTemplates',
		'createTemplate',
		'editTemplate',
		'editTemplateSettings',
		'deleteTemplate',
		'duplicateTemplate',
		'createCategory',
		'editCategory',
		'deleteCategory',
		'createQuestion',
		'editQuestion',
		'deleteQuestion',
		'toggleRequiredQuestion',
		'reorderCategories',
		'reorderQuestions'
	]
} as const;

const tagsAvailablePermissions = {
	name: 'Tags',
	code: 'tags',
	availableActions: ['viewTags', 'createTag', 'editTag', 'deleteTag']
} as const;

export const availablePermissions = [
	auditsAvailablePermissions,
	workplacesAvailablePermissions,
	checkpointsAvailablePermissions,
	plantSettingsAvailablePermissions,
	plantEvalTypesAvailablePermissions,
	auditTypesAvailablePermissions,
	tagsAvailablePermissions
] as const;
