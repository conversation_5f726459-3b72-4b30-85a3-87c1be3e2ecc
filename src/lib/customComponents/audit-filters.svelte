<script lang="ts">
	import ComboboxFilter from './combobox-filter.svelte';
	import { Label } from '$lib/components/ui/label';
	import { t, locale } from '$lib/translations';
	import { cn } from '$lib/utils';
	import { slide, fade } from 'svelte/transition';
	import CalendarIcon from '@lucide/svelte/icons/calendar';
	import type { DateRange } from 'bits-ui';
	import {
		CalendarDate,
		DateFormatter,
		type DateValue,
		getLocalTimeZone
	} from '@internationalized/date';
	import { RangeCalendar } from '$lib/components/ui/range-calendar/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import Button from '$lib/components/ui/button/button.svelte';

	interface FilterOption {
		id: string;
		name: string;
	}

	interface AuditData {
		id: string;
		progress: number;
		completionDate: string | null;
		plannedDate: string;
	}

	interface AuditFilters {
		auditTypes: string[];
		workplaces: string[];
		auditors: string[];
		statuses: string[];
		plannedDateRange: DateRange | undefined;
		completionDateRange: DateRange | undefined;
	}

	let {
		auditTypes = [],
		workplaces = [],
		auditors = [],
		audits = [],
		filters = $bindable({
			auditTypes: [],
			workplaces: [],
			auditors: [],
			statuses: [],
			plannedDateRange: undefined,
			completionDateRange: undefined
		}),
		onFiltersChange = () => {},
		class: className = ''
	}: {
		auditTypes?: FilterOption[];
		workplaces?: FilterOption[];
		auditors?: FilterOption[];
		audits?: AuditData[];
		filters?: AuditFilters;
		onFiltersChange?: (filters: AuditFilters) => void;
		class?: string;
	} = $props();

	const df = $derived(
		new DateFormatter($locale || 'en', {
			dateStyle: 'medium'
		})
	);

	let startValue: DateValue | undefined = $state(undefined);
	let startValueCompletion: DateValue | undefined = $state(undefined);

	const getAuditStatus = (audit: AuditData): string => {
		if (audit.progress === -1) return 'late';
		if (audit.progress === 100 && audit.completionDate) return 'closed';
		if (audit.progress === 100) return 'completed';
		if (audit.progress > 0) return 'inProgress';
		return 'planned';
	};

	const statusOptions = $derived(() => {
		if (!audits?.length) return [];

		const statusOrder = ['planned', 'inProgress', 'completed', 'closed', 'late'];
		const availableStatuses = new Set(audits.map(getAuditStatus));

		return statusOrder
			.filter((status) => availableStatuses.has(status))
			.map((status) => ({
				id: status,
				name:
					status === 'inProgress'
						? $t('audits.progress.inProgressSimple')
						: $t(`audits.progress.${status}`)
			}));
	});
</script>

<div class={cn('w-full', className)} transition:slide|global>
	<div
		class="grid grid-cols-1 gap-6 p-4 transition-all duration-200 ease-in-out md:grid-cols-2 lg:grid-cols-6"
	>
		{#if auditTypes.length > 0}
			<ComboboxFilter
				label={$t('audits.auditList.auditType')}
				emptyText={$t('audits.newInstance.noAuditTypeFound')}
				options={auditTypes}
				bind:selectedValues={filters.auditTypes}
				onSelectionChange={(values) => {
					filters.auditTypes = values;
					onFiltersChange(filters);
				}}
			/>
		{/if}

		<!-- Workplaces Filter -->
		{#if workplaces.length > 0}
			<ComboboxFilter
				label={$t('audits.auditList.workplace')}
				emptyText={$t('audits.newInstance.noWorkplaceFound')}
				options={workplaces}
				bind:selectedValues={filters.workplaces}
				onSelectionChange={(values) => {
					filters.workplaces = values;
					onFiltersChange(filters);
				}}
			/>
		{/if}

		<!-- Auditors Filter -->
		{#if auditors.length > 0}
			<ComboboxFilter
				label={$t('audits.auditList.auditor')}
				emptyText={$t('audits.newInstance.noAuditorFound')}
				options={auditors}
				bind:selectedValues={filters.auditors}
				onSelectionChange={(values) => {
					filters.auditors = values;
					onFiltersChange(filters);
				}}
			/>
		{/if}

		<!-- Status Filter -->
		<ComboboxFilter
			label={$t('audits.auditList.progress')}
			options={statusOptions()}
			bind:selectedValues={filters.statuses}
			onSelectionChange={(values) => {
				filters.statuses = values;
				onFiltersChange(filters);
			}}
		/>
		<!-- Planned Date Range Filter -->
		<div class="space-y-2">
			<Label class="text-sm font-medium">{$t('audits.auditList.plannedDateRange')}</Label>
			<Popover.Root>
				<Popover.Trigger
					class={cn(
						'border-input ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full cursor-pointer items-center rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
						'justify-start text-left font-normal',
						!filters.plannedDateRange && 'text-muted-foreground'
					)}
				>
					<CalendarIcon class="mr-2 size-4" />
					{#if filters.plannedDateRange && filters.plannedDateRange.start}
						{#if filters.plannedDateRange.end}
							{df.format(filters.plannedDateRange.start.toDate(getLocalTimeZone()))} - {df.format(
								filters.plannedDateRange.end.toDate(getLocalTimeZone())
							)}
						{:else}
							{df.format(filters.plannedDateRange.start.toDate(getLocalTimeZone()))}
						{/if}
					{:else if startValue}
						{df.format(startValue.toDate(getLocalTimeZone()))}
					{/if}
				</Popover.Trigger>
				<Popover.Content class="w-auto p-0" align="start">
					<RangeCalendar
						bind:value={filters.plannedDateRange}
						onStartValueChange={(v) => {
							startValue = v;
						}}
						onValueChange={(value) => {
							filters.plannedDateRange = value;
							onFiltersChange(filters);
						}}
						numberOfMonths={2}
					/>
					<div class="border-t p-3">
						<Button
							type="button"
							class="w-full uppercase"
							onclick={() => {
								filters.plannedDateRange = undefined;
								startValue = undefined;
								onFiltersChange(filters);
							}}
						>
							{$t('common.clear')}
						</Button>
					</div>
				</Popover.Content>
			</Popover.Root>
		</div>

		<!-- Completion Date Range Filter -->
		<div class="space-y-2">
			<Label class="text-sm font-medium">{$t('audits.auditList.completionDateRange')}</Label>
			<Popover.Root>
				<Popover.Trigger
					class={cn(
						'border-input ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full cursor-pointer items-center rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
						'justify-start text-left font-normal',
						!filters.completionDateRange && 'text-muted-foreground'
					)}
				>
					<CalendarIcon class="mr-2 size-4" />
					{#if filters.completionDateRange && filters.completionDateRange.start}
						{#if filters.completionDateRange.end}
							{df.format(filters.completionDateRange.start.toDate(getLocalTimeZone()))} - {df.format(
								filters.completionDateRange.end.toDate(getLocalTimeZone())
							)}
						{:else}
							{df.format(filters.completionDateRange.start.toDate(getLocalTimeZone()))}
						{/if}
					{:else if startValueCompletion}
						{df.format(startValueCompletion.toDate(getLocalTimeZone()))}
					{/if}
				</Popover.Trigger>
				<Popover.Content class="w-auto p-0" align="start">
					<RangeCalendar
						bind:value={filters.completionDateRange}
						onStartValueChange={(v) => {
							startValueCompletion = v;
						}}
						onValueChange={(value) => {
							filters.completionDateRange = value;
							onFiltersChange(filters);
						}}
						numberOfMonths={2}
					/>
					<div class="border-t p-3">
						<Button
							type="button"
							class="w-full uppercase"
							onclick={() => {
								filters.completionDateRange = undefined;
								startValueCompletion = undefined;
								onFiltersChange(filters);
							}}
						>
							{$t('common.clear')}
						</Button>
					</div>
				</Popover.Content>
			</Popover.Root>
		</div>
	</div>
</div>
