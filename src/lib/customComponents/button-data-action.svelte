<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { Component } from 'svelte';

	let {
		Icon,
		onClickAction,
		size = 'icon',
		srText = '',
		bgColor = 'bg-primary',
		hoverBgColor = 'hover:bg-primary/80',
		class: className = '',
		type = 'button'
	}: {
		Icon: Component;
		onClickAction: (() => void) | ((e: MouseEvent) => void);
		size?: 'sm' | 'default' | 'lg' | 'icon';
		srText?: string;
		bgColor?: string;
		hoverBgColor?: string;
		class?: string;
		type?: 'button' | 'submit' | 'reset';
	} = $props();
</script>

<Button
	variant="default"
	{size}
	class="h-8 w-8 rounded-lg {bgColor} {hoverBgColor} p-1.5 text-white hover:text-white {className}"
	{type}
	onclick={(e) => {
		onClickAction(e);
	}}
>
	<Icon class="h-4 w-4" />
	{#if srText}
		<span class="sr-only">{srText}</span>
	{/if}
</Button>
