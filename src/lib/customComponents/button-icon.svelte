<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { type Component } from 'svelte';
	import { cn } from '$lib/utils';

	let {
		onClickAction = () => {},
		textColor,
		backgroundColor = '#7D9AD3',
		hoverTextColor = 'white',
		hoverBackgroundColor = '#7D9AD3',
		hoverOpacity = '80',
		iconSize = 6,
		class: className = '',
		Icon,
		children
	}: {
		onClickAction?: () => void;
		textColor?: string;
		backgroundColor?: string;
		hoverTextColor?: string;
		hoverBackgroundColor?: string;
		hoverOpacity?: string;
		iconSize?: number;
		class?: string;
		Icon?: Component;
		children?: any;
	} = $props();

	let isHovered = $state(false);

	function handleMouseEnter() {
		isHovered = true;
	}

	function handleMouseLeave() {
		isHovered = false;
	}

	let buttonStyle = $derived(
		isHovered
			? `background-color: ${hoverBackgroundColor}; opacity: ${parseInt(hoverOpacity) / 100}; color: ${hoverTextColor};`
			: `background-color: ${backgroundColor}; color: ${textColor || 'white'};`
	);
</script>

<Button
	onclick={onClickAction}
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
	style={buttonStyle}
	class={cn(
		'flex size-12 cursor-pointer items-center justify-center rounded-xl transition-colors duration-200',
		className
	)}
>
	{#if children}
		{@render children()}
	{:else if Icon}
		<Icon class={cn(`size-${iconSize}`)} />
	{/if}
</Button>
