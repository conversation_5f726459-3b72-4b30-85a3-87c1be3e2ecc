<script lang="ts">
	import { Combobox } from 'bits-ui';
	import { Label } from '$lib/components/ui/label';
	import CheckIcon from '@lucide/svelte/icons/check';
	import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
	import { t } from '$lib/translations';

	interface FilterOption {
		id: string;
		name: string;
	}

	let {
		label,
		placeholder = '',
		emptyText = '',
		options = [],
		selectedValues = $bindable([]),
		onSelectionChange,
		class: className = ''
	}: {
		label: string;
		placeholder?: string;
		emptyText?: string;
		options: FilterOption[];
		selectedValues: string[];
		onSelectionChange?: (values: string[]) => void;
		class?: string;
	} = $props();

	let searchValue = $state('');
	let displayValue = $state('');

	$effect(() => {
		if (selectedValues.length === 0) {
			displayValue = placeholder;
		} else if (selectedValues.length === 1) {
			displayValue = options.find((opt) => opt.id === selectedValues[0])?.name || placeholder;
		} else {
			const count = selectedValues.length;
			const pluralKey = count === 1 ? 'one' : count >= 2 && count <= 4 ? 'few' : 'many';
			const selectedText = $t(`common.filters.selected.${pluralKey}`);
			displayValue = `${count} ${selectedText}`;
		}
	});

	const filteredOptions = $derived(
		searchValue === ''
			? options
			: options.filter((option) => option.name.toLowerCase().includes(searchValue.toLowerCase()))
	);
</script>

<div class="space-y-2 {className}">
	<Label class="text-sm font-medium">{label}</Label>
	{#if options.length > 0}
		<Combobox.Root
			type="multiple"
			bind:value={selectedValues}
			inputValue={searchValue !== '' ? searchValue : displayValue}
			onValueChange={(values) => {
				selectedValues = values || [];
				onSelectionChange?.(selectedValues);
				searchValue = '';
			}}
			onOpenChange={(o) => {
				if (!o) {
					searchValue = '';
				}
			}}
		>
			<Combobox.Trigger class="relative w-full">
				<Combobox.Input
					class="border-input  ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full cursor-pointer rounded-md border px-3 py-2 pr-10 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
					{placeholder}
					aria-label={label}
					oninput={(e) => {
						searchValue = e.currentTarget.value;
					}}
				/>
				<div class="pointer-events-none absolute end-3 top-1/2 size-4 -translate-y-1/2">
					<ChevronsUpDownIcon class="text-muted-foreground size-4" />
				</div>
			</Combobox.Trigger>
			<Combobox.Portal>
				<Combobox.Content
					class="bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 w-[var(--bits-combobox-anchor-width)] min-w-[8rem] overflow-hidden rounded-md border shadow-md"
					sideOffset={4}
				>
					<Combobox.Viewport class="max-h-[200px] overflow-y-auto p-1">
						{#each filteredOptions as option}
							<Combobox.Item
								class="data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground relative flex w-full cursor-pointer items-center justify-between rounded-sm py-1.5 pr-8 pl-2 text-sm outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
								value={option.id}
								label={option.name}
							>
								{#snippet children({ selected })}
									{option.name}
									{#if selected}
										<span class="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
											<CheckIcon class="h-4 w-4" />
										</span>
									{/if}
								{/snippet}
							</Combobox.Item>
						{:else}
							<span class="block px-2 py-1.5 text-sm text-muted-foreground">
								{emptyText}
							</span>
						{/each}
					</Combobox.Viewport>
				</Combobox.Content>
			</Combobox.Portal>
		</Combobox.Root>
	{:else}
		<div
			class="text-muted-foreground border-input bg-background flex h-10 items-center rounded-md border px-3 py-2 text-sm"
		>
			{emptyText}
		</div>
	{/if}
</div>
