<script lang="ts">
	import { Dialog as DialogPrimitive } from 'bits-ui';
	import { X } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { t } from '$lib/translations';
	import { Button } from '$lib/components/ui/button';

	let {
		open = $bindable(false),
		title,
		description,
		confirmButtonText,
		closeButtonText = $t('common.buttons.close'),
		onClose,
		onConfirm,
		width = 'max-w-lg'
	} = $props<{
		open: boolean;
		title: string;
		description: string;
		confirmButtonText: string;
		closeButtonText?: string;
		onClose?: () => void;
		onConfirm?: () => void;
		width?: string;
	}>();
</script>

<DialogPrimitive.Root bind:open onOpenChange={(open) => !open && onClose?.()}>
	<DialogPrimitive.Portal>
		<DialogPrimitive.Overlay
			class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80"
		/>
		<DialogPrimitive.Content
			class={cn(
				'animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 fixed top-1/2 left-1/2 z-50 w-[95%] -translate-x-1/2 -translate-y-1/2 rounded-[1.25rem] bg-white shadow-xl transition-[height,transform] duration-200 ease-out sm:w-[90%]',
				width,
				'max-h-[95vh] sm:max-h-[90vh]'
			)}
		>
			<div
				class="rounded-t-[1.12rem] bg-gradient-to-r from-[#101826] via-[#536D9A] via-65% to-[#101826] sm:rounded-t-[1.12rem]"
			>
				<div class="flex items-center justify-between p-4 sm:p-6">
					<DialogPrimitive.Title>
						<div>
							<h2 class="text-lg text-white">{title}</h2>
						</div></DialogPrimitive.Title
					>
					<DialogPrimitive.Close
						class="rounded-sm bg-gray-600/50 p-1 opacity-90 transition-all duration-200 ease-out
                    hover:bg-gray-600/60 hover:opacity-100 hover:shadow-sm"
					>
						<X class="h-6 w-6  text-white transition-transform duration-200 hover:scale-105" />
						<span class="sr-only">{$t('common.buttons.close')}</span>
					</DialogPrimitive.Close>
				</div>
			</div>

			<div class="flex flex-col justify-center p-4 pt-0 sm:p-7">
				<div
					class="my-4 pb-2 text-center text-sm leading-tight whitespace-pre-line text-[#747C8A] sm:my-4 sm:pb-2 sm:text-base"
				>
					{description}
				</div>

				<div class="mt-2 flex flex-col gap-3 sm:flex-row sm:justify-end sm:gap-5">
					<div class="order-1 flex">
						<Button
							type="button"
							class="bg-secondary font-titillium hover:bg-secondary/80 w-full rounded-xl px-5 py-7 text-sm font-semibold text-white uppercase hover:text-white sm:min-w-[120px] sm:p-7 sm:text-sm"
							onclick={onClose}
						>
							{closeButtonText}
						</Button>
					</div>

					<div class="order-2 flex">
						<Button
							type="button"
							onclick={onConfirm}
							class="font-titillium w-full rounded-xl bg-[#2E384F] px-5 py-7 text-sm font-semibold uppercase hover:bg-[#2E384F]/80 sm:min-w-[120px] sm:p-7 sm:text-sm"
						>
							{confirmButtonText}
						</Button>
					</div>
				</div>
			</div>
		</DialogPrimitive.Content>
	</DialogPrimitive.Portal>
</DialogPrimitive.Root>
