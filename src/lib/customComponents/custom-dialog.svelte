<script lang="ts">
	import { Dialog as DialogPrimitive } from 'bits-ui';
	import { X } from '@lucide/svelte';
	import type { Snippet } from 'svelte';
	import { cn } from '$lib/utils';

	let {
		open,
		title,
		subtitle,
		children,
		onClose,
		width = 'max-w-lg'
	} = $props<{
		open: boolean;
		title: string;
		subtitle?: string;
		children: Snippet;
		onClose?: () => void;
		width?: string;
	}>();
</script>

<DialogPrimitive.Root bind:open onOpenChange={(open) => !open && onClose?.()}>
	<DialogPrimitive.Portal>
		<DialogPrimitive.Overlay
			class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80"
		/>
		<DialogPrimitive.Content
			class={cn(
				'animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 fixed top-1/2 left-1/2 z-50 flex w-[90%] -translate-x-1/2 -translate-y-1/2 flex-col rounded-[1.25rem] bg-white shadow-xl transition-[height,transform] duration-200 ease-out',
				width,
				'max-h-[95vh] sm:max-h-[90vh]'
			)}
		>
			<div
				class="shrink-0 rounded-t-[1.12rem] bg-gradient-to-r from-[#101826] via-[#536D9A] via-65% to-[#101826]"
			>
				<div class="flex items-center justify-between p-4 sm:p-6">
					<DialogPrimitive.Title>
						<div>
							<h2 class="text-lg text-white">{title}</h2>
							{#if subtitle}
								<p class="-mt-1 text-sm text-[#B1B7C3]">{subtitle}</p>
							{/if}
						</div></DialogPrimitive.Title
					>
					<DialogPrimitive.Close class="cursor-pointer rounded-sm bg-gray-600/50 p-1">
						<X class="h-6 w-6 text-white" />
					</DialogPrimitive.Close>
				</div>
			</div>

			<div class="min-h-0 flex-1 overflow-y-auto p-6">
				{@render children()}
			</div>
		</DialogPrimitive.Content>
	</DialogPrimitive.Portal>
</DialogPrimitive.Root>
