<script lang="ts">
	import Button from '$lib/components/ui/button/button.svelte';
	import { ChevronDown, ChevronsUpDown, ChevronUp } from '@lucide/svelte';

	let { text, column } = $props<{ text: string; column?: any }>();

	let sortable = $derived(column?.getCanSort());
	let sortDirection = $derived(column?.getIsSorted());

	function toggleSort() {
		if (!sortDirection) {
			column?.toggleSorting(false);
		} else if (sortDirection === 'asc') {
			column?.toggleSorting(true);
		} else {
			column?.clearSorting();
		}
	}
</script>

{#if sortable}
	<Button
		variant="ghost"
		class="hover:text-foreground/80 mb-4 inline-flex items-center gap-2 px-2 py-1"
		onclick={toggleSort}
	>
		<span class="text-base">{text}</span>
		<div class="relative flex size-4 items-center justify-center">
			<div class="relative size-4">
				<ChevronUp
					class=" absolute size-4 transition-all duration-200 {sortDirection === 'asc'
						? 'opacity-100'
						: 'opacity-0'}"
				/>
				<ChevronDown
					class=" absolute size-4 transition-all duration-200 {sortDirection === 'desc'
						? 'opacity-100'
						: 'opacity-0'}"
				/>
				<ChevronsUpDown
					class="text-secondary absolute size-4 transition-all duration-200 {!sortDirection
						? 'opacity-100'
						: 'opacity-0'}"
				/>
			</div>
		</div>
	</Button>
{:else}
	<i>{text}</i>
{/if}
