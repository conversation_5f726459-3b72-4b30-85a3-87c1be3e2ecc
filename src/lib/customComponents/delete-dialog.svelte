<script lang="ts">
	import { Dialog as DialogPrimitive } from 'bits-ui';
	import Button from '$lib/components/ui/button/button.svelte';
	import { enhance } from '$app/forms';
	import { t } from '$lib/translations';
	import { X } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';

	let { open, id, title, description, code, formAction, onDelete, onClose, successToast } = $props<{
		open: boolean;
		id: string;
		title: string;
		description: string;
		code?: string;
		formAction: string;
		onDelete: () => void;
		onClose: () => void;
		successToast?: string;
	}>();
</script>

<DialogPrimitive.Root bind:open onOpenChange={(open) => !open && onClose()}>
	<DialogPrimitive.Portal>
		<DialogPrimitive.Overlay
			class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80"
		/>
		<DialogPrimitive.Content
			class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-1/2 left-1/2 z-50 w-[90%] max-w-lg -translate-x-1/2 -translate-y-1/2 rounded-[1.25rem] bg-white shadow-xl transition-[height,transform] duration-200 ease-out "
		>
			<div
				class="rounded-t-[1.12rem] bg-linear-to-r from-[#101826] via-[#536D9A] via-65% to-[#101826]"
			>
				<div class="flex items-center justify-between p-4 sm:p-6">
					<div>
						<h2 class="text-lg text-white">{title}</h2>
						{#if code}
							<p class="text-secondary/70 -mt-2 text-sm">{code}</p>
						{/if}
					</div>

					<div class="flex items-center gap-2">
						<DialogPrimitive.Close
							class="rounded-sm bg-gray-600/50 p-1 opacity-90 transition-all duration-200 ease-out
                            hover:bg-gray-600/60 hover:opacity-100 hover:shadow-sm"
						>
							<X class="h-6 w-6 text-white transition-transform duration-200 hover:scale-105" />
							<span class="sr-only">{$t('common.buttons.close')}</span>
						</DialogPrimitive.Close>
					</div>
				</div>
			</div>

			<form
				method="POST"
				action={formAction}
				use:enhance={() => {
					return ({ result, update }) => {
						update({ reset: false });

						if (result.type === 'success') {
							if (successToast) {
								toast.success(successToast);
							}
							onDelete();
							onClose();
						} else if (result.type === 'failure') {
							// Special handling for question used in audit types (status 409)
							if (result.status === 409 && result.data?.isUsed) {
								const auditTypeNames = result.data.auditTypeNames as string;
								const message = $t('questions.delete.usedInAuditTypes').replace(
									'{auditTypes}',
									auditTypeNames
								);
								toast.error(message);
							} else if (result.data?.message) {
								toast.error(result.data.message as string);
							}
						}
					};
				}}
			>
				<input type="hidden" name="id" value={id} />
				<div class="flex flex-col justify-center p-4 pt-0 sm:p-7">
					<div class="my-4 pb-2 text-center leading-tight whitespace-pre-line text-[#747C8A]">
						{description}
					</div>

					<div class="mt-2 flex justify-end gap-5">
						<div class="flex">
							<Button
								type="button"
								class="bg-secondary font-titillium hover:bg-secondary/80 min-w-[120px] cursor-pointer rounded-xl p-7 text-sm font-semibold text-white uppercase hover:text-white"
								onclick={onClose}
							>
								{$t('common.buttons.cancel')}
							</Button>
						</div>

						<div class="flex">
							<Button
								type="submit"
								class="font-titillium min-w-[120px] rounded-xl bg-[#C01F44] p-7 text-sm font-semibold uppercase hover:bg-[#C01F44]/80"
							>
								{$t('common.buttons.delete')}
							</Button>
						</div>
					</div>
				</div>
			</form>
		</DialogPrimitive.Content>
	</DialogPrimitive.Portal>
</DialogPrimitive.Root>
