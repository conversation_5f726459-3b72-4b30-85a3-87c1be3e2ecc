<script lang="ts">
	import Button from '$lib/components/ui/button/button.svelte';
	import * as Form from '$lib/components/ui/form/index';
	import { Input } from '$lib/components/ui/input';
	import { changePasswordSchema } from '$lib/schemas/auth';
	import { useForm } from '$lib/hooks/superformValidation';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import * as Alert from '$lib/components/ui/alert';
	import { page } from '$app/state';
	import { slide } from 'svelte/transition';

	let { changePasswordForm, onClose }: { changePasswordForm: any; onClose: () => void } = $props();

	let errorMessage = $state('');

	let settingsUrl = $derived(
		`/${page.params.companySlug}/${page.params.plantSlug}/settings?/changePassword`
	);

	const handleClose = () => {
		errorMessage = '';
		onClose();
	};

	let form = useForm(
		changePasswordForm,
		changePasswordSchema,
		'form',
		() => {
			errorMessage = '';
			onClose();
			toast.success($t('auth.changePassword.successChange'));
		},
		() => {},
		() => {
			const formError = page.form?.error;
			if (formError) {
				errorMessage =
					$t(`auth.changePassword.errors.${formError}`) ||
					$t('auth.changePassword.errors.password_change_failed');
			} else {
				errorMessage = $t('auth.changePassword.errors.password_change_failed');
			}
		}
	);
	const { form: formData, enhance, errors } = form;
</script>

<div class="flex w-full flex-col gap-6">
	{#if errorMessage}
		<div transition:slide={{ duration: 300 }}>
			<Alert.Root variant="destructive" class="border-red-200 bg-red-100  text-red-800">
				<Alert.Description>
					{errorMessage}
				</Alert.Description>
			</Alert.Root>
		</div>
	{/if}

	<form method="POST" use:enhance action={settingsUrl} class="flex flex-col gap-4">
		<div class="h-[85px]">
			<Form.Field {form} name="oldPassword">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.changePassword.oldPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.oldPassword}
							class={`h-10 bg-white ${$errors.oldPassword ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="newPassword">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.changePassword.newPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.newPassword}
							class={`h-10 bg-white ${$errors.newPassword ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="newPasswordConfirm">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.changePassword.confirmNewPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.newPasswordConfirm}
							class={`h-10 bg-white ${$errors.newPasswordConfirm ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="mt-6 flex justify-end gap-4">
			<Button
				type="button"
				variant="outline"
				class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				onclick={() => {
					handleClose();
				}}
			>
				{$t('common.buttons.cancel')}
			</Button>

			<Form.Button
				type="submit"
				class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			>
				{$t('common.buttons.save')}
			</Form.Button>
		</div>
	</form>
</div>
