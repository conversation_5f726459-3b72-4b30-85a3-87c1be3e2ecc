<script lang="ts">
	import { Dialog } from 'bits-ui';
	import { scale, fly } from 'svelte/transition';
	import { X, Download, ChevronLeft, ChevronRight } from '@lucide/svelte';
	import { cubicOut } from 'svelte/easing';

	let {
		open = $bindable(false),
		imageUrl,
		imageName,
		onNext = undefined,
		onPrevious = undefined,
		hasNext = false,
		hasPrevious = false,
		onDownload
	}: {
		open?: boolean;
		imageUrl: string;
		imageName: string;
		onNext?: (() => void) | undefined;
		onPrevious?: (() => void) | undefined;
		hasNext?: boolean;
		hasPrevious?: boolean;
		onDownload: (url: string, filename: string) => void;
	} = $props();

	function handleDownload() {
		onDownload(imageUrl, imageName);
	}
</script>

<Dialog.Root bind:open>
	<Dialog.Portal>
		<Dialog.Overlay
			class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/90"
		/>

		<Dialog.Content class="fixed inset-0 z-50 flex flex-col bg-transparent">
			<div
				class="flex items-center justify-between border-b border-white/10 bg-black/50 px-4 py-4"
				transition:fly={{ y: -50, duration: 400, delay: 100, easing: cubicOut }}
			>
				<span class="text-white">{imageName}</span>
				<div class="flex items-center gap-4">
					<button
						class="cursor-pointer rounded-md bg-white/20 p-1.5 text-white transition-colors duration-200 hover:bg-white/30"
						onclick={handleDownload}
					>
						<Download class="size-6" />
					</button>
					<Dialog.Close
						class="cursor-pointer rounded-md bg-white/20 p-1.5 text-white transition-colors duration-200 hover:bg-white/30 "
					>
						<X class="size-6" />
						<span class="sr-only">Close</span>
					</Dialog.Close>
				</div>
			</div>

			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_static_element_interactions -->
			<div class="relative flex-1" onclick={() => (open = false)}>
				<div class="flex h-full w-full items-center justify-center p-4">
					<div onclick={(e) => e.stopPropagation()}>
						<img
							src={imageUrl}
							alt={imageName}
							class="max-h-[80vh] max-w-[90vw] object-contain transition-opacity duration-300"
							transition:scale={{
								duration: 500,
								delay: 200,
								start: 0.8,
								easing: cubicOut
							}}
						/>
					</div>

					<button
						class="absolute top-1/2 left-8 -translate-y-1/2 cursor-pointer rounded-full bg-white/10 p-3 text-white transition-all duration-200 hover:bg-white/30"
						onclick={(e) => {
							e.stopPropagation();
							onPrevious;
						}}
						transition:fly={{ x: -100, duration: 400, delay: 300, easing: cubicOut }}
					>
						<ChevronLeft class="h-8 w-8" />
					</button>

					<button
						class="absolute top-1/2 right-8 -translate-y-1/2 cursor-pointer rounded-full bg-white/10 p-3 text-white transition-all duration-200 hover:bg-white/30"
						onclick={(e) => {
							e.stopPropagation();
							onNext;
						}}
						transition:fly={{ x: 100, duration: 400, delay: 300, easing: cubicOut }}
					>
						<ChevronRight class="h-8 w-8" />
					</button>
				</div>
			</div>
		</Dialog.Content>
	</Dialog.Portal>
</Dialog.Root>

<style>
	:global(body.dialog-open) {
		overflow: hidden;
	}

	img {
		image-rendering: crisp-edges;
		image-rendering: -webkit-optimize-contrast;
	}
</style>
