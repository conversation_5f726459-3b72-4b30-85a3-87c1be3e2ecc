<script lang="ts">
	import ComboboxFilter from './combobox-filter.svelte';
	import { t } from '$lib/translations';
	import { cn } from '$lib/utils';
	import { EvaluationMethods } from '$lib/enums/audits';
	import type { QuestionWithTagsDTO } from '$lib/DTO/questions/questions';

	interface FilterOption {
		id: string;
		name: string;
	}

	interface QuestionFilters {
		evaluationTypes: string[];
		tags: string[]; // Multiple selection for tags
	}

	let {
		questions = [],
		filters = $bindable({
			evaluationTypes: [],
			tags: [] // Array for multiple selection
		}),
		onFiltersChange = () => {},
		class: className = ''
	}: {
		questions?: QuestionWithTagsDTO[];
		filters?: QuestionFilters;
		onFiltersChange?: (filters: QuestionFilters) => void;
		class?: string;
	} = $props();

	const evaluationTypeOptions = $derived(() => {
		if (!questions?.length) return [];

		const availableTypes = new Set(questions.map((q) => q.evaluationType));

		return Object.values(EvaluationMethods)
			.filter((type) => availableTypes.has(type))
			.map((type) => ({
				id: type,
				name: $t(`questions.evaluationType.${type}`)
			}));
	});

	const tagOptions = $derived(() => {
		if (!questions?.length) return [];

		const tagSet = new Set<string>();
		questions.forEach((question) => {
			question.tags?.forEach((tag) => {
				if (tag.name) {
					tagSet.add(tag.name);
				}
			});
		});

		return Array.from(tagSet)
			.sort()
			.map((tagName) => ({
				id: tagName,
				name: tagName
			}));
	});
</script>

<div class={cn('w-full', className)}>
	<div class="grid w-full grid-cols-2 gap-6 p-4 transition-all duration-200 ease-in-out">
		<!-- Evaluation Type Filter -->
		<ComboboxFilter
			label={$t('questions.evaluationType.label')}
			emptyText={$t('questions.evaluationType.noOptions')}
			options={evaluationTypeOptions()}
			bind:selectedValues={filters.evaluationTypes}
			onSelectionChange={(values) => {
				filters.evaluationTypes = values;
				onFiltersChange(filters);
			}}
			class="w-full min-w-0"
		/>

		<!-- Tags Filter -->
		<ComboboxFilter
			label={$t('questions.tags.label')}
			emptyText={$t('questions.tags.noOptions')}
			options={tagOptions()}
			bind:selectedValues={filters.tags}
			onSelectionChange={(values) => {
				filters.tags = values;
				onFiltersChange(filters);
			}}
			class="w-full min-w-0"
		/>
	</div>
</div>
