<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';
	import { X, Search } from '@lucide/svelte';

	let searchInput = $state('');

	let {
		value = $bindable(''),
		placeholder = $t('common.inputs.searchPlaceholder'),
		onSearch
	} = $props();

	function handleSearch() {
		onSearch(searchInput);
	}

	function resetSearch() {
		searchInput = '';
		onSearch('');
	}
</script>

<div class="relative flex max-w-sm">
	<Input
		{placeholder}
		class="h-12 w-full pr-28 placeholder:opacity-0 md:placeholder:opacity-100"
		value={searchInput}
		onkeydown={(e) => {
			if (e.key === 'Enter') {
				e.preventDefault();
				handleSearch();
			} else if (e.key === 'Escape') {
				resetSearch();
			}
		}}
		oninput={(e) => (searchInput = e.currentTarget.value)}
	/>
	{#if searchInput}
		<Button
			variant="ghost"
			size="icon"
			onclick={resetSearch}
			class="absolute top-0 right-12 h-12 w-12 hover:bg-transparent"
		>
			<X class="size-5 text-gray-500" />
		</Button>
	{/if}
	<Button
		variant="default"
		size="icon"
		onclick={handleSearch}
		class="bg-primary hover:bg-primary/80 absolute top-0 right-0 h-12 w-12 cursor-pointer rounded-xl text-white hover:text-white
		 [&_svg]:size-6!"
	>
		<Search />
	</Button>
</div>
