<script lang="ts">
	import { t } from '$lib/translations';
	import { page } from '$app/state';
	import {
		CalendarFold,
		ChartPie,
		ClipboardList,
		Files,
		LandPlot,
		ShieldCheck,
		Users
	} from '@lucide/svelte';
	import { Menu } from '@lucide/svelte';
	import logo from '$lib/assets/logo.png';

	const pages = $derived([
		{
			name: $t('common.navigation.dashboard'),
			icon: ChartPie,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/dashboard`
		},
		{
			name: $t('common.navigation.overview'),
			icon: CalendarFold,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/audits`
		},
		{
			name: $t('common.navigation.workplaces'),
			icon: LandPlot,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/workplaces`
		},
		{
			name: $t('common.navigation.auditors'),
			icon: Users,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/auditors`
		},
		// { name: $t('common.navigation.documents'), icon: Files, path: `/${page.params.companySlug}/${page.params.plantSlug}/documents` },
		{
			name: $t('common.navigation.questions'),
			icon: ClipboardList,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/questions`
		}
	]);

	let menuOpen = $state(true);
	let path = $derived(page.url.pathname);

	function isActive(itemPath: string): boolean {
		return path === itemPath || path.startsWith(itemPath + '/');
	}
</script>

<div class="flex h-screen flex-col">
	<aside
		class={`sticky top-0 flex h-full flex-col overflow-y-auto bg-[#101826] px-[15px] transition-all duration-300 ${
			menuOpen ? 'w-[120px]' : 'w-[75px]'
		}`}
	>
		<div class="mt-3 flex justify-center">
			<img src={logo} alt="LeanAudit.app logo" />
		</div>

		<div class="flex flex-1 flex-col justify-center">
			<div class="font-exo2">
				{#each pages as item (item.name)}
					<a
						href={item.path}
						class="my-3 flex aspect-square w-full flex-col items-center justify-center rounded-xl p-1 text-[0.7rem] text-white transition-all {isActive(
							item.path
						)
							? ' bg-[#7D9AD3]/25'
							: 'bg-opacity-25 hover:bg-gray-800'}"
					>
						<div class="flex h-full w-full flex-col items-center justify-center py-2">
							{#if item.icon}
								<item.icon
									size={32}
									class={`${isActive(item.path) ? 'text-white' : 'text-gray-300'}`}
								/>
							{/if}
							<div
								class="mt-1 text-center leading-tight break-words transition-all duration-300 {menuOpen
									? 'max-h-[40px] opacity-100'
									: 'max-h-0 opacity-0'}"
							>
								{item.name.toUpperCase()}
							</div>
						</div>
					</a>
				{/each}
			</div>
		</div>

		<div class="mt-auto pb-4">
			<button
				class="flex w-full cursor-pointer flex-col items-center p-2 text-black"
				onclick={() => {
					menuOpen = !menuOpen;
				}}
			>
				<Menu size={32} class="text-white" />
			</button>
		</div>
	</aside>
</div>
