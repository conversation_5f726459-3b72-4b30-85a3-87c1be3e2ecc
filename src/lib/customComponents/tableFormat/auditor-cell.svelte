<script lang="ts">
	import * as Avatar from '$lib/components/ui/avatar/index';

	let { firstName, lastName }: { firstName: string | null; lastName: string | null } = $props();
</script>

<div class="flex flex-row items-center gap-2">
	<Avatar.Root class="size-8">
		<Avatar.Fallback class="text-[11px] italic">
			{firstName?.[0].toUpperCase()}{lastName?.[0].toUpperCase()}
		</Avatar.Fallback>
	</Avatar.Root>
	<div class="text-sm">
		{firstName}
		{lastName}
	</div>
</div>
