<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { toast } from 'svelte-sonner';
	import { t } from '$lib/translations';

	let {
		progress,
		text,
		variant = 'default',
		auditId,
		completionDate,
		successRate,
		userId,
		auditorId
	} = $props<{
		progress: number;
		text: string;
		variant?:
			| 'default'
			| 'filled'
			| 'warning'
			| 'success'
			| 'success-good'
			| 'success-average'
			| 'success-poor'
			| 'destructive';
		auditId?: string;
		completionDate?: string | null;
		successRate?: number;
		userId: string;
		auditorId: string;
	}>();

	function handleClick() {
		if (!auditId) return;

		let url: string | undefined;

		if (completionDate) {
			url = `/${page.params.companySlug}/${page.params.plantSlug}/audits/${auditId}/results`;
		} else {
			if (userId === auditorId) {
				url = `/${page.params.companySlug}/${page.params.plantSlug}/audits/${auditId}/evaluate`;
			} else {
				toast.warning($t('permissions.audits.evaluation.notEvaluationAuditor'));
				return;
			}
		}

		goto(url);
	}

	let badgeClass = $derived(
		variant === 'destructive'
			? 'bg-[#f88a68] hover:bg-[#f88a68] border-[#f88a68]'
			: variant === 'success-good'
				? 'bg-[#90DAB4] border-[#90DAB4] hover:bg-[#90DAB4]'
				: variant === 'success-average'
					? 'bg-[#EFDF66] border-[#EFDF66] hover:bg-[#EFDF66]'
					: variant === 'success-poor'
						? 'bg-[#D18385] border-[#D18385] hover:bg-[#D18385]'
						: variant === 'success'
							? 'bg-[#90DAB4] border-[#90DAB4] hover:bg-[#90DAB4]'
							: variant === 'warning'
								? 'bg-[#A6B5D6] border-[#A6B5D6] hover:bg-[#A6B5D6]'
								: variant === 'filled'
									? 'bg-[#242E46] border-[#242E46] hover:bg-[#242E46]'
									: 'bg-[#697691] border-[#697691] hover:bg-[#697691]'
	);

	let progressClass = $derived(
		variant === 'destructive'
			? 'bg-[#f88a68]'
			: variant === 'success-good'
				? 'bg-emerald-500/20'
				: variant === 'success-average'
					? 'bg-yellow-500/20'
					: variant === 'success-poor'
						? 'bg-red-500/20'
						: variant === 'success'
							? 'bg-emerald-500/20'
							: variant === 'warning'
								? 'bg-[#A6B5D6]'
								: variant === 'filled'
									? 'bg-[#242E46]'
									: 'bg-[#414E6B]'
	);

	let showProgressBar = $derived(
		(variant === 'default' && progress > 0 && progress < 100) || variant === 'destructive'
	);
</script>

{#if progress !== undefined}
	<div class="inline-flex items-center">
		<Badge
			onclick={handleClick}
			class={`relative overflow-hidden ${badgeClass} h-8 w-32 rounded-md px-3 text-[11px] leading-none font-extralight text-white italic ${auditId ? 'cursor-pointer' : ''}`}
		>
			{#if showProgressBar}
				<div
					class={`absolute top-0 left-0 h-full rounded-md ${progressClass}`}
					style={progress === -1
						? 'width: 100%'
						: progress > 0
							? `width: ${progress}%`
							: 'width: 0%'}
				></div>
			{/if}
			<span class="relative z-10 flex items-center uppercase">
				<span>{text}</span>
				{#if successRate !== undefined && successRate !== null}
					<span class="mx-2 inline-block h-5 w-px bg-white/30"></span>
					<span class="font-semibold">{successRate}</span>
				{/if}
			</span></Badge
		>
	</div>
{/if}
