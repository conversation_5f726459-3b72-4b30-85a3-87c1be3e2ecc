<script lang="ts">
	import TagBadge from '../tags/tag-badge.svelte';
	import { t } from '$lib/translations';
	import type { TagDTO } from '$lib/DTO/tags';

	let {
		tags = [],
		className = '',
		maxTags = 2
	} = $props<{
		tags?: TagDTO[];
		className?: string;
		maxTags?: number;
	}>();

	let visibleTags = $derived(tags.slice(0, maxTags));
	let remainingCount = $derived(Math.max(0, tags.length - maxTags));
</script>

<div class="flex flex-wrap items-center gap-1 {className}">
	{#if tags && tags.length > 0}
		{#each visibleTags as tag (tag.id)}
			<TagBadge {tag} class="max-w-[120px]" truncateText={true} />
		{/each}
		{#if remainingCount > 0}
			<span class="rounded-md bg-gray-500 px-2 py-1 text-xs font-medium text-white">
				+{remainingCount}
			</span>
		{/if}
	{:else}
		<span class="text-sm text-gray-400 italic">{$t('questions.list.noTags')}</span>
	{/if}
</div>
