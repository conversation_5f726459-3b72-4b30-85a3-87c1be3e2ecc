<script lang="ts">
	import { Badge, type BadgeVariant } from '$lib/components/ui/badge';
	import type { TagDTO } from '$lib/DTO/tags';
	import { X } from '@lucide/svelte';

	let {
		tag,
		removable = false,
		onRemove,
		truncateText = false,
		class: className = ''
	}: {
		tag: TagDTO;
		removable?: boolean;
		onRemove?: (tag: TagDTO) => void;
		truncateText?: boolean;
		class?: string;
	} = $props();

	function handleRemove(event: MouseEvent) {
		event.stopPropagation();
		onRemove?.(tag);
	}
</script>

<Badge class="gap-1 {className}" style="background-color: {tag.color}; color: {tag.textColor};">
	<span class={truncateText ? 'truncate' : ''} title={truncateText ? tag.name : undefined}>
		{tag.name}
	</span>
	{#if removable && onRemove}
		<button
			type="button"
			class="ml-1 inline-flex h-3 w-3 items-center justify-center rounded-full transition-colors hover:bg-black/10"
			onclick={handleRemove}
			aria-label="Remove {tag.name}"
		>
			<X class="h-2.5 w-2.5 cursor-pointer" />
		</button>
	{/if}
</Badge>
