<script lang="ts">
	let {
		text,
		backgroundColor = 'white',
		textColor = 'black'
	}: {
		text?: string;
		backgroundColor?: string;
		textColor?: string;
	} = $props();
</script>

{#if text !== ''}
	<span
		class="inline-flex items-center gap-1 rounded-md border px-2.5 py-1.5 text-sm font-medium"
		style="background-color: {backgroundColor}; color: {textColor}; border-color: {backgroundColor}"
	>
		{text}
	</span>
{/if}
