<script lang="ts">
	import { t } from '$lib/translations';
	import TagBadge from './tag-badge.svelte';
	import * as Popover from '$lib/components/ui/popover/index';
	import type { TagDTO } from '$lib/DTO/tags';

	let {
		availableTags = [],
		selectedTags = [],
		placeholder = $t('questions.form.selectTags'),
		createNewTag = true,
		onTagsChange,
		disabled = false
	}: {
		availableTags: TagDTO[];
		selectedTags: TagDTO[];
		placeholder?: string;
		createNewTag?: boolean;
		onTagsChange?: (tags: TagDTO[]) => void;
		disabled?: boolean;
	} = $props();

	let isOpen = $state(false);
	let searchTerm = $state('');
	let inputRef = $state<HTMLInputElement | undefined>(undefined);

	const filteredTags = $derived(
		availableTags.filter(
			(tag) =>
				!selectedTags.some((selected) => selected.id === tag.id) &&
				tag.name.toLowerCase().includes(searchTerm.toLowerCase())
		)
	);

	const canCreateNew = $derived(
		createNewTag &&
			searchTerm.trim() &&
			!availableTags.some((tag) => tag.name.toLowerCase() === searchTerm.toLowerCase()) &&
			!selectedTags.some((tag) => tag.name.toLowerCase() === searchTerm.toLowerCase())
	);

	function selectTag(tag: TagDTO) {
		const newTags = [...selectedTags, tag];
		onTagsChange?.(newTags);
		searchTerm = '';
		isOpen = false;
	}

	function removeTag(tag: TagDTO) {
		const newTags = selectedTags.filter((t) => t.id !== tag.id);
		onTagsChange?.(newTags);
	}

	function createTag() {
		if (!canCreateNew) return;

		const trimmedName = searchTerm.trim();
		const capitalizedName = trimmedName.charAt(0).toUpperCase() + trimmedName.slice(1);

		const newTag: TagDTO = {
			id: `temp-${Date.now()}`, // Temporary ID
			name: capitalizedName,
			color: '#3B82F6',
			textColor: 'white'
		};

		selectTag(newTag);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			event.preventDefault();
			if (filteredTags.length === 1) {
				selectTag(filteredTags[0]);
			} else if (canCreateNew) {
				createTag();
			}
		} else if (event.key === 'Escape') {
			isOpen = false;
			searchTerm = '';
		}
	}
</script>

<Popover.Root bind:open={isOpen}>
	<Popover.Trigger class="w-full">
		<div
			class="border-input bg-background ring-offset-background min-h-[42px] w-full cursor-pointer rounded-md border px-3 py-2 text-sm {disabled
				? 'cursor-not-allowed opacity-50'
				: ''}"
			role="button"
			tabindex="0"
		>
			{#if selectedTags.length > 0}
				<div
					class="flex flex-wrap gap-1 {selectedTags.length > 4
						? 'scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 max-h-[120px] overflow-y-auto'
						: ''}"
				>
					{#each selectedTags as tag}
						<TagBadge {tag} removable={!disabled} onRemove={removeTag} />
					{/each}
				</div>
			{:else}
				<div class="text-muted-foreground py-1">
					{placeholder}
				</div>
			{/if}
		</div>
	</Popover.Trigger>

	<Popover.Content class="w-(--bits-popover-anchor-width) p-0" align="start" sideOffset={4}>
		<!-- Search input -->
		<div class="border-b p-2">
			<input
				bind:this={inputRef}
				bind:value={searchTerm}
				class="focus:ring-ring w-full rounded border px-2 py-1 text-sm focus:ring-2 focus:outline-none"
				placeholder={$t('questions.form.searchTag')}
				onkeydown={handleKeydown}
			/>
		</div>

		<!-- Tag options -->
		<div
			class={filteredTags.length > 4
				? 'scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 max-h-48 overflow-y-auto'
				: 'max-h-48 overflow-y-auto'}
		>
			{#if filteredTags.length > 0}
				{#each filteredTags as tag, index}
					<button
						type="button"
						class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground w-full px-3 py-2 text-left text-sm focus:outline-none {index ===
							filteredTags.length - 1 && !canCreateNew
							? 'rounded-b-md'
							: ''}"
						onclick={() => selectTag(tag)}
					>
						<TagBadge {tag} />
					</button>
				{/each}
			{/if}

			{#if canCreateNew}
				<button
					type="button"
					class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground w-full rounded-b-md border-t px-3 py-2 text-left text-sm focus:outline-none"
					onclick={createTag}
				>
					<div class="flex items-center gap-2">
						<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 6v6m0 0v6m0-6h6m-6 0H6"
							></path>
						</svg>
						{$t('questions.form.createTag').replace('{tagName}', searchTerm)}
					</div>
				</button>
			{/if}

			{#if filteredTags.length === 0 && !canCreateNew}
				<div class="text-muted-foreground px-3 py-2 text-sm">
					{searchTerm ? $t('questions.form.noTagsFound') : $t('questions.form.noAvailableTags')}
				</div>
			{/if}
		</div>
	</Popover.Content>
</Popover.Root>
