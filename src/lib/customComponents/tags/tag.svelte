<script lang="ts">
	import type { TagDTO } from '$lib/DTO/tags';

	let {
		tag,
		variant = 'default',
		size = 'sm',
		removable = false,
		onRemove
	}: {
		tag: TagDTO;
		variant?: 'default' | 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
		size?: 'xs' | 'sm' | 'md' | 'lg';
		removable?: boolean;
		onRemove?: (tag: TagDTO) => void;
	} = $props();

	const variants = {
		default: 'bg-gray-100 text-gray-800 border-gray-200',
		blue: 'bg-blue-100 text-blue-800 border-blue-200',
		green: 'bg-green-100 text-green-800 border-green-200',
		yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
		red: 'bg-red-100 text-red-800 border-red-200',
		purple: 'bg-purple-100 text-purple-800 border-purple-200',
		gray: 'bg-gray-100 text-gray-600 border-gray-200'
	};

	const sizes = {
		xs: 'text-xs px-1.5 py-0.5',
		sm: 'text-xs px-2 py-1',
		md: 'text-sm px-2.5 py-1.5',
		lg: 'text-base px-3 py-2'
	};

	function handleRemove() {
		onRemove?.(tag);
	}
</script>

<span
	class="inline-flex items-center gap-1 rounded-md border font-medium text-white {variants[
		variant
	]} {sizes[size]}"
>
	{tag.name}
	{#if removable && onRemove}
		<button
			type="button"
			class="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full transition-colors hover:bg-black/10"
			onclick={handleRemove}
			aria-label="Odstranit tag {tag.name}"
		>
			<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
				<path
					fill-rule="evenodd"
					d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
					clip-rule="evenodd"
				></path>
			</svg>
		</button>
	{/if}
</span>
