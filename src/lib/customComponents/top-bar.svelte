<script lang="ts">
	import * as Breadcrumb from '$lib/components/ui/breadcrumb/index.js';
	import * as Avatar from '$lib/components/ui/avatar/index.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import { Play, ShieldPlus, Languages } from '@lucide/svelte';
	import { locale, t } from '$lib/translations';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
	import { fade } from 'svelte/transition';

	//Simplified audit DTO
	type LayoutAuditType = {
		id: string;
		name: string;
		code: string;
	};

	type LayoutWorkplace = {
		id: string;
		name: string;
		code: string;
	};

	type LayoutAuditor = {
		id: string;
		name: {
			firstName: string | null;
			lastName: string | null;
		};
		email: string;
	};
	import { useForm } from '$lib/hooks/superformValidation';
	import { createAuditSchema } from '$lib/schemas/audits/audits';
	import { toast } from 'svelte-sonner';
	import FormChangePassword from './form-change-password.svelte';
	import FormAudit from '../../routes/[companySlug]/[plantSlug]/(pages)/audits/_components/form-audit.svelte';
	import CustomDialog from './custom-dialog.svelte';
	import { AuthMethods } from '$lib/utils/authorizationMethods';

	let {
		createAuditData,
		changePasswordForm,
		className = '',
		languages
	} = $props<{
		createAuditData: {
			auditTypes: LayoutAuditType[];
			workplaces: LayoutWorkplace[];
			auditors: LayoutAuditor[];
			instanceForm: any;
		};
		changePasswordForm: any;
		languages: string[];
	}>();

	let isDropdownOpen = $state(false);
	let isAuditDialogOpen = $state(false);
	let isChangePasswordDialogOpen = $state(false);

	const switchLanguage = async (newLocale: string) => {
		if (browser) {
			document.cookie = `language=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}`;
			localStorage.setItem('language', newLocale);
			await goto(page.url.pathname, {
				invalidateAll: true,
				replaceState: true
			});
			window.location.reload();
		}
	};

	let instanceForm = useForm(createAuditData.instanceForm, createAuditSchema, 'form', () => {
		isAuditDialogOpen = false;
		toast.success($t('audits.actionMessages.auditCreated'));
	});
</script>

<div class="flex w-full flex-row items-center justify-between {className}">
	<Breadcrumb.Root>
		<Breadcrumb.List class="text-md">
			{#if page.data.breadcrumbs}
				{#each page.data.breadcrumbs as crumb, i (crumb.label)}
					<div in:fade={{ duration: 300 }} class="flex items-center gap-1.5 sm:gap-2.5">
						<Breadcrumb.Item>
							{#if crumb.href}
								<Breadcrumb.Link href={crumb.href}>
									{#if crumb.kind === 'static'}
										{$t(crumb.label)}
									{:else}
										{crumb.label}
									{/if}
								</Breadcrumb.Link>
							{:else}
								<span
									class={crumb.isLeanAudit
										? 'text-muted-foreground'
										: i === page.data.breadcrumbs.length - 1
											? 'text-black'
											: 'text-muted-foreground'}
								>
									{#if crumb.kind === 'static'}
										{$t(crumb.label)}
									{:else}
										{crumb.label}
									{/if}
								</span>
							{/if}
						</Breadcrumb.Item>
						{#if i < page.data.breadcrumbs.length - 1}
							<Breadcrumb.Separator />
						{/if}
					</div>
				{/each}
			{/if}
		</Breadcrumb.List>
	</Breadcrumb.Root>

	<div class="flex flex-row items-center gap-3">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				<Button variant="ghost" class="h-14 w-14 rounded-full bg-white">
					<Languages style="scale: 1.75" class="text-[#B1B7C3]" />
				</Button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content>
				{#each languages as lang}
					<DropdownMenu.Item class="cursor-pointer" onclick={() => switchLanguage(lang)}>
						<span class:font-bold={$locale === lang}>
							{$t(`common.languages.${lang}`)}
						</span>
						{#if $locale === lang}
							<DropdownMenu.Shortcut>✓</DropdownMenu.Shortcut>
						{/if}
					</DropdownMenu.Item>
				{/each}
			</DropdownMenu.Content>
		</DropdownMenu.Root>

		<!-- <Button variant="ghost" class="h-14 w-14 rounded-full bg-white">
			<ClipboardCheck style="scale: 1.75" class="text-[#B1B7C3]" />
		</Button> -->
		<Button
			variant="default"
			class="h-14 w-14 rounded-full bg-[#2E384F]"
			onclick={() => (isAuditDialogOpen = true)}
		>
			<ShieldPlus style="scale: 1.75" />
		</Button>

		<!-- svelte-ignore a11y_click_events_have_key_events -->
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<div
			class="relative w-72 shrink-0 cursor-pointer rounded-full bg-white"
			onclick={() => (isDropdownOpen = !isDropdownOpen)}
		>
			<div class="mx-3 my-2 flex flex-row items-center">
				<div class="flex h-10 w-10 shrink-0 items-center justify-center">
					<Avatar.Root class="h-full w-full">
						<Avatar.Image alt="User avatar" />
						<Avatar.Fallback class="text-base">
							{page.data.user?.firstName?.[0].toUpperCase()}{page.data.user?.lastName?.[0].toUpperCase()}
						</Avatar.Fallback>
					</Avatar.Root>
				</div>
				<div class="flex min-w-0 grow flex-col px-6 text-left">
					<div class="truncate text-sm leading-none">
						{page.data.user?.firstName.charAt(0).toUpperCase()}{page.data.user?.firstName.slice(1)}
						{page.data.user?.lastName.charAt(0).toUpperCase()}{page.data.user?.lastName.slice(1)}
					</div>
					<div class="truncate text-xs text-gray-400">
						<i>{$t(`common.roles.${page.data.plantRole.name}`)}</i>
					</div>
				</div>
				<div class="relative shrink-0">
					<DropdownMenu.Root bind:open={isDropdownOpen}>
						<DropdownMenu.Trigger>
							<Button
								class={`h-10 w-10 cursor-pointer rounded-full bg-[#2E384F] p-0 transition-all duration-200 ${isDropdownOpen ? 'rotate-90' : 'rotate-0'}`}
							>
								<Play />
							</Button>
						</DropdownMenu.Trigger>
						<DropdownMenu.Content class="w-[calc(17rem-1.5rem)]" align="end">
							{#if AuthMethods.isSuperAdmin(page.data.plantRole?.permissions, page.data.plantRole?.name)}
								<DropdownMenu.Item>
									<button
										class="w-full cursor-pointer px-4 text-left"
										onclick={() =>
											goto(`/${page.params.companySlug}/${page.params.plantSlug}/superAdmin`)}
									>
										{$t('common.adminPage')}
									</button>
								</DropdownMenu.Item>

								<hr class="mx-2 my-2" />
							{/if}

							{#if AuthMethods.can(page.data.plantRole?.permissions, 'plantSettings', '*') || AuthMethods.can(page.data.plantRole?.permissions, 'plantSettings', 'viewPlantSettings')}
								<DropdownMenu.Item>
									<button
										class="w-full cursor-pointer px-4 text-left"
										onclick={() =>
											goto(
												`/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/general`
											)}
									>
										{$t('common.navigation.plantAdmin')}
									</button>
								</DropdownMenu.Item>
							{/if}

							<DropdownMenu.Item>
								<button
									class="w-full cursor-pointer px-4 text-left"
									onclick={() => (isChangePasswordDialogOpen = true)}
								>
									{$t('auth.changePassword.title')}
								</button>
							</DropdownMenu.Item>

							<hr class="mx-2 my-2" />

							<form action="/logout" method="POST" use:enhance>
								<DropdownMenu.Item>
									<button type="submit" class="w-full cursor-pointer px-4 text-left">
										{$t('common.logout')}
									</button>
								</DropdownMenu.Item>
							</form>
						</DropdownMenu.Content>
					</DropdownMenu.Root>
				</div>
			</div>
		</div>
	</div>
</div>

<CustomDialog
	open={isAuditDialogOpen}
	title={$t('audits.newInstance.title')}
	onClose={() => (isAuditDialogOpen = false)}
>
	<FormAudit
		form={instanceForm}
		auditors={createAuditData.auditors}
		auditTypes={createAuditData.auditTypes}
		workplaces={createAuditData.workplaces}
		onClose={() => (isAuditDialogOpen = false)}
		isEditMode={false}
		selectedAuditId={null}
	/>
</CustomDialog>

<CustomDialog
	open={isChangePasswordDialogOpen}
	onClose={() => (isChangePasswordDialogOpen = false)}
	title={$t('auth.changePassword.title')}
>
	<FormChangePassword {changePasswordForm} onClose={() => (isChangePasswordDialogOpen = false)} />
</CustomDialog>
