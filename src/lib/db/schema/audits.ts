import {
	boolean,
	date,
	integer,
	jsonb,
	pgEnum,
	pgTable,
	text,
	timestamp,
	uuid,
	decimal
} from 'drizzle-orm/pg-core';
import { usersTable } from './user';
import { relations } from 'drizzle-orm';
import { workplaceTable } from './workplace';
import { plantsEvaluationConfigurationTable, plantsTable } from './company';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
import { type Attachment } from '$lib/schemas/audits/auditsAnswers';

export const auditRepetetion = pgEnum('repetetion', [
	'singleTime',
	'daily',
	'weekly',
	'monthly',
	'yearly',
	'untilSuccess',
	'other'
]);

export const auditTypesTable = pgTable('templates', {
	id: uuid('id').defaultRandom().primaryKey(),

	createdAt: timestamp('created_at').notNull().defaultNow(),
	updatedAt: timestamp('updated_at').notNull().defaultNow(),

	name: text('name').notNull(),
	code: text('code').notNull(),

	responsiblePersonId: uuid('responsible_person_id')
		.references(() => usersTable.id)
		.notNull(),
	repetetionPlan: auditRepetetion('repetetion_plan').notNull(),
	expectedDuration: integer('expected_duration').notNull().default(60),
	active: boolean('active').notNull().default(true),
	specification: text('specification'),
	plantId: uuid('plant_id').references(() => plantsTable.id),
	questions: jsonb('questions').$type<TemplateQuestions>().notNull().default({})
});

export const auditInstancesTable = pgTable('audits', {
	id: uuid('id').defaultRandom().primaryKey(),

	code: text('code').notNull(),

	auditTypeId: uuid('audit_type_id')
		.notNull()
		.references(() => auditTypesTable.id),
	workplaceId: uuid('workspace_id').references(() => workplaceTable.id),
	responsiblePersonId: uuid('responsible_person_id').references(() => usersTable.id),

	realDuration: integer('real_duration').notNull().default(0),

	plannedDate: date('planned_date').notNull(),
	completionDate: date('completion_date'),
	questions: jsonb('questions')
		.$type<TemplateQuestions>()
		.notNull()
		.default({}) /* Snapshot of template questions => keeps questions even if template changes */,

	successRate: integer('success_rate').notNull().default(0),
	rulesResult: text('rules_result'), // 'success', 'average', 'bad', or null if percentage mode

	evaluationConfigId: uuid('evaluation_config_id').references(
		() => auditInstanceEvaluationConfigTable.id
	),

	createdBy: uuid('created_by').references(() => usersTable.id),
	createdAt: timestamp('created_at').notNull().defaultNow()
});

export const auditAnswersTable = pgTable('answers', {
	id: uuid('id').defaultRandom().primaryKey(),
	questionId: uuid('question_id')
		.references(() => questionsTable.id)
		.notNull(),
	auditId: uuid('audit_id')
		.references(() => auditInstancesTable.id)
		.notNull(),
	evaluationValue: text('evaluation_value').notNull().default('NotVisited'),
	normalizedValue: decimal('normalized_value', { precision: 5, scale: 2 }).notNull().default('0'),
	note: text('note'),
	createdAt: timestamp('created_at').notNull().defaultNow()
});

//All questions in standalone table
export const questionsTable = pgTable('questions', {
	id: uuid('id').defaultRandom().primaryKey(),
	text: text('').notNull(),
	subtext: text(''),
	evaluationType: text('evaluation_type').notNull().default('oknok'),
	deleted: boolean('deleted').notNull().default(false),
	plantId: uuid('plant_id').references(() => plantsTable.id),
	createdAt: timestamp('created_at').notNull().defaultNow()
});

// Tags table
export const tagsTable = pgTable('tags', {
	id: uuid('id').defaultRandom().primaryKey(),
	name: text('name').notNull(),
	color: text('color').notNull().default('#3B82F6'),
	textColor: text('text_color').notNull().default('white'),
	plantId: uuid('plant_id').references(() => plantsTable.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// Many-to-many relationship between questions and tags
export const questionTagsTable = pgTable('question_tags', {
	id: uuid('id').defaultRandom().primaryKey(),
	questionId: uuid('question_id')
		.references(() => questionsTable.id)
		.notNull(),
	tagId: uuid('tag_id')
		.references(() => tagsTable.id)
		.notNull()
});

export const answersAttachmentsTable = pgTable('answers_attachments', {
	id: uuid('id').primaryKey().defaultRandom(),
	answerId: uuid('answer_id').references(() => auditAnswersTable.id),
	url: text('url').notNull(), // S3 klíč (např. "uploads/file.jpg")
	filename: text('filename').notNull(),
	type: text('type').notNull(),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	// Optimalizace presigned URL cache
	presignedUrl: text('presigned_url'), // Vygenerovaná presigned URL
	presignedUrlGeneratedAt: timestamp('presigned_url_generated_at') // Kdy byla vygenerována
});

//-------------------------
//auditType and Instance evaluation config tables
//-------------------------
export const auditTypeEvaluationConfigTable = pgTable('template_evaluation_configuration', {
	id: uuid('id').defaultRandom().primaryKey(),
	auditTypeId: uuid('audit_type_id')
		.notNull()
		.references(() => auditTypesTable.id),

	evaluationMode: text('evaluation_mode').notNull().default('percentage'),

	//Eval weight for wReservations
	evalWeight_wReservations: decimal('eval_weight_w_reservations', { scale: 1 }),

	//Points range
	pointsRangeMin: integer('points_range_min'),
	pointsRangeMax: integer('points_range_max'),
	pointsAvgThreshold: integer('points_avg_threshold'),
	pointsSuccessThreshold: integer('points_success_threshold'),

	//Percentage threshold
	percentageThreshold_average: integer('percentage_threshold_average'),
	percentageThreshold_success: integer('percentage_threshold_success'),

	//Audit score evaluation
	auditThreshold_average: integer('audit_threshold_average'),
	auditThreshold_success: integer('audit_threshold_success'),

	//Rules for rule-based evaluation
	//Orange (avg) result
	average_averageAuditAnswers: integer('rules_average_average_audit_answers'),
	average_badAuditAnswers: integer('rules_average_bad_audit_answers'),

	//Red (bad) result
	notSuccessful_averageAnswers: integer('rules_not_successful_average_answers'),
	notSuccessful_badAnswers: integer('rules_not_successful_bad_answers'),

	createdAt: timestamp('created_at').notNull().defaultNow(),
	updatedAt: timestamp('updated_at').notNull().defaultNow()
});

export const auditInstanceEvaluationConfigTable = pgTable(
	'audit_instance_evaluation_configuration',
	{
		id: uuid('id').defaultRandom().primaryKey(),
		auditTypeEvalConfigId: uuid('audit_type_eval_config_id')
			.notNull()
			.references(() => auditTypeEvaluationConfigTable.id),
		plantEvalConfigId: uuid('plant_eval_config_id')
			.notNull()
			.references(() => plantsEvaluationConfigurationTable.id),

		evaluationMode: text('evaluation_mode').notNull().default('percentage'),

		//Eval weight for wReservations
		evalWeight_wReservations: decimal('eval_weight_w_reservations', { scale: 1 }).notNull(),

		//Points range
		pointsRangeMin: integer('points_range_min').notNull(),
		pointsRangeMax: integer('points_range_max').notNull(),
		pointsAvgThreshold: integer('points_avg_threshold').notNull(),
		pointsSuccessThreshold: integer('points_success_threshold').notNull(),

		//Percentage threshold
		percentageThreshold_average: integer('percentage_threshold_average').notNull(),
		percentageThreshold_success: integer('percentage_threshold_success').notNull(),

		//Audit score evaluation
		auditThreshold_average: integer('audit_threshold_average').notNull(),
		auditThreshold_success: integer('audit_threshold_success').notNull(),

		//Rules for rule-based evaluation
		//Orange (avg) result
		average_averageAuditAnswers: integer('rules_average_average_audit_answers').notNull(),
		average_badAuditAnswers: integer('rules_average_bad_audit_answers').notNull(),

		//Red (bad) result
		notSuccessful_averageAnswers: integer('rules_not_successful_average_answers').notNull(),
		notSuccessful_badAnswers: integer('rules_not_successful_bad_answers').notNull(),

		createdAt: timestamp('created_at').notNull().defaultNow()
	}
);

//-------------------------
//Relations
//-------------------------

export const auditTypesRelations = relations(auditTypesTable, ({ one, many }) => ({
	auditInstance: many(auditInstancesTable),
	responsiblePerson: one(usersTable, {
		fields: [auditTypesTable.responsiblePersonId],
		references: [usersTable.id]
	}),
	plant: one(plantsTable, {
		fields: [auditTypesTable.plantId],
		references: [plantsTable.id]
	})
}));

export const auditInstancesRelations = relations(auditInstancesTable, ({ one }) => ({
	workspace: one(workplaceTable, {
		fields: [auditInstancesTable.workplaceId],
		references: [workplaceTable.id]
	}),
	responsiblePerson: one(usersTable, {
		fields: [auditInstancesTable.responsiblePersonId],
		references: [usersTable.id]
	}),
	createdBy: one(usersTable, {
		fields: [auditInstancesTable.createdBy],
		references: [usersTable.id]
	}),
	auditType: one(auditTypesTable, {
		fields: [auditInstancesTable.auditTypeId],
		references: [auditTypesTable.id]
	}),
	auditAnswer: one(auditAnswersTable, {
		fields: [auditInstancesTable.id],
		references: [auditAnswersTable.auditId]
	}),
	evaluationConfig: one(auditInstanceEvaluationConfigTable, {
		fields: [auditInstancesTable.evaluationConfigId],
		references: [auditInstanceEvaluationConfigTable.id]
	})
}));

export const auditAnswersRelations = relations(auditAnswersTable, ({ one, many }) => ({
	auditInstance: one(auditInstancesTable, {
		fields: [auditAnswersTable.auditId],
		references: [auditInstancesTable.id]
	}),
	question: one(questionsTable, {
		fields: [auditAnswersTable.questionId],
		references: [questionsTable.id]
	}),
	attachments: many(answersAttachmentsTable)
}));

export const answersAttachmentsRelations = relations(answersAttachmentsTable, ({ one }) => ({
	answer: one(auditAnswersTable, {
		fields: [answersAttachmentsTable.answerId],
		references: [auditAnswersTable.id]
	})
}));

// Questions relations with tags
export const questionsRelations = relations(questionsTable, ({ many, one }) => ({
	questionTags: many(questionTagsTable),
	plant: one(plantsTable, {
		fields: [questionsTable.plantId],
		references: [plantsTable.id]
	})
}));

// Tags relations
export const tagsRelations = relations(tagsTable, ({ many, one }) => ({
	questionTags: many(questionTagsTable),
	plant: one(plantsTable, {
		fields: [tagsTable.plantId],
		references: [plantsTable.id]
	})
}));

// Question tags relations
export const questionTagsRelations = relations(questionTagsTable, ({ one }) => ({
	question: one(questionsTable, {
		fields: [questionTagsTable.questionId],
		references: [questionsTable.id]
	}),
	tag: one(tagsTable, {
		fields: [questionTagsTable.tagId],
		references: [tagsTable.id]
	})
}));

export type AuditAnswers = Record<
	string,
	{
		questionId: string;
		auditId: string;
		evaluationValue: string | number | null;
		note?: string;
		files?: Attachment[];
	}
>;

// Config Tables relations
export const auditTypeEvaluationConfigRelations = relations(
	auditTypeEvaluationConfigTable,
	({ one }) => ({
		auditType: one(auditTypesTable, {
			fields: [auditTypeEvaluationConfigTable.auditTypeId],
			references: [auditTypesTable.id]
		})
	})
);

export const auditInstanceEvaluationConfigRelations = relations(
	auditInstanceEvaluationConfigTable,
	({ one }) => ({
		auditTypeEvalConfig: one(auditTypeEvaluationConfigTable, {
			fields: [auditInstanceEvaluationConfigTable.auditTypeEvalConfigId],
			references: [auditTypeEvaluationConfigTable.id]
		}),
		plantEvalConfig: one(plantsEvaluationConfigurationTable, {
			fields: [auditInstanceEvaluationConfigTable.plantEvalConfigId],
			references: [plantsEvaluationConfigurationTable.id]
		})
	})
);
