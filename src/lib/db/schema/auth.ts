import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { usersTable } from './user';
import { relations } from 'drizzle-orm';

export const userAuthsTable = pgTable('users_auths', {
	id: uuid('id').primaryKey().defaultRandom(),
	userId: uuid('user_id')
		.notNull()
		.references(() => usersTable.id)
		.unique(),
	password: text('password'),
	passwordReset: text('password_reset'),
	passwordResetExpiration: timestamp('password_reset_exp', { withTimezone: true, mode: 'date' }),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' })
});

export const userAuthsRelations = relations(userAuthsTable, ({ one }) => ({
	user: one(usersTable, {
		fields: [userAuthsTable.userId],
		references: [usersTable.id]
	})
}));
