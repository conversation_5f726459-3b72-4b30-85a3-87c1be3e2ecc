import { relations } from 'drizzle-orm';
import { boolean, decimal, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { usersTable } from './user';

export const companyTable = pgTable('companies', {
	id: uuid('id').defaultRandom().primaryKey(),
	name: text('name').notNull(),
	code: text('code').notNull().unique(),
	slug: text('slug').notNull(),
	active: boolean('active').notNull().default(true),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	url: text('url'),
	numberOfLicenses: integer('number_of_licenses').notNull().default(10),

	//soft delete - if the case would happen
	deleted: boolean('deleted').notNull().default(false),
	deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'date' })
});

export const plantsTable = pgTable('plants', {
	id: uuid('id').defaultRandom().primaryKey(),
	name: text('name').notNull(),
	slug: text('slug').notNull(),
	code: text('code').notNull(),
	active: boolean('active').notNull().default(true),
	//ekaizen form url
	eKaizenFormURL: text('ekaizen_form_url').unique(),
	tasksEnabled: boolean('tasks_enabled').notNull().default(false),

	url: text('url'),
	countryCode: text('country_code').notNull(),
	companyId: uuid('company_id')
		.notNull()
		.references(() => companyTable.id),
	gpsLocation: text('gps_location'),
	numberOfLicenses: integer('number_of_licenses').notNull().default(10),

	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),

	//soft delete - if the case would happen
	deleted: boolean('deleted').notNull().default(false),
	deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'date' })
});

export const plantsConfigurationTable = pgTable('plants_configuration', {
	id: uuid('id').defaultRandom().primaryKey(),
	plantId: uuid('plant_id')
		.notNull()
		.references(() => plantsTable.id),
	planningHorizonMonths: integer('planning_horizon_months').notNull().default(3),

	//Languages
	supportedLanguages: text('supported_languages').array().notNull().default(['en']),
	defaultLanguage: text('default_language').notNull().default('en'),

	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }),
	updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' })
});

export const plantsEvaluationConfigurationTable = pgTable('plants_evaluation_configuration', {
	id: uuid('id').defaultRandom().primaryKey(),
	plantId: uuid('plant_id')
		.notNull()
		.references(() => plantsTable.id),

	//Eval weight for wReservations
	evalWeight_wReservations: decimal('eval_weight_w_reservations', { scale: 1 })
		.notNull()
		.default('0.5'),

	//Points range
	pointsRangeMin: integer('points_range_min').notNull().default(0),
	pointsRangeMax: integer('points_range_max').notNull().default(10),
	pointsAvgThreshold: integer('points_avg_threshold').notNull().default(5),
	pointsSuccessThreshold: integer('points_success_threshold').notNull().default(8),

	//Percentage threshold
	percentageThreshold_average: integer('percentage_threshold_average').notNull().default(45),
	percentageThreshold_success: integer('percentage_threshold_success').notNull().default(70),

	//Audit score evaluation
	auditThreshold_average: integer('audit_threshold_average').notNull().default(45),
	auditThreshold_success: integer('audit_threshold_success').notNull().default(70),

	//Rules for rule-based evaluation
	//Orange (avg) result
	average_averageAuditAnswers: integer('rules_average_average_audit_answers').notNull().default(2),
	average_badAuditAnswers: integer('rules_average_bad_audit_answers').notNull().default(3),

	//Red (bad) result
	notSuccessful_averageAnswers: integer('rules_not_successful_average_answers')
		.notNull()
		.default(4),
	notSuccessful_badAnswers: integer('rules_not_successful_bad_answers').notNull().default(6),

	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

export const plantsRelations = relations(plantsTable, ({ one }) => ({
	company: one(companyTable),
	configuration: one(plantsConfigurationTable, {
		fields: [plantsTable.id],
		references: [plantsConfigurationTable.plantId]
	}),
	evaluationConfiguration: one(plantsEvaluationConfigurationTable, {
		fields: [plantsTable.id],
		references: [plantsEvaluationConfigurationTable.plantId]
	})
}));

export const plantsConfigurationRelations = relations(plantsConfigurationTable, ({ one }) => ({
	plant: one(plantsTable, {
		fields: [plantsConfigurationTable.plantId],
		references: [plantsTable.id]
	})
}));

export const plantsEvaluationConfigurationRelations = relations(
	plantsEvaluationConfigurationTable,
	({ one }) => ({
		plant: one(plantsTable, {
			fields: [plantsEvaluationConfigurationTable.plantId],
			references: [plantsTable.id]
		})
	})
);

export const companyRelations = relations(companyTable, ({ many }) => ({
	plants: many(plantsTable),
	users: many(usersTable)
}));

// Export types
export type PlantEvaluationConfig = typeof plantsEvaluationConfigurationTable.$inferSelect;
