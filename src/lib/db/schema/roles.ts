import { json, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { usersTable } from './user';
import { plantsTable } from './company';

export const rolesTable = pgTable('roles', {
	id: uuid('id').defaultRandom().primaryKey(),
	name: text('name').notNull().unique(),
	permissions: json('permissions').$type<Record<string, string[]>>().default({}),

	createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow()
});

export const plantRolesTable = pgTable('plant_roles', {
	id: uuid('id').defaultRandom().primaryKey(),
	roleId: uuid('role_id')
		.notNull()
		.references(() => rolesTable.id),
	plantId: uuid('plant_id')
		.notNull()
		.references(() => plantsTable.id),
	createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow()
});

export const userRolePlantsTable = pgTable('user_role_plants', {
	id: uuid('id').defaultRandom().primaryKey(),
	userId: uuid('user_id')
		.notNull()
		.references(() => usersTable.id),
	roleId: uuid('role_id')
		.notNull()
		.references(() => rolesTable.id),
	plantId: uuid('plant_id').references(() => plantsTable.id)
});
