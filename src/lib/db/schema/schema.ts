import { usersRelations, usersTable, usersWorkInfoTable } from './user';
import { userAuthsRelations, userAuthsTable } from './auth';
import {
	auditTypesTable,
	auditInstancesTable,
	auditAnswersTable,
	answersAttachmentsTable,
	auditTypesRelations,
	auditInstancesRelations,
	auditAnswersRelations,
	answersAttachmentsRelations,
	questionsTable,
	tagsTable,
	questionTagsTable,
	questionsRelations,
	tagsRelations,
	questionTagsRelations
} from './audits';
import { workplaceRelations, workplaceTable } from './workplace';
import { companyRelations, companyTable, plantsRelations, plantsTable } from './company';

export const schema = {
	// Users
	users: usersTable,
	usersWorkInfo: usersWorkInfoTable,
	usersRelations,

	// Auth
	userAuths: userAuthsTable,
	userAuthsRelations,

	// Audits
	auditTypes: auditTypesTable,
	auditInstances: auditInstancesTable,
	auditAnswers: auditAnswersTable,
	answersAttachments: answersAttachmentsTable,
	auditTypesRelations,
	auditInstancesRelations,
	auditAnswersRelations,
	answersAttachmentsRelations,

	//Workspaces
	workspaces: workplaceTable,
	workplaceRelations,

	//Questions
	questions: questionsTable,
	questionsRelations,

	//Tags
	tags: tagsTable,
	questionTags: questionTagsTable,
	tagsRelations,
	questionTagsRelations,

	//Company and plants
	companies: companyTable,
	plants: plantsTable,
	companyRelations,
	plantsRelations
};
