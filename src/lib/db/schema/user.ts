import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { userAuthsTable } from './auth';
import { relations } from 'drizzle-orm';
import { companyTable, plantsTable } from './company';
import { auditTypesTable } from './audits';

export const usersTable = pgTable('users', {
	id: uuid('id').defaultRandom().primaryKey(),
	firstName: text('first_name'),
	lastName: text('last_name'),
	email: text('email').notNull().unique(),
	phone: text('phone').notNull(),
	active: boolean('active').notNull().default(true),
	//Company
	companyId: uuid('company_id').references(() => companyTable.id),

	deleted: boolean('deleted').notNull().default(false),
	deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'date' })
});

export const usersWorkInfoTable = pgTable('users_work_info', {
	id: uuid('id').defaultRandom().primaryKey(),
	userId: uuid('user_id')
		.notNull()
		.references(() => usersTable.id),
	companyId: uuid('company_id')
		.notNull()
		.references(() => companyTable.id),
	plantId: uuid('plant_id')
		.notNull()
		.references(() => plantsTable.id),
	cardNumber: text('card_number').notNull(),

	deleted: boolean('deleted').notNull().default(false),
	deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'date' })
});

//Access to plants - TODO: DELETE
export const userPlantsTable = pgTable('user_plants', {
	id: uuid('id').defaultRandom().primaryKey(),
	userId: uuid('user_id')
		.references(() => usersTable.id)
		.notNull(),
	plantId: uuid('plant_id')
		.references(() => plantsTable.id)
		.notNull(),

	deletedUser: boolean('deleted').notNull().default(false),
	deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'date' })
});

export const usersRelations = relations(usersTable, ({ one, many }) => ({
	auth: one(userAuthsTable),
	company: one(companyTable, {
		fields: [usersTable.companyId],
		references: [companyTable.id]
	}),
	workInfo: one(usersWorkInfoTable),
	userPlants: many(userPlantsTable),
	responsibleForAuditTypes: many(auditTypesTable)
}));

export const userPlantsRelations = relations(userPlantsTable, ({ one }) => ({
	user: one(usersTable, {
		fields: [userPlantsTable.userId],
		references: [usersTable.id]
	}),
	plant: one(plantsTable, {
		fields: [userPlantsTable.plantId],
		references: [plantsTable.id]
	})
}));

export const usersWorkInfoRelations = relations(usersWorkInfoTable, ({ one }) => ({
	user: one(usersTable, {
		fields: [usersWorkInfoTable.userId],
		references: [usersTable.id]
	}),
	company: one(companyTable, {
		fields: [usersWorkInfoTable.companyId],
		references: [companyTable.id]
	}),
	plant: one(plantsTable, {
		fields: [usersWorkInfoTable.plantId],
		references: [plantsTable.id]
	})
}));

export const sessionsTable = pgTable('sessions', {
	id: text('id').primaryKey(),
	userId: uuid('user_id')
		.notNull()
		.references(() => usersTable.id),
	expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull()
});
