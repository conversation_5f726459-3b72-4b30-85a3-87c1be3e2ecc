import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { usersTable } from './user';
import { plantsTable } from './company';
import { relations } from 'drizzle-orm';

export const workplaceTable = pgTable('workplaces', {
	id: uuid('id').defaultRandom().primaryKey(),
	name: text('name').notNull(),
	code: text('code').notNull().default(''),
	responsiblePersonId: uuid('responsible_person_id').references(() => usersTable.id),
	plantId: uuid('plant_id').references(() => plantsTable.id),
	eKaizenWorkstationId: text('ekaizen_workstation_id'),
	active: boolean('active').notNull().default(true),
	description: text('description'),

	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

export const workplaceRelations = relations(workplaceTable, ({ many, one }) => ({
	users: many(usersTable),
	responsiblePerson: one(usersTable, {
		fields: [workplaceTable.responsiblePersonId],
		references: [usersTable.id]
	}),
	plant: one(plantsTable, {
		fields: [workplaceTable.plantId],
		references: [plantsTable.id]
	})
}));
