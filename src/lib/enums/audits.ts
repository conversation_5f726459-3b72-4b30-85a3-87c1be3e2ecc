export const AuditRepetition = {
	singleTime: 'singleTime',
	daily: 'daily',
	weekly: 'weekly',
	monthly: 'monthly',
	yearly: 'yearly',
	untilSuccess: 'untilSuccess',
	other: 'other'
} as const;

export const AuditState = {
	planned: 'planned',
	inProgress: 'inProgress',
	finished: 'finished',
	late: 'late'
} as const;

export type AuditRepetition = (typeof AuditRepetition)[keyof typeof AuditRepetition];
export type AuditState = (typeof AuditState)[keyof typeof AuditState];
export const EvaluationMethods = {
	oknok: 'oknok',
	yesno: 'yesno',
	yesno_inverse: 'yesno_inverse',
	points: 'points',
	percentage: 'percentage',
	meetsreservations: 'meetsreservations'
};
export type EvaluationType = (typeof EvaluationMethods)[keyof typeof EvaluationMethods];

export const EvaluationModes = {
	percentage: 'percentage',
	rules: 'rules'
};
export type EvaluationMode = (typeof EvaluationModes)[keyof typeof EvaluationModes];
