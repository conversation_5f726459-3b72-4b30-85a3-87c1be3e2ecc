import { superForm, type SuperValidated } from 'sveltekit-superforms';
import { zodClient } from 'sveltekit-superforms/adapters';
import type { ZodType } from 'zod';

/**
 *
 * @param dataForm Data to be validated
 * @param schema Schema used for valiadation
 * @param dataTypeVal Data type: json, form (default), undefined
 * @param onSuccessAction Action that will be executed on success
 * @param onErrorAction Action that will be executed on error
 * @param onFailureAction Action that will be executed on failure
 * @returns SuperValidated data using zod client
 */
export function useForm<T extends Record<string, unknown>>(
	dataForm: SuperValidated<T>,
	schema: ZodType<T>,
	dataTypeVal?: 'json' | 'form' | undefined,
	onSuccessAction?: () => void,
	onErrorAction?: () => void,
	onFailureAction?: (result?: unknown) => void,
	resetFormOnSuccess = true
) {
	const form = superForm(dataForm, {
		validators: zodClient(schema),
		dataType: dataTypeVal || 'form',
		resetForm: resetFormOnSuccess,
		onResult: ({ result }) => {
			if (result.type === 'success') {
				if (onSuccessAction) onSuccessAction();
			} else if (result.type === 'error') {
				if (onErrorAction) onErrorAction();
			} else if (result.type === 'failure') {
				if (onFailureAction) onFailureAction(result);
			}
		}
	});

	return form;
}
