import type { availablePermissions } from '$lib/constants/permissions';
import type { sessionsTable, usersTable } from '$lib/db/schema/user';
import type { InferSelectModel } from 'drizzle-orm';

export type User = InferSelectModel<typeof usersTable>;
export type Session = InferSelectModel<typeof sessionsTable>;

export type AppUser = User & {
	companyId: string;
	plants: string[];
	mainPlantId: string;
};

export type SessionValidationResult =
	| {
			session: Session;
			user: AppUser;
			plantRoles: Array<{ roleId: string; plantId: string | null }>;
	  }
	| { session: null; user: null; plantRoles: null };

export type PermissionCode = (typeof availablePermissions)[number]['code'];

export type RolePermissions = Record<string, string[]>;

export type AvailablePermissions = {
	code: PermissionCode;
	actions: string[];
};
