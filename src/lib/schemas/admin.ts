import { t } from '$lib/translations';
import { z } from 'zod';

const localizedError = (key: string) => t.get(`admin.users.validation.${key}`);

export const createUserSchema = z
	.object({
		firstName: z.string().min(1, localizedError('firstNameRequired')),
		lastName: z.string().min(1, localizedError('lastNameRequired')),
		email: z.string().email(localizedError('emailInvalid')),
		phone: z.string().min(3, localizedError('phoneRequired')),
		active: z.boolean().default(true),
		password: z.string().min(8, localizedError('passwordTooShort')),
		passwordConfirm: z.string().min(1, localizedError('passwordConfirmRequired')),
		companyId: z.string().optional(),
		plantId: z.string().optional(),
		cardNumber: z.string().optional()
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: localizedError('passwordConfirmInvalid'),
		path: ['passwordConfirm']
	});

export const updateUserSchema = z.object({
	id: z.string().min(1, localizedError('idRequired')),
	firstName: z.string().min(1, localizedError('firstNameRequired')),
	lastName: z.string().min(1, localizedError('lastNameRequired')),
	email: z.string().email(localizedError('emailInvalid')),
	phone: z.string().optional(),
	active: z.boolean().default(true),
	companyId: z.string().optional(),
	plantId: z.string().optional(),
	cardNumber: z.string().optional()
});

export type CreateUserForm = z.infer<typeof createUserSchema>;
export type UpdateUserForm = z.infer<typeof updateUserSchema>;
