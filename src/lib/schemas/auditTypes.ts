import { AuditRepetition } from '$lib/enums/audits';
import { z } from 'zod';
import { t } from '$lib/translations';

// Helper pro lokalizované chybové <PERSON>
const localizedError = (key: string) => t.get(`errors.${key}`);

export const auditTypeSchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1, localizedError('form.auditTypeNameRequired')),
	code: z
		.string()
		.min(1, localizedError('form.auditTypeCodeRequired'))
		.max(10, localizedError('form.auditTypeCodeMax')),
	evaluationMode: z.string().min(1).default('percentage'),
	active: z.boolean(),
	responsiblePersonId: z.string().min(1, localizedError('form.responsiblePersonRequired')),
	expectedDuration: z
		.string()
		.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, localizedError('validation.timeFormat')),
	repetetionPlan: z.nativeEnum(AuditRepetition)
});

export type AuditTypeForm = z.infer<typeof auditTypeSchema>;

export const createAuditTypeSchema = auditTypeSchema.omit({
	id: true,
	active: true,
	expectedDuration: true,
	evaluationMode: true
});

export const duplicateAuditTypeSchema = createAuditTypeSchema.extend({
	originalAuditTypeId: z.string()
});

export type CreateAuditTypeForm = z.infer<typeof createAuditTypeSchema>;

export const auditTypeCategorySchema = z.object({
	title: z
		.string()
		.min(1, localizedError('form.categoryAbbreviationRequired'))
		.max(10, localizedError('form.categoryAbbreviationMax')),
	subtitle: z.string().optional()
});

export type AuditTypeCategoryForm = z.infer<typeof auditTypeCategorySchema>;

export const templateInfoSchema = auditTypeSchema.extend({
	specification: z.string().nullable().optional()
});

export type TemplateInfoForm = z.infer<typeof templateInfoSchema>;
