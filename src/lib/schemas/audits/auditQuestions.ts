import { z } from 'zod';
import { t } from '$lib/translations';

// Helper pro lokalizované chybo<PERSON>
const localizedError = (key: string) => t.get(`errors.validation.${key}`);

export type TemplateQuestion = {
	questionId: string;
	required: boolean;
	order?: number;
};

export interface TemplateCategory {
	order: number;
	name: string;
	questions: TemplateQuestion[];
}

export interface TemplateQuestions {
	[key: string]: TemplateCategory;
}

export const templateQuestionSchema = z.object({
	questionId: z.string().min(1, localizedError('required')),
	required: z.boolean()
});

export const templateQuestionsSchema = z.record(z.string(), z.array(templateQuestionSchema));

export type TemplateQuestionForm = z.infer<typeof templateQuestionSchema>;

export type TemplateQuestionsForm = z.infer<typeof templateQuestionsSchema>;
