import { AuditRepetition } from '$lib/enums/audits';
import { z } from 'zod';
import { t } from '$lib/translations';

// Helper pro lokalizované chybové <PERSON>
const localizedError = (key: string) => t.get(`errors.validation.${key}`);

export const auditInstanceSchema = z.object({
	id: z.string().optional(),
	code: z.string().min(1, localizedError('required')),
	auditTypeId: z.string().min(1, localizedError('required')),
	auditTypeName: z.string().optional(),
	workplaceId: z.string().min(1, localizedError('required')),
	workplaceName: z.string().optional(),
	responsiblePersonId: z.string().min(1, localizedError('required')),
	responsiblePersonName: z.string().optional(),
	repetetionPlan: z.nativeEnum(AuditRepetition),
	expectedDuration: z
		.string()
		.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, localizedError('timeFormat')),
	plannedDate: z.string().transform((date) => {
		const parsed = new Date(date);
		if (isNaN(parsed.getTime())) {
			throw new Error(localizedError('invalidDate'));
		}
		return parsed.toISOString();
	}),
	progress: z.number().min(0)
});

export type AuditInstanceForm = z.infer<typeof auditInstanceSchema>;

//Additional validation schemas

export const createAuditSchema = z.object({
	id: z.string().optional(),
	auditTypeId: z.string().min(1, localizedError('required')),
	workplaceId: z.string().min(1, localizedError('required')),
	responsiblePersonId: z.string().min(1, localizedError('required')),
	plannedDate: z.string().min(1, localizedError('required'))
});

export type CreateAuditForm = z.infer<typeof createAuditSchema>;

export const setupAuditSchema = auditInstanceSchema.omit({ code: true });
