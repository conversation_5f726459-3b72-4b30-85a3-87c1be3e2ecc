import { z } from 'zod';
import { t } from '$lib/translations';

// Helper pro lokalizované chybové <PERSON>
const localizedError = (key: string) => t.get(`errors.${key}`);

export const attachmentSchema = z.object({
	url: z.string(),
	filename: z.string(),
	type: z.string()
});

export type Attachment = z.infer<typeof attachmentSchema>;

export const questionAnswerSchema = z.object({
	questionId: z.string().min(1, localizedError('form.questionRequired')),
	auditId: z.string().min(1, localizedError('form.auditTypeRequired')),
	evaluationValue: z.union([z.string(), z.number()]).nullable(),
	note: z.string().optional(),
	files: z.array(attachmentSchema).optional()
});

export type QuestionAnswer = z.infer<typeof questionAnswerSchema>;
export type AuditAnswers = Record<string, QuestionAnswer>;

export const auditAnswersSchema = z.record(z.string(), questionAnswerSchema);
