import { z } from 'zod';
import { t } from '$lib/translations';

const localizedError = (key: string) => t.get(`errors.${key}`);

export const questionSchema = z.object({
	id: z.string().optional(),
	text: z.string().min(1, localizedError('form.questionTextRequired')),
	subtext: z.string().nullable(),
	evaluationType: z.enum([
		'oknok',
		'yesno',
		'yesno_inverse',
		'points',
		'percentage',
		'meetsreservations'
	]),
	tagIds: z.array(z.string()).optional().default([]),
	tagNames: z.array(z.string()).optional().default([])
});

export type QuestionForm = z.infer<typeof questionSchema>;
