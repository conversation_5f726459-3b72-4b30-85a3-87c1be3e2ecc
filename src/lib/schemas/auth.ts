import { z } from 'zod';
import { t } from '$lib/translations';

// Helper for localized error messages
const localizedError = (key: string) => t.get(`errors.validation.${key}`);

// Registration and login schemas

export const registrationSchema = z
	.object({
		firstName: z.string().min(1, localizedError('required')),
		lastName: z.string().min(1, localizedError('required')),
		email: z.string().email(localizedError('email')),
		phone: z.string().min(3, localizedError('required')),
		password: z.string().min(8, localizedError('password')),
		passwordConfirm: z.string().min(1, localizedError('required'))
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: localizedError('passwordMatch'),
		path: ['passwordConfirm']
	});

export const loginSchema = z.object({
	email: z.string().email(localizedError('email')),
	password: z.string().min(1, localizedError('required'))
});

export type RegistrationForm = z.infer<typeof registrationSchema>;
export type LoginForm = z.infer<typeof loginSchema>;

// Forgot and reset password schemas

export const forgotPasswordSchema = z.object({
	email: z.string().email(localizedError('email')),
	locale: z.string().optional()
});

export const resetPasswordSchema = z
	.object({
		password: z.string().min(8, localizedError('password')),
		passwordConfirm: z.string().min(1, localizedError('required'))
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: localizedError('passwordMatch'),
		path: ['passwordConfirm']
	});

export type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordForm = z.infer<typeof resetPasswordSchema>;

// Change password schema
export const changePasswordSchema = z
	.object({
		oldPassword: z.string().min(8, localizedError('oldPasswordRequired')),
		newPassword: z.string().min(8, localizedError('password')),
		newPasswordConfirm: z.string().min(1, localizedError('required'))
	})
	.refine((data) => data.newPassword === data.newPasswordConfirm, {
		message: localizedError('passwordMatch'),
		path: ['newPasswordConfirm']
	});
export type ChangePasswordForm = z.infer<typeof changePasswordSchema>;
