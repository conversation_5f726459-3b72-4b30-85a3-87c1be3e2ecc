import { z } from 'zod';
import { t } from '$lib/translations';

const localizationError = (area: string, key: string) => t.get(`admin.${area}.validation.${key}`);

export const companySchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1, localizationError('companies', 'name')),
	code: z.string().min(1, localizationError('companies', 'code')),
	active: z.boolean().optional().default(true),
	slug: z.string().min(1, localizationError('companies', 'slug')),
	url: z.string().url(localizationError('companies', 'url')).optional(),
	numberOfLicenses: z.number().min(1, localizationError('companies', 'numberOfLicenses'))
});

export const plantSchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1, localizationError('plants', 'name')),
	code: z.string().min(1, localizationError('plants', 'code')),
	slug: z.string().min(1, localizationError('plants', 'slug')),
	active: z.boolean().optional().default(true),
	countryCode: z.string().min(1, localizationError('plants', 'countryCode')),
	companyId: z.string().min(1, localizationError('plants', 'companyId')),
	numberOfLicenses: z.number().min(1, localizationError('plants', 'numberOfLicenses')),
	url: z.string().url(localizationError('plants', 'url')).optional(),
	gpsLocation: z.string().optional(),
	eKaizenFormURL: z.string().url(localizationError('plants', 'eKaizenFormURL')).optional(),
	tasksEnabled: z.boolean().optional().default(false)
});

export type CompanyForm = z.infer<typeof companySchema>;
export type PlantForm = z.infer<typeof plantSchema>;
