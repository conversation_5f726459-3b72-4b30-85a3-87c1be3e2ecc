import { z } from 'zod';
import { t } from '$lib/translations';

const localizedError = (key: string) => t.get(`evaluationConfig.errors.${key}`);

const baseThresholdsSchema = z.object({
	percentageThreshold_average: z.coerce
		.number({ required_error: localizedError('required') })
		.min(1, localizedError('percentage.min'))
		.max(100, localizedError('percentage.max')),
	percentageThreshold_success: z.coerce
		.number({ required_error: localizedError('required') })
		.min(1, localizedError('percentage.min'))
		.max(100, localizedError('percentage.max')),

	pointsRangeMin: z.coerce.number({ required_error: localizedError('required') }),
	pointsRangeMax: z.coerce
		.number({ required_error: localizedError('required') })
		.min(1, localizedError('points.minMax')),

	pointsAvgThreshold: z.coerce.number({ required_error: localizedError('required') }),
	pointsSuccessThreshold: z.coerce.number({ required_error: localizedError('required') })
});

const baseWeightsSchema = z.object({
	evalWeight_wReservations: z.coerce
		.number({ required_error: localizedError('required') })
		.min(0, localizedError('weight.min'))
		.max(1, localizedError('weight.max'))
});

const baseRulesSchema = z.object({
	average_averageAuditAnswers: z.coerce.number().min(0, localizedError('rules.min')).nullable(),
	average_badAuditAnswers: z.coerce.number().min(0, localizedError('rules.min')).nullable(),
	notSuccessful_averageAnswers: z.coerce.number().min(1, localizedError('rules.min')).nullable(),
	notSuccessful_badAnswers: z.coerce.number().min(0, localizedError('rules.min')).nullable()
});

const baseAuditThresholdsSchema = z.object({
	auditThreshold_average: z.coerce
		.number({ required_error: localizedError('required') })
		.min(1, localizedError('auditThreshold.min'))
		.max(100, localizedError('auditThreshold.max')),
	auditThreshold_success: z.coerce
		.number({ required_error: localizedError('required') })
		.min(1, localizedError('auditThreshold.min'))
		.max(100, localizedError('auditThreshold.max'))
});

export const thresholdsSchema = baseThresholdsSchema
	.refine((data) => data.percentageThreshold_success > data.percentageThreshold_average, {
		message: localizedError('validation.successHigherThanAverage'),
		path: ['percentageThreshold_success']
	})
	.refine((data) => data.pointsRangeMax > data.pointsRangeMin, {
		message: localizedError('validation.maxHigherThanMin'),
		path: ['pointsRangeMax']
	})
	.refine((data) => data.pointsSuccessThreshold > data.pointsAvgThreshold, {
		message: localizedError('validation.successHigherThanAverage'),
		path: ['pointsSuccessThreshold']
	})
	.refine(
		(data) =>
			data.pointsAvgThreshold >= data.pointsRangeMin &&
			data.pointsAvgThreshold <= data.pointsRangeMax,
		{
			message: localizedError('validation.pointsThresholdInRange'),
			path: ['pointsAvgThreshold']
		}
	)
	.refine(
		(data) =>
			data.pointsSuccessThreshold >= data.pointsRangeMin &&
			data.pointsSuccessThreshold <= data.pointsRangeMax,
		{
			message: localizedError('validation.pointsThresholdInRange'),
			path: ['pointsSuccessThreshold']
		}
	);

export const weightsSchema = baseWeightsSchema;

export const rulesSchema = baseRulesSchema
	.refine(
		(data) => {
			if (data.notSuccessful_averageAnswers === null || data.average_averageAuditAnswers === null) {
				return true;
			}

			if (data.average_averageAuditAnswers === 0) {
				return true;
			}

			return data.notSuccessful_averageAnswers > data.average_averageAuditAnswers;
		},
		{
			message: localizedError('validation.unsuccessfulHigherThanAverage'),
			path: ['notSuccessful_averageAnswers']
		}
	)
	.refine(
		(data) => {
			if (data.notSuccessful_badAnswers === null || data.average_badAuditAnswers === null) {
				return true;
			}

			if (data.average_badAuditAnswers === 0) {
				return true;
			}

			return data.notSuccessful_badAnswers > data.average_badAuditAnswers;
		},
		{
			message: localizedError('validation.unsuccessfulBadHigherThanAverageBad'),
			path: ['notSuccessful_badAnswers']
		}
	);

export const auditThresholdsSchema = baseAuditThresholdsSchema.refine(
	(data) => data.auditThreshold_success > data.auditThreshold_average,
	{
		message: localizedError('validation.auditSuccessHigherThanAverage'),
		path: ['auditThreshold_success']
	}
);

export const evaluationConfigSchema = z
	.object({
		evaluationMode: z.string(),
		...baseThresholdsSchema.shape,
		...baseWeightsSchema.shape,
		...baseRulesSchema.shape,
		...baseAuditThresholdsSchema.shape
	})
	.refine((data) => data.percentageThreshold_success > data.percentageThreshold_average, {
		message: localizedError('validation.successHigherThanAverage'),
		path: ['percentageThreshold_success']
	})
	.refine((data) => data.pointsRangeMax > data.pointsRangeMin, {
		message: localizedError('validation.maxHigherThanMin'),
		path: ['pointsRangeMax']
	})
	.refine((data) => data.pointsSuccessThreshold > data.pointsAvgThreshold, {
		message: localizedError('validation.successHigherThanAverage'),
		path: ['pointsSuccessThreshold']
	})
	.refine((data) => data.auditThreshold_success > data.auditThreshold_average, {
		message: localizedError('validation.auditSuccessHigherThanAverage'),
		path: ['auditThreshold_success']
	})
	.refine(
		(data) => {
			if (data.notSuccessful_averageAnswers === null || data.average_averageAuditAnswers === null) {
				return true;
			}

			if (data.average_averageAuditAnswers === 0) {
				return true;
			}

			return data.notSuccessful_averageAnswers > data.average_averageAuditAnswers;
		},
		{
			message: localizedError('validation.unsuccessfulHigherThanAverage'),
			path: ['notSuccessful_averageAnswers']
		}
	)
	.refine(
		(data) => {
			if (data.notSuccessful_badAnswers === null || data.average_badAuditAnswers === null) {
				return true;
			}

			if (data.average_badAuditAnswers === 0) {
				return true;
			}

			return data.notSuccessful_badAnswers > data.average_badAuditAnswers;
		},
		{
			message: localizedError('validation.unsuccessfulBadHigherThanAverageBad'),
			path: ['notSuccessful_badAnswers']
		}
	);

export type ThresholdsForm = z.infer<typeof thresholdsSchema>;
export type WeightsForm = z.infer<typeof weightsSchema>;
export type RulesForm = z.infer<typeof rulesSchema>;
export type AuditThresholdsForm = z.infer<typeof auditThresholdsSchema>;
export type EvaluationConfigForm = z.infer<typeof evaluationConfigSchema>;

//Plant evaluation config schema
// Create the base object, omit evaluationMode, then apply the same refinements as evaluationConfigSchema
export const plantEvaluationConfigSchema = z
	.object({
		...baseThresholdsSchema.shape,
		...baseWeightsSchema.shape,
		...baseRulesSchema.shape,
		...baseAuditThresholdsSchema.shape
	})
	.refine((data) => data.percentageThreshold_success > data.percentageThreshold_average, {
		message: localizedError('validation.successHigherThanAverage'),
		path: ['percentageThreshold_success']
	})
	.refine((data) => data.pointsRangeMax > data.pointsRangeMin, {
		message: localizedError('validation.maxHigherThanMin'),
		path: ['pointsRangeMax']
	})
	.refine((data) => data.pointsSuccessThreshold > data.pointsAvgThreshold, {
		message: localizedError('validation.successHigherThanAverage'),
		path: ['pointsSuccessThreshold']
	})
	.refine((data) => data.auditThreshold_success > data.auditThreshold_average, {
		message: localizedError('validation.auditSuccessHigherThanAverage'),
		path: ['auditThreshold_success']
	})
	.refine(
		(data) => {
			if (data.notSuccessful_averageAnswers === null || data.average_averageAuditAnswers === null) {
				return true;
			}

			if (data.average_averageAuditAnswers === 0) {
				return true;
			}

			return data.notSuccessful_averageAnswers > data.average_averageAuditAnswers;
		},
		{
			message: localizedError('validation.unsuccessfulHigherThanAverage'),
			path: ['notSuccessful_averageAnswers']
		}
	)
	.refine(
		(data) => {
			if (data.notSuccessful_badAnswers === null || data.average_badAuditAnswers === null) {
				return true;
			}

			if (data.average_badAuditAnswers === 0) {
				return true;
			}

			return data.notSuccessful_badAnswers > data.average_badAuditAnswers;
		},
		{
			message: localizedError('validation.unsuccessfulBadHigherThanAverageBad'),
			path: ['notSuccessful_badAnswers']
		}
	);

export type PlantEvaluationConfigForm = z.infer<typeof plantEvaluationConfigSchema>;
