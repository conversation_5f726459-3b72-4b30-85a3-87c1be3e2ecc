import { z } from 'zod';
import { t } from '$lib/translations';

// Helper pro lokalizované chybové <PERSON>
const localizedError = (key: string) => t.get(`errors.${key}`);

export const contactMessageSchema = z.object({
	message: z.string().min(1, localizedError('form.messageRequired')),
	questionId: z.string(),
	templateAuditorId: z.string(),
	workplaceManagerId: z.string(),
	auditTypeName: z.string()
});

export type ContactMessageForm = z.infer<typeof contactMessageSchema>;
