import { z } from 'zod';
import { t } from '$lib/translations';

const localizedError = (key: string) => t.get(`admin.plants.validation.${key}`);

export const plantSchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1, localizedError('name')),
	code: z.string().min(1, localizedError('code')),
	slug: z.string().min(1, localizedError('slug')),
	status: z.boolean().default(true),
	url: z.string().url(localizedError('url')).optional(),
	countryCode: z.string().min(1, localizedError('countryCode')),
	companyId: z.string().min(1, localizedError('companyId')),
	gpsLocation: z.string().min(1, localizedError('gpsLocation')),
	numberOfLicenses: z.number().min(1, localizedError('numberOfLicenses')),
	eKaizenFormURL: z.string().url(localizedError('eKaizenFormURL')).optional(),
	tasksEnabled: z.boolean().default(false)
});

export type PlantForm = z.infer<typeof plantSchema>;
