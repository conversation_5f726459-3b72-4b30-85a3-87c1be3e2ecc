import { z } from 'zod';

export const plantGeneralSettingsSchema = z.object({
	eKaizenFormURL: z
		.string()
		.optional()
		.refine((val) => !val || val === '' || /^https?:\/\/.+\..+/.test(val), {
			message: 'plantAdmin.general.validation.invalidUrl'
		}),
	tasksEnabled: z.boolean().default(false),
	supportedLanguages: z
		.array(z.string())
		.min(1, 'plantAdmin.general.validation.atLeastOneLanguage')
		.default(['en']),
	defaultLanguage: z.string().min(1, 'plantAdmin.general.validation.defaultLanguageRequired')
});

export type PlantGeneralSettingsForm = z.infer<typeof plantGeneralSettingsSchema>;
