import { z } from 'zod';
import { t } from '$lib/translations';

// Helper pro lokalizované chybov<PERSON>
const localizedError = (key: string) => t.get(`errors.${key}`);

export const workplaceSchema = z.object({
	name: z.string().min(1, localizedError('workplaces.workplaceNameRequired')),
	code: z
		.string()
		.min(1, localizedError('workplaces.workplaceCodeRequired'))
		.max(10, localizedError('workplaces.workplaceCodeMax')),
	responsiblePersonId: z.string().min(1, localizedError('workplaces.responsiblePersonRequired')),
	eKaizenWorkstationId: z.string().optional()
});

export type WorkplaceForm = z.infer<typeof workplaceSchema>;

export const workplaceInfoSchema = workplaceSchema.extend({
	id: z.string().optional(),
	active: z.boolean(),
	description: z.string().optional()
});

export type WorkplaceInfoForm = z.infer<typeof workplaceInfoSchema>;
