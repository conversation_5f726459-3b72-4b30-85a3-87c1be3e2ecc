import { emailClient, getBaseMail } from '.';
import { compileFile } from 'pug';
import { getQuestionById } from '../services/questions/questions';
import { loadTranslations, t } from '$lib/translations';

export type ContactEmailConfig = {
	message: string;
	auditTypeName: string;
	questionId: string;
	senderName?: string;
	locale: string;
};

export const contactEmail = async (to: string, config: ContactEmailConfig) => {

	const compiledFunction = compileFile('src/templates/email/evaluateContact.pug');

	await loadTranslations(config.locale, '/');
	let questionText = '';

	if (config.questionId) {
		try {
			const question = await getQuestionById(config.questionId);

			if (question) {
				questionText = question.text || '';
			} else {
				questionText = t.get('emails.contactEmail.errors.questionNotFound');
			}
		} catch {
			questionText = t.get('emails.contactEmail.errors.questionLoadError');
		}
	} else {
		questionText = t.get('emails.contactEmail.errors.noQuestionSelected');
	}

	const templateData = {
		...config,
		headerTitle: t.get('emails.contactEmail.title'),
		greeting: t.get('emails.contactEmail.greeting'),
		intro: t.get('emails.contactEmail.intro'),
		questionLabel: t.get('emails.contactEmail.questionLabel'),
		messageLabel: t.get('emails.contactEmail.messageLabel'),
		senderLabel: t.get('emails.contactEmail.senderLabel'),
		footer: t.get('emails.contactEmail.footer'),
		questionText: questionText,
		auditTypeName: config.auditTypeName,
		message: config.message,
		senderName: config.senderName
	};

	const htmlTemplate = compiledFunction(templateData);

	const subject = t.get('emails.contactEmail.subject');

	try {
		await emailClient.sendMail({
			...getBaseMail(to, subject),
			html: htmlTemplate
		});

		return true;
	} catch (error) {
		console.error('Error sending email:', error);
		return false;
	}
};
