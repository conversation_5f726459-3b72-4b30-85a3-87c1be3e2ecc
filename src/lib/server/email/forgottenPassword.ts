import { compileFile } from 'pug';
import { t, loadTranslations } from '$lib/translations';
import { emailClient, getBaseMail } from '.';

export interface ForgottenPasswordConfig {
	link: string;
	locale: string;
}

export const forgottenPassword = async (to: string, config: ForgottenPasswordConfig) => {
	await loadTranslations(config.locale, '/');
	const compiledFunction = compileFile('src/templates/email/forgottenPassword.pug');

	const templateData = {
		...config,
		title: t.get('emails.forgottenPassword.title'),
		subject: t.get('emails.forgottenPassword.subject'),
		text: t.get('emails.forgottenPassword.text'),
		additionalInfo: t.get('emails.forgottenPassword.expiration'),
		buttonText: t.get('emails.forgottenPassword.buttonText'),
		securityNoticeTitle: t.get('emails.forgottenPassword.securityNoticeTitle'),
		fallbackText: t.get('emails.forgottenPassword.fallbackText'),
		regards: t.get('emails.forgottenPassword.regards'),
		teamName: t.get('emails.forgottenPassword.teamName'),
		footerInfo: t.get('emails.forgottenPassword.footerInfo'),
		supportText: t.get('emails.forgottenPassword.supportText')
	};

	const htmlTemplate = compiledFunction(templateData);

	try {
		await emailClient.sendMail({
			...getBaseMail(to, templateData.subject),
			html: htmlTemplate
		});
		return true;
	} catch (error) {
		console.error('Error sending email:', error);
		return false;
	}
};
