import { createTransport } from 'nodemailer';
import { contactEmail } from './contactEmail';
import { SECRET_EMAIL_PASSWORD, SECRET_EMAIL_USER } from '$env/static/private';
import { forgottenPassword } from './forgottenPassword';
import { negativeCheckpointsEmail } from './negativeCheckpointsEmail';

const emailClient = createTransport({
	service: 'Gmail',
	host: 'smtp.gmail.com',
	port: 587,
	secure: true,
	auth: {
		user: SECRET_EMAIL_USER,
		pass: SECRET_EMAIL_PASSWORD
	}
});

emailClient.verify(function (error) {
	if (error) {
		console.error('SMTP connection error:', error);
	} else {
		console.log('SMTP server is ready to take our messages');
	}
});

export { emailClient };

export const getBaseMail = (to: string, subject: string) => {
	return { from: '"Noreply LeanAudit" <<EMAIL>>', to, subject } as const;
};

export const EmailService = {
	contactEmail,
	forgottenPassword,
	negativeCheckpointsEmail
};
