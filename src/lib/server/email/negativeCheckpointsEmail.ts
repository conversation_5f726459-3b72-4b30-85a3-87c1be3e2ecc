import { emailClient, getBaseMail } from '.';
import { compileFile } from 'pug';
import { loadTranslations, t } from '$lib/translations';
import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';

export type NegativeCheckpointsEmailConfig = {
	recipientName: string;
	auditTitle: string;
	auditCode: string;
	auditDate: string;
	workplaceName: string;
	auditorName: string;
	negativeCheckpoints: AuditAnswerResultsDTO[];
	auditUrl: string;
	locale: string;
};

export const negativeCheckpointsEmail = async (
	to: string,
	config: NegativeCheckpointsEmailConfig
) => {
	const compiledFunction = compileFile('src/templates/email/negativeCheckpoints.pug');

	await loadTranslations(config.locale, '/');

	const templateData = {
		...config,
		headerTitle: t.get('emails.negativeCheckpoints.title'),
		greeting: t.get('emails.negativeCheckpoints.greeting'),
		intro: t.get('emails.negativeCheckpoints.intro'),
		negativeFound: t
			.get('emails.negativeCheckpoints.negativeFound')
			.replace('{count}', config.negativeCheckpoints.length.toString()),
		checkpointsHeader: t.get('emails.negativeCheckpoints.checkpointsHeader'),
		noteLabel: t.get('emails.negativeCheckpoints.noteLabel'),
		viewAuditButton: t.get('emails.negativeCheckpoints.viewAuditButton'),
		footer: t.get('emails.negativeCheckpoints.footer'),
		regards: t.get('emails.negativeCheckpoints.regards'),
		auditTeam: t.get('emails.negativeCheckpoints.auditTeam'),
		auditInfoTitle: t.get('emails.negativeCheckpoints.auditInfoTitle'),
		auditLabel: t.get('emails.negativeCheckpoints.auditLabel'),
		workplaceLabel: t.get('emails.negativeCheckpoints.workplaceLabel'),
		auditorLabel: t.get('emails.negativeCheckpoints.auditorLabel'),
		dateLabel: t.get('emails.negativeCheckpoints.dateLabel'),
		totalNegativeCount: config.negativeCheckpoints.length
	};

	const htmlTemplate = compiledFunction(templateData);

	const subject = t
		.get('emails.negativeCheckpoints.subject')
		.replace('{auditTitle}', config.auditTitle);

	try {
		await emailClient.sendMail({
			...getBaseMail(to, subject),
			html: htmlTemplate
		});

		return true;
	} catch (error) {
		console.error('Error sending negative checkpoints email:', error);
		return false;
	}
};
