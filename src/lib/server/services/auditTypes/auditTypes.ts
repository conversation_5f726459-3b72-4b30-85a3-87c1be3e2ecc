import type { CreateAuditTypeForm, TemplateInfoForm } from '$lib/schemas/auditTypes';
import { db } from '$lib/db/db.server';
import { auditTypeEvaluationConfigTable, auditTypesTable } from '$lib/db/schema/audits';
import { eq, desc, and, not, like } from 'drizzle-orm';
import { t } from '$lib/translations';
import { usersTable } from '$lib/db/schema/user';
import type { AuditTypeDTO } from '$lib/DTO/auditTypes/auditTypes';
import type { TemplateQuestion, TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
import { plantsEvaluationConfigurationTable } from '$lib/db/schema/company';

export async function getAuditTypes(
	plantId: string,
	getOnlyActive: boolean = true
): Promise<AuditTypeDTO[]> {
	try {
		const conditions = [];

		// Filter out soft-deleted audit types
		conditions.push(not(like(auditTypesTable.code, 'DELETED_%')));

		if (getOnlyActive) {
			conditions.push(eq(auditTypesTable.active, true));
		}

		// plants filtering
		if (plantId) {
			conditions.push(eq(auditTypesTable.plantId, plantId));
		}

		const rawResult = await db
			.select({
				id: auditTypesTable.id,
				code: auditTypesTable.code,
				name: auditTypesTable.name,
				evaluationMode: auditTypeEvaluationConfigTable.evaluationMode,
				active: auditTypesTable.active,
				responsiblePerson: {
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				expectedDuration: auditTypesTable.expectedDuration,
				repetetionPlan: auditTypesTable.repetetionPlan,
				// Evaluation rules
				average_averageAuditAnswers: auditTypeEvaluationConfigTable.average_averageAuditAnswers,
				average_badAuditAnswers: auditTypeEvaluationConfigTable.average_badAuditAnswers,
				notSuccessful_averageAnswers: auditTypeEvaluationConfigTable.notSuccessful_averageAnswers,
				notSuccessful_badAnswers: auditTypeEvaluationConfigTable.notSuccessful_badAnswers,
				// Success thresholds
				auditThreshold_average: auditTypeEvaluationConfigTable.auditThreshold_average,
				auditThreshold_success: auditTypeEvaluationConfigTable.auditThreshold_success
			})
			.from(auditTypesTable)
			.leftJoin(usersTable, eq(auditTypesTable.responsiblePersonId, usersTable.id))
			.leftJoin(
				auditTypeEvaluationConfigTable,
				eq(auditTypesTable.id, auditTypeEvaluationConfigTable.auditTypeId)
			)
			.where(conditions.length > 0 ? and(...conditions) : undefined)
			.orderBy(desc(auditTypesTable.createdAt));

		// Transform the result to match AuditTypeDTO
		const result: AuditTypeDTO[] = rawResult.map((row) => ({
			id: row.id,
			code: row.code,
			name: row.name,
			evaluationMode: row.evaluationMode,
			active: row.active,
			responsiblePerson: row.responsiblePerson,
			expectedDuration: row.expectedDuration,
			repetetionPlan: row.repetetionPlan,
			evaluation: {
				rules: {
					average_averageAuditAnswers: row.average_averageAuditAnswers,
					average_badAuditAnswers: row.average_badAuditAnswers,
					notSuccessful_averageAnswers: row.notSuccessful_averageAnswers,
					notSuccessful_badAnswers: row.notSuccessful_badAnswers
				},
				successThreshold: {
					auditThreshold_average: row.auditThreshold_average,
					auditThreshold_success: row.auditThreshold_success
				}
			}
		}));

		return result ?? [];
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch audit types');
	}
}

export async function getAuditTypeById(auditTypeId: string): Promise<AuditTypeDTO | null> {
	try {
		const [rawResult] = await db
			.select({
				id: auditTypesTable.id,
				name: auditTypesTable.name,
				questions: auditTypesTable.questions,
				expectedDuration: auditTypesTable.expectedDuration,
				repetetionPlan: auditTypesTable.repetetionPlan,
				specification: auditTypesTable.specification,
				active: auditTypesTable.active,
				code: auditTypesTable.code,
				responsiblePerson: {
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				updatedAt: auditTypesTable.updatedAt,
				evaluationMode: auditTypeEvaluationConfigTable.evaluationMode,

				average_averageAuditAnswers: auditTypeEvaluationConfigTable.average_averageAuditAnswers,
				average_badAuditAnswers: auditTypeEvaluationConfigTable.average_badAuditAnswers,
				notSuccessful_averageAnswers: auditTypeEvaluationConfigTable.notSuccessful_averageAnswers,
				notSuccessful_badAnswers: auditTypeEvaluationConfigTable.notSuccessful_badAnswers,

				auditThreshold_average: auditTypeEvaluationConfigTable.auditThreshold_average,
				auditThreshold_success: auditTypeEvaluationConfigTable.auditThreshold_success,

				plant_average_averageAuditAnswers:
					plantsEvaluationConfigurationTable.average_averageAuditAnswers,
				plant_average_badAuditAnswers: plantsEvaluationConfigurationTable.average_badAuditAnswers,
				plant_notSuccessful_averageAnswers:
					plantsEvaluationConfigurationTable.notSuccessful_averageAnswers,
				plant_notSuccessful_badAnswers: plantsEvaluationConfigurationTable.notSuccessful_badAnswers,

				plant_auditThreshold_average: plantsEvaluationConfigurationTable.auditThreshold_average,
				plant_auditThreshold_success: plantsEvaluationConfigurationTable.auditThreshold_success
			})
			.from(auditTypesTable)
			.leftJoin(usersTable, eq(auditTypesTable.responsiblePersonId, usersTable.id))
			.leftJoin(
				auditTypeEvaluationConfigTable,
				eq(auditTypesTable.id, auditTypeEvaluationConfigTable.auditTypeId)
			)
			.leftJoin(
				plantsEvaluationConfigurationTable,
				eq(auditTypesTable.plantId, plantsEvaluationConfigurationTable.plantId)
			)
			.where(and(eq(auditTypesTable.id, auditTypeId), not(like(auditTypesTable.code, 'DELETED_%'))))
			.limit(1);

		if (!rawResult) {
			return null;
		}

		const result = {
			id: rawResult.id,
			code: rawResult.code,
			name: rawResult.name,
			evaluationMode: rawResult.evaluationMode,
			active: rawResult.active,
			responsiblePerson: rawResult.responsiblePerson,
			expectedDuration: rawResult.expectedDuration,
			repetetionPlan: rawResult.repetetionPlan,
			specification: rawResult.specification,
			questions: rawResult.questions,
			evaluation: {
				rules: {
					average_averageAuditAnswers: rawResult.average_averageAuditAnswers,
					average_badAuditAnswers: rawResult.average_badAuditAnswers,
					notSuccessful_averageAnswers: rawResult.notSuccessful_averageAnswers,
					notSuccessful_badAnswers: rawResult.notSuccessful_badAnswers,

					plant_average_averageAuditAnswers: rawResult.plant_average_averageAuditAnswers,
					plant_average_badAuditAnswers: rawResult.plant_average_badAuditAnswers,
					plant_notSuccessful_averageAnswers: rawResult.plant_notSuccessful_averageAnswers,
					plant_notSuccessful_badAnswers: rawResult.plant_notSuccessful_badAnswers
				},
				successThreshold: {
					auditThreshold_average: rawResult.auditThreshold_average,
					auditThreshold_success: rawResult.auditThreshold_success,

					plant_auditThreshold_average: rawResult.plant_auditThreshold_average,
					plant_auditThreshold_success: rawResult.plant_auditThreshold_success
				}
			},
			updatedAt: rawResult.updatedAt
		};

		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch audit type');
	}
}

export async function createAuditType(data: CreateAuditTypeForm, plantId: string) {
	try {
		const [result] = await db.transaction(async (tx) => {
			const [auditType] = await tx
				.insert(auditTypesTable)
				.values({
					name: data.name,
					code: data.code.toUpperCase(),
					responsiblePersonId: data.responsiblePersonId,
					repetetionPlan: data.repetetionPlan,
					plantId
				})
				.returning();

			await tx.insert(auditTypeEvaluationConfigTable).values({
				auditTypeId: auditType.id,
				evaluationMode: 'percentage'
			});

			return [auditType];
		});

		return result;
	} catch (err) {
		console.error('Error creating audit type:', err);
		throw new Error('Failed to create audit type');
	}
}

export async function updateAuditType(auditTypeId: string, data: TemplateInfoForm) {
	// Convert HH:mm to minutes
	const [hours, minutes] = data.expectedDuration.split(':').map(Number);
	const totalMinutes = hours * 60 + minutes;

	try {
		return await db.transaction(async (tx) => {
			// Update main audit type info
			const [result] = await tx
				.update(auditTypesTable)
				.set({
					name: data.name,
					code: data.code.toUpperCase(),
					responsiblePersonId: data.responsiblePersonId,
					expectedDuration: totalMinutes,
					repetetionPlan: data.repetetionPlan,
					active: data.active,
					updatedAt: new Date(),
					specification: data.specification ?? null
				})
				.where(eq(auditTypesTable.id, auditTypeId))
				.returning();

			// Update evaluation mode in evaluation config table
			await tx
				.update(auditTypeEvaluationConfigTable)
				.set({
					evaluationMode: data.evaluationMode,
					updatedAt: new Date()
				})
				.where(eq(auditTypeEvaluationConfigTable.auditTypeId, auditTypeId));

			return result ?? null;
		});
	} catch (err) {
		console.error('Error updating audit type:', err);
		throw new Error('Failed to update audit type');
	}
}

export async function deleteAuditType(auditTypeId: string) {
	try {
		// Get the audit type first to get its current code
		const auditType = await db
			.select({ code: auditTypesTable.code })
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId))
			.limit(1);

		if (!auditType || auditType.length === 0) {
			throw new Error('Audit type not found');
		}

		// Soft delete by prefixing the code with DELETED_
		const updatedCode = auditType[0].code.startsWith('DELETED_')
			? auditType[0].code
			: `DELETED_${auditType[0].code}`;

		const result = await db.transaction(async (tx) => {
			const [updatedAuditType] = await tx
				.update(auditTypesTable)
				.set({
					code: updatedCode,
					active: false
				})
				.where(eq(auditTypesTable.id, auditTypeId))
				.returning();

			return updatedAuditType;
		});
		return result;
	} catch (err) {
		console.error('Error soft-deleting audit type:', err);
		throw new Error('Failed to delete audit type');
	}
}

export async function duplicateAuditType(
	data: CreateAuditTypeForm,
	originalAuditId: string,
	plantId: string
) {
	try {
		const [result] = await db.transaction(async (tx) => {
			const createdAuditType = await createAuditType(data, plantId);
			if (!createdAuditType) {
				throw new Error('Failed to create duplicated audit type');
			}

			const [auditTypeQuestions] = await tx
				.select({ questions: auditTypesTable.questions })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, originalAuditId))
				.limit(1);

			if (!auditTypeQuestions) {
				throw new Error('Audit type not found');
			}

			await tx
				.update(auditTypesTable)
				.set({
					questions: auditTypeQuestions.questions
				})
				.where(eq(auditTypesTable.id, createdAuditType.id))
				.returning();

			const [originalAuditTypeEvalConfig] = await tx
				.select()
				.from(auditTypeEvaluationConfigTable)
				.where(eq(auditTypeEvaluationConfigTable.auditTypeId, originalAuditId));

			if (!originalAuditTypeEvalConfig) {
				throw new Error('Original audit type evaluation config not found');
			}

			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			const { id, auditTypeId, ...configData } = originalAuditTypeEvalConfig;

			await tx
				.update(auditTypeEvaluationConfigTable)
				.set({
					...configData
				})
				.where(eq(auditTypeEvaluationConfigTable.auditTypeId, createdAuditType.id));

			return [auditTypeQuestions];
		});
		return result;
	} catch (err) {
		console.error('Error duplicating audit type:', err);
		throw new Error('Failed to duplicate audit type');
	}
}

//Categories
//__________________________

export async function addCategory(auditTypeId: string, categoryName: string) {
	const audit = await db.select().from(auditTypesTable).where(eq(auditTypesTable.id, auditTypeId));
	if (!audit || audit.length === 0) {
		throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
	}
	try {
		const currentQuestions = { ...audit[0].questions } as TemplateQuestions;

		let maxId = 0;
		Object.keys(currentQuestions).forEach((key) => {
			const id = parseInt(key);
			if (!isNaN(id) && id > maxId) {
				maxId = id;
			}
		});

		const newId = (maxId + 1).toString();
		currentQuestions[newId] = {
			order: maxId + 1,
			name: categoryName,
			questions: []
		};

		const [result] = await db
			.update(auditTypesTable)
			.set({ questions: currentQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId))
			.returning();

		return result ?? null;
	} catch (error) {
		console.error('Error adding category:', error);
		throw new Error('Failed to add category');
	}
}

export async function editCategory(
	auditTypeId: string,
	categoryId: string,
	newCategoryName: string
) {
	const audit = await db.select().from(auditTypesTable).where(eq(auditTypesTable.id, auditTypeId));
	if (!audit || audit.length === 0) {
		throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
	}
	try {
		const currentQuestions = { ...audit[0].questions } as TemplateQuestions;

		if (!currentQuestions[categoryId]) {
			throw new Error(t.get('errors.audits.auditSetup.categoryNotFound'));
		}

		currentQuestions[categoryId] = {
			...currentQuestions[categoryId],
			name: newCategoryName
		};

		const [result] = await db
			.update(auditTypesTable)
			.set({ questions: currentQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId))
			.returning();

		return result ?? null;
	} catch (error) {
		console.error('Error editing category:', error);
		throw new Error('Failed to edit category');
	}
}

export async function deleteCategory(auditTypeId: string, categoryId: string) {
	const audit = await db.select().from(auditTypesTable).where(eq(auditTypesTable.id, auditTypeId));
	if (!audit || audit.length === 0) {
		throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
	}
	try {
		const currentQuestions = { ...audit[0].questions } as TemplateQuestions;

		if (!currentQuestions[categoryId]) {
			throw new Error(t.get('errors.audits.auditSetup.categoryNotFound'));
		}

		delete currentQuestions[categoryId];

		const sortedCategories = Object.entries(currentQuestions).sort(([keyA], [keyB]) => {
			const idA = parseInt(keyA);
			const idB = parseInt(keyB);
			return idA - idB;
		});

		const updatedQuestions: TemplateQuestions = {};
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		sortedCategories.forEach(([_, category], index) => {
			updatedQuestions[(index + 1).toString()] = category;
		});

		const [result] = await db
			.update(auditTypesTable)
			.set({ questions: updatedQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId))
			.returning();

		return result ?? null;
	} catch (error) {
		console.error('Error deleting category:', error);
		throw new Error(t.get('errors.audits.auditSetup.failedDeleteCategory'));
	}
}

//Questions
//__________________________

export async function addQuestion(auditId: string, categoryId: string, question: TemplateQuestion) {
	try {
		const newQuestion: TemplateQuestion = question;

		const [result] = await db.transaction(async (tx) => {
			const [template] = await tx
				.select({ questions: auditTypesTable.questions })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, auditId));

			if (!template) {
				throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
			}

			const updatedQuestions = { ...template.questions } as TemplateQuestions;

			if (!updatedQuestions[categoryId]) {
				throw new Error(t.get('errors.audits.auditSetup.categoryNotFound'));
			}

			updatedQuestions[categoryId].questions.push(newQuestion);

			return await tx
				.update(auditTypesTable)
				.set({
					questions: updatedQuestions,
					updatedAt: new Date()
				})
				.where(eq(auditTypesTable.id, auditId))
				.returning();
		});

		return result;
	} catch (err) {
		console.error('Error adding question to audit type:', err);
		throw new Error('Failed to add question');
	}
}

export async function deleteQuestion(auditId: string, questionId: string) {
	try {
		const [result] = await db.transaction(async (tx) => {
			const [template] = await tx
				.select({ questions: auditTypesTable.questions })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, auditId));

			if (!template) {
				throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
			}

			const updatedQuestions = { ...template.questions } as TemplateQuestions;

			let categoryId: string | null = null;
			let questionIndex = -1;

			for (const [id, category] of Object.entries(updatedQuestions)) {
				const index = category.questions.findIndex(
					(q: TemplateQuestion) => q.questionId === questionId
				);
				if (index !== -1) {
					categoryId = id;
					questionIndex = index;
					break;
				}
			}

			if (!categoryId || questionIndex === -1) {
				throw new Error(t.get('errors.audits.services.quesitonNotFound'));
			}

			updatedQuestions[categoryId].questions.splice(questionIndex, 1);

			return await tx
				.update(auditTypesTable)
				.set({
					questions: updatedQuestions,
					updatedAt: new Date()
				})
				.where(eq(auditTypesTable.id, auditId))
				.returning();
		});

		return result;
	} catch (err) {
		console.error('Error deleting question from audit type:', err);
		throw new Error('Failed to delete question');
	}
}

export async function editQuestion(
	auditId: string,
	questionId: string,
	updates: Partial<TemplateQuestion>
) {
	try {
		const [result] = await db.transaction(async (tx) => {
			const [template] = await tx
				.select({ questions: auditTypesTable.questions })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, auditId));

			if (!template) {
				throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
			}

			const updatedQuestions = { ...template.questions } as TemplateQuestions;

			let categoryId: string | null = null;
			let questionIndex = -1;

			for (const [id, category] of Object.entries(updatedQuestions)) {
				const index = category.questions.findIndex(
					(q: TemplateQuestion) => q.questionId === questionId
				);
				if (index !== -1) {
					categoryId = id;
					questionIndex = index;
					break;
				}
			}

			if (!categoryId || questionIndex === -1) {
				throw new Error(t.get('errors.audits.services.quesitonNotFound'));
			}

			updatedQuestions[categoryId].questions[questionIndex] = {
				...updatedQuestions[categoryId].questions[questionIndex],
				...updates
			};

			return await tx
				.update(auditTypesTable)
				.set({
					questions: updatedQuestions,
					updatedAt: new Date()
				})
				.where(eq(auditTypesTable.id, auditId))
				.returning();
		});

		return result;
	} catch (err) {
		console.error('Error editing question in audit type:', err);
		throw new Error('Failed to edit question');
	}
}

export async function updateQuestionOrder(
	auditTypeId: string,
	categoryId: string,
	questions: TemplateQuestion[]
) {
	try {
		const [audit] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!audit) {
			throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
		}

		const currentQuestions = { ...audit.questions } as TemplateQuestions;

		if (!currentQuestions[categoryId]) {
			throw new Error(t.get('errors.audits.auditSetup.categoryNotFound'));
		}

		const questionsWithOrder = questions.map((question, index) => ({
			...question,
			order: index
		}));

		currentQuestions[categoryId].questions = questionsWithOrder;

		const [result] = await db
			.update(auditTypesTable)
			.set({ questions: currentQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId))
			.returning();

		return result ?? null;
	} catch (error) {
		console.error('Error updating question order:', error);
		throw new Error('Failed to update question order');
	}
}

export async function updateCategoryOrder(auditTypeId: string, categoryIds: string[]) {
	try {
		const [audit] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!audit) {
			throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
		}

		const currentQuestions = { ...audit.questions } as TemplateQuestions;
		const updatedQuestions: TemplateQuestions = {};

		categoryIds.forEach((categoryId, index) => {
			const newCategoryId = (index + 1).toString();

			if (currentQuestions[categoryId]) {
				updatedQuestions[newCategoryId] = currentQuestions[categoryId];
			}
		});

		const [result] = await db
			.update(auditTypesTable)
			.set({
				questions: updatedQuestions,
				updatedAt: new Date()
			})
			.where(eq(auditTypesTable.id, auditTypeId))
			.returning();

		return result ?? null;
	} catch (error) {
		console.error('Error updating category order:', error);
		throw new Error('Failed to update category order');
	}
}

export async function reorderQuestions(
	auditId: string,
	categoryId: string,
	oldIndex: number,
	newIndex: number
) {
	try {
		const [result] = await db.transaction(async (tx) => {
			const [template] = await tx
				.select({ questions: auditTypesTable.questions })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, auditId));

			if (!template) {
				throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
			}

			const updatedQuestions = { ...template.questions } as TemplateQuestions;

			if (!updatedQuestions[categoryId]) {
				throw new Error(t.get('errors.audits.auditSetup.categoryNotFound'));
			}

			const questions = updatedQuestions[categoryId].questions;
			const [movedQuestion] = questions.splice(oldIndex, 1);
			questions.splice(newIndex, 0, movedQuestion);

			return await tx
				.update(auditTypesTable)
				.set({
					questions: updatedQuestions,
					updatedAt: new Date()
				})
				.where(eq(auditTypesTable.id, auditId))
				.returning();
		});

		return result;
	} catch (err) {
		console.error('Error reordering questions:', err);
		throw new Error('Failed to reorder questions');
	}
}

export async function reorderCategories(auditId: string, oldIndex: number, newIndex: number) {
	try {
		const [result] = await db.transaction(async (tx) => {
			const [template] = await tx
				.select({ questions: auditTypesTable.questions })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, auditId));

			if (!template) {
				throw new Error(t.get('errors.audits.auditSetup.auditNotFound'));
			}

			const currentQuestions = { ...template.questions } as TemplateQuestions;

			const categories = Object.keys(currentQuestions).sort((a, b) => {
				return parseInt(a) - parseInt(b);
			});

			const [movedCategory] = categories.splice(oldIndex, 1);
			categories.splice(newIndex, 0, movedCategory);

			const updatedQuestions: TemplateQuestions = {};
			categories.forEach((categoryId, index) => {
				const newCategoryId = (index + 1).toString();
				if (currentQuestions[categoryId]) {
					updatedQuestions[newCategoryId] = currentQuestions[categoryId];
				}
			});

			return await tx
				.update(auditTypesTable)
				.set({
					questions: updatedQuestions,
					updatedAt: new Date()
				})
				.where(eq(auditTypesTable.id, auditId))
				.returning();
		});

		return result;
	} catch (err) {
		console.error('Error reordering categories:', err);
		throw new Error('Failed to reorder categories');
	}
}
