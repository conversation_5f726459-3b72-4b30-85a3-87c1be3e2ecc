import {
	addQuestion,
	createAuditType,
	deleteAuditType,
	deleteQuestion,
	updateAuditType,
	getAuditTypeById,
	getAuditTypes,
	addCategory,
	editQuestion,
	deleteCategory,
	editCategory,
	updateCategoryOrder,
	updateQuestionOrder,
	reorderQuestions,
	reorderCategories,
	duplicateAuditType
} from './auditTypes';

export const AuditTypeService = {
	getAuditTypes,
	getAuditTypeById,
	createAuditType,
	updateAuditType,
	deleteAuditType,
	duplicateAuditType,

	//categories
	addCategory,
	editCategory,
	deleteCategory,
	//quesions
	addQuestion,
	editQuestion,
	deleteQuestion,
	updateQuestionOrder,
	updateCategoryOrder,
	reorderQuestions,
	reorderCategories
};
