import { db } from '$lib/db/db.server';
import { usersTable } from '$lib/db/schema/user';
import { userRolePlantsTable } from '$lib/db/schema/roles';
import type { AuditorDTO } from '$lib/DTO/auditor';
import { eq, and } from 'drizzle-orm';

export async function getAuditors(plantId?: string): Promise<AuditorDTO[]> {
	try {
		let result;

		if (plantId) {
			// Filter auditors who have access to the specified plant via roles
			result = await db
				.select({
					id: usersTable.id,
					name: {
						firstName: usersTable.firstName,
						lastName: usersTable.lastName
					},
					email: usersTable.email
				})
				.from(usersTable)
				.innerJoin(userRolePlantsTable, eq(usersTable.id, userRolePlantsTable.userId))
				.where(and(eq(userRolePlantsTable.plantId, plantId), eq(usersTable.active, true)))
				.groupBy(usersTable.id, usersTable.firstName, usersTable.lastName, usersTable.email);
		} else {
			// If no user or no tenants, return all auditors (fallback for admin or system calls)
			result = await db
				.select({
					id: usersTable.id,
					name: {
						firstName: usersTable.firstName,
						lastName: usersTable.lastName
					},
					email: usersTable.email
				})
				.from(usersTable);
		}

		if (!result.length) {
			return [];
		}

		return result;
	} catch (error) {
		console.error('Error fetching auditors:', error);
		throw new Error('Failed to fetch auditors');
	}
}

export async function getAuditorById(auditorId: string): Promise<AuditorDTO | null> {
	try {
		const [result] = await db
			.select({
				id: usersTable.id,
				name: {
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				email: usersTable.email
			})
			.from(usersTable)
			.where(eq(usersTable.id, auditorId));

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error('Error fetching auditor:', error);
		throw new Error('Failed to fetch auditor');
	}
}

export async function updateAuditor(
	auditorId: string,
	data: { firstName: string; lastName: string }
): Promise<AuditorDTO | null> {
	try {
		const [result] = await db
			.update(usersTable)
			.set({
				firstName: data.firstName,
				lastName: data.lastName
			})
			.where(eq(usersTable.id, auditorId))
			.returning({
				id: usersTable.id,
				name: {
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				email: usersTable.email
			});

		return result ?? null;
	} catch (error) {
		console.error('Error updating auditor:', error);
		throw new Error('Failed to update auditor');
	}
}
