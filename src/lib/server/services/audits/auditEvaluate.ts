import { db } from '$lib/db/db.server';
import {
	auditInstancesTable,
	auditAnswersTable,
	auditTypesTable,
	questionsTable,
	answersAttachmentsTable,
	auditInstanceEvaluationConfigTable
} from '$lib/db/schema/audits';
import { and, eq, inArray } from 'drizzle-orm';
import type { AuditAnswers } from '../../../schemas/audits/auditsAnswers';
import { workplaceTable } from '$lib/db/schema/workplace';
import { usersTable } from '$lib/db/schema/user';
import { t } from '$lib/translations';
import type { AuditEvaluateDTO } from '$lib/DTO/audits/auditEvaluate';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
import { deleteFile } from '$lib/server/storage';
import { normalizeEvaluationValue } from '../scoreNormalization/score';
import { PlantEvaluationConfigService } from '../tenants';
import { ScoreService } from '../scoreNormalization';
import { AuditScoreService } from '$lib/utils/auditScore';
import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';

export async function getEvaluationQuestions(auditId: string): Promise<AuditEvaluateDTO[]> {
	try {
		const result = await db
			.select({
				id: auditInstancesTable.id,
				code: auditInstancesTable.code,
				workplace: {
					id: workplaceTable.id,
					name: workplaceTable.name
				},
				workplaceId: auditInstancesTable.workplaceId,
				auditType: {
					id: auditTypesTable.id,
					name: auditTypesTable.name
				},
				responsiblePerson: {
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				auditTypeResponsiblePersonId: auditTypesTable.responsiblePersonId,
				expectedDuration: auditTypesTable.expectedDuration,
				realDuration: auditInstancesTable.realDuration,
				plannedEvaluationDate: auditInstancesTable.plannedDate,
				completionDate: auditInstancesTable.completionDate,
				questions: auditInstancesTable.questions
			})
			.from(auditInstancesTable)
			.leftJoin(workplaceTable, eq(auditInstancesTable.workplaceId, workplaceTable.id))
			.leftJoin(auditTypesTable, eq(auditInstancesTable.auditTypeId, auditTypesTable.id))
			.leftJoin(usersTable, eq(auditInstancesTable.responsiblePersonId, usersTable.id))
			.where(eq(auditInstancesTable.id, auditId));

		const answers = await getAuditAnswers(auditId);

		const enrichedResult = await Promise.all(
			result.map(async (item) => {
				let auditTypeResponsiblePerson = null;
				if (item.auditTypeResponsiblePersonId) {
					const [personResult] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName
						})
						.from(usersTable)
						.where(eq(usersTable.id, item.auditTypeResponsiblePersonId))
						.limit(1);

					auditTypeResponsiblePerson = personResult || null;
				}

				let workplaceResponsiblePerson = null;
				if (item.workplaceId) {
					const [workplace] = await db
						.select({
							responsiblePersonId: workplaceTable.responsiblePersonId
						})
						.from(workplaceTable)
						.where(eq(workplaceTable.id, item.workplaceId))
						.limit(1);

					if (workplace && workplace.responsiblePersonId) {
						// load workplace responsible person info
						const [personResult] = await db
							.select({
								id: usersTable.id,
								firstName: usersTable.firstName,
								lastName: usersTable.lastName
							})
							.from(usersTable)
							.where(eq(usersTable.id, workplace.responsiblePersonId))
							.limit(1);

						workplaceResponsiblePerson = personResult || null;
					}
				}

				return {
					...item,
					auditTypeResponsiblePerson,
					workplaceResponsiblePerson
				};
			})
		);

		const questionIds = [];
		for (const audit of enrichedResult) {
			const questions = audit.questions as TemplateQuestions;
			for (const category of Object.values(questions)) {
				for (const question of category.questions) {
					questionIds.push(question.questionId);
				}
			}
		}

		const questionDetails = await db
			.select({
				id: questionsTable.id,
				text: questionsTable.text,
				subtext: questionsTable.subtext,
				evaluationType: questionsTable.evaluationType
			})
			.from(questionsTable)
			.where(inArray(questionsTable.id, questionIds));

		const questionsMap: Record<string, (typeof questionDetails)[0]> = questionDetails.reduce(
			(acc, q) => {
				acc[q.id] = q;
				return acc;
			},
			{} as Record<string, (typeof questionDetails)[0]>
		);

		const resultWithProgress = enrichedResult.map((item) => {
			const progress = calculateAuditProgress(
				item.questions as TemplateQuestions,
				answers,
				item.plannedEvaluationDate,
				item.completionDate
			);

			const questionsWithDetails = { ...item.questions };
			for (const categoryId in questionsWithDetails) {
				const category = questionsWithDetails[categoryId];
				category.questions = category.questions.map((q) => {
					const questionDetails = questionsMap[q.questionId];
					if (!questionDetails) {
						console.warn(`Question details not found for ID: ${q.questionId}`);
						return q;
					}

					return {
						questionId: q.questionId,
						required: q.required,
						id: questionDetails.id,
						text: questionDetails.text,
						subtext: questionDetails.subtext,
						evaluationType: questionDetails.evaluationType
					};
				});
			}

			const { ...restItem } = item;

			return {
				...restItem,
				progress,
				expectedDuration: item.expectedDuration ?? undefined,
				questions: questionsWithDetails
			} as AuditEvaluateDTO;
		});

		return resultWithProgress;
	} catch (error) {
		console.error(error);
		throw new Error(t.get('errors.audits.services.failedToFetchAudit'));
	}
}

async function processAnswers(auditId: string, answers: AuditAnswers, plantId: string) {
	const [auditInstance] = await db
		.select({ evaluationConfigId: auditInstancesTable.evaluationConfigId })
		.from(auditInstancesTable)
		.where(eq(auditInstancesTable.id, auditId))
		.limit(1);

	let evaluationConfig = null;
	if (auditInstance?.evaluationConfigId) {
		const [config] = await db
			.select()
			.from(auditInstanceEvaluationConfigTable)
			.where(eq(auditInstanceEvaluationConfigTable.id, auditInstance.evaluationConfigId));
		evaluationConfig = config;
	}

	// Fallback to plant config if audit instance has no config
	if (!evaluationConfig) {
		evaluationConfig = await PlantEvaluationConfigService.getPlantEvaluationConfig(plantId);
	}

	const results = [];
	for (const [questionId, answer] of Object.entries(answers)) {
		const [questionInfo] = await db
			.select({ evaluationType: questionsTable.evaluationType })
			.from(questionsTable)
			.where(eq(questionsTable.id, questionId))
			.limit(1);

		const normalizedValue = questionInfo?.evaluationType
			? normalizeEvaluationValue(
					String(answer.evaluationValue || ''),
					questionInfo.evaluationType,
					evaluationConfig
				)
			: 0;

		const existingAnswer = await db
			.select()
			.from(auditAnswersTable)
			.where(
				and(eq(auditAnswersTable.auditId, auditId), eq(auditAnswersTable.questionId, questionId))
			)
			.limit(1);

		if (existingAnswer.length > 0) {
			const answerId = existingAnswer[0].id;

			const updated = await db
				.update(auditAnswersTable)
				.set({
					evaluationValue: String(answer.evaluationValue || ''),
					note: answer.note || '',
					normalizedValue: String(normalizedValue ?? 0)
				})
				.where(
					and(eq(auditAnswersTable.auditId, auditId), eq(auditAnswersTable.questionId, questionId))
				)
				.returning();

			await db
				.delete(answersAttachmentsTable)
				.where(eq(answersAttachmentsTable.answerId, answerId));

			if (answer.files && answer.files.length > 0) {
				await db.insert(answersAttachmentsTable).values(
					answer.files.map((file) => ({
						answerId,
						url: file.url,
						filename: file.filename,
						type: file.type
					}))
				);
			}

			results.push(...updated);
		} else {
			const [newAnswer] = await db
				.insert(auditAnswersTable)
				.values({
					auditId,
					questionId,
					evaluationValue: String(answer.evaluationValue || ''),
					note: answer.note || '',
					normalizedValue: String(normalizedValue ?? 0)
				})
				.returning();

			if (answer.files && answer.files.length > 0) {
				await db.insert(answersAttachmentsTable).values(
					answer.files.map((file) => ({
						answerId: newAnswer.id,
						url: file.url,
						filename: file.filename,
						type: file.type
					}))
				);
			}

			results.push(newAnswer);
		}
	}
	return results;
}

export async function updateAuditProgress(
	auditId: string,
	answers: AuditAnswers,
	realDuration?: number,
	shouldComplete = false
) {
	try {
		const [result, progress] = await db.transaction(async (tx) => {
			const [audit] = await tx
				.select({
					questions: auditInstancesTable.questions,
					plannedDate: auditInstancesTable.plannedDate,
					completionDate: auditInstancesTable.completionDate,
					plantId: auditTypesTable.plantId
				})
				.from(auditInstancesTable)
				.leftJoin(auditTypesTable, eq(auditInstancesTable.auditTypeId, auditTypesTable.id))
				.where(eq(auditInstancesTable.id, auditId))
				.limit(1);

			if (!audit) {
				throw new Error(t.get('errors.audits.services.auditNotFound'));
			}

			if (shouldComplete) {
				const questions = audit.questions as TemplateQuestions;
				const allQuestions = Object.values(questions)
					.flatMap((category) => category?.questions || [])
					.filter((q) => q.required);

				const unansweredQuestions = allQuestions.filter(
					(q) =>
						!answers[q.questionId] ||
						answers[q.questionId].evaluationValue === null ||
						answers[q.questionId].evaluationValue === undefined
				);

				if (unansweredQuestions.length > 0) {
					console.error('Unanswered required questions:', unansweredQuestions);
					throw new Error(t.get('errors.audits.services.requiredQuestionsNotAnswered'));
				}

				if (realDuration !== undefined) {
					await tx
						.update(auditInstancesTable)
						.set({
							completionDate: new Date().toISOString(),
							realDuration
						})
						.where(eq(auditInstancesTable.id, auditId));
				}
			}

			const calculatedProgress = calculateAuditProgress(
				audit.questions as TemplateQuestions,
				answers,
				audit.plannedDate,
				audit.completionDate
			);

			const results = await processAnswers(auditId, answers, audit.plantId!);

			return [results, calculatedProgress];
		});

		return { result: result ?? null, progress };
	} catch (error) {
		console.error('Error evaluating audit:', error);
		throw new Error(t.get('errors.audits.services.failedToEvaluate'));
	}
}

async function calculateRulesResult(
	auditId: string,
	evaluationConfig: {
		evaluationMode: string;
		average_averageAuditAnswers?: number | null;
		average_badAuditAnswers?: number | null;
		notSuccessful_averageAnswers?: number | null;
		notSuccessful_badAnswers?: number | null;
	}
): Promise<string> {
	try {
		const answersWithQuestions = await db
			.select({
				id: auditAnswersTable.id,
				questionId: auditAnswersTable.questionId,
				evaluationValue: auditAnswersTable.evaluationValue,
				note: auditAnswersTable.note,
				questionText: questionsTable.text,
				questionSubtext: questionsTable.subtext,
				evaluationType: questionsTable.evaluationType
			})
			.from(auditAnswersTable)
			.innerJoin(questionsTable, eq(auditAnswersTable.questionId, questionsTable.id))
			.where(eq(auditAnswersTable.auditId, auditId));

		// Get audit type to find plant ID
		const [auditInfo] = await db
			.select({
				plantId: auditTypesTable.plantId
			})
			.from(auditInstancesTable)
			.innerJoin(auditTypesTable, eq(auditInstancesTable.auditTypeId, auditTypesTable.id))
			.where(eq(auditInstancesTable.id, auditId));

		if (!auditInfo?.plantId) {
			throw new Error('Plant ID not found for audit');
		}

		// Get plant evaluation config
		const plantConfig = await PlantEvaluationConfigService.getPlantEvaluationConfig(
			auditInfo.plantId
		);

		if (!plantConfig) {
			throw new Error('Plant evaluation config not found');
		}

		// Convert to AuditAnswerResultsDTO format (without files for rules calculation)
		const auditAnswers: AuditAnswerResultsDTO[] = answersWithQuestions.map((answer) => ({
			id: answer.id,
			questionId: answer.questionId,
			questionText: answer.questionText,
			questionSubtext: answer.questionSubtext,
			evaluationValue: answer.evaluationValue || '',
			note: answer.note,
			required: false,
			files: [],
			evaluationType: answer.evaluationType || 'text'
		}));

		const result = AuditScoreService.getAuditResultColorByRules(
			auditAnswers,
			evaluationConfig,
			plantConfig
		);

		return result;
	} catch (error) {
		console.error('Error calculating rules result:', error);

		return 'green';
	}
}

export async function completeAudit(auditId: string, answers: AuditAnswers, realDuration: number) {
	try {
		const result = await db.transaction(async (tx) => {
			const updateResult = await updateAuditProgress(auditId, answers, realDuration, true);

			// Get audit with evaluation config
			const [audit] = await tx
				.select({
					questions: auditInstancesTable.questions,
					evaluationConfigId: auditInstancesTable.evaluationConfigId
				})
				.from(auditInstancesTable)
				.where(eq(auditInstancesTable.id, auditId))
				.limit(1);

			if (!audit) {
				throw new Error('Audit not found');
			}

			// Calculate success rate (always calculate for both modes)
			const successRateValue = await ScoreService.computeSuccessRateOfAudit(auditId);
			const successRate = isNaN(successRateValue) ? 0 : Math.round(successRateValue);

			// Calculate rules result if in rules mode
			let rulesResult: string | null = null;
			if (audit.evaluationConfigId) {
				const [evaluationConfig] = await tx
					.select({
						evaluationMode: auditInstanceEvaluationConfigTable.evaluationMode,
						average_averageAuditAnswers:
							auditInstanceEvaluationConfigTable.average_averageAuditAnswers,
						average_badAuditAnswers:
							auditInstanceEvaluationConfigTable.average_badAuditAnswers,
						notSuccessful_averageAnswers:
							auditInstanceEvaluationConfigTable.notSuccessful_averageAnswers,
						notSuccessful_badAnswers: auditInstanceEvaluationConfigTable.notSuccessful_badAnswers
					})
					.from(auditInstanceEvaluationConfigTable)
					.where(eq(auditInstanceEvaluationConfigTable.id, audit.evaluationConfigId));

				if (evaluationConfig && evaluationConfig.evaluationMode === 'rules') {
					rulesResult = await calculateRulesResult(auditId, evaluationConfig);
				}
			}

			await tx
				.update(auditInstancesTable)
				.set({
					completionDate: new Date().toISOString(),
					successRate: successRate,
					rulesResult: rulesResult
				})
				.where(eq(auditInstancesTable.id, auditId));

			return updateResult.result;
		});

		return result;
	} catch (error) {
		console.error('Error completing audit:', error);
		throw new Error(t.get('errors.audits.services.failedToComplete'));
	}
}

export async function saveQuestionAnswer(
	auditId: string,
	questionId: string,
	value: string | null,
	note: string,
	realDuration: number,
	files: Array<{ url: string; filename: string; type: string }>,
	filesToRemove: string[] = []
) {
	try {
		const result = await db.transaction(async (tx) => {
			const [audit] = await tx
				.select({
					plantId: auditTypesTable.plantId,
					evaluationConfigId: auditInstancesTable.evaluationConfigId
				})
				.from(auditInstancesTable)
				.leftJoin(auditTypesTable, eq(auditInstancesTable.auditTypeId, auditTypesTable.id))
				.where(eq(auditInstancesTable.id, auditId));

			if (!audit) {
				throw new Error('Audit not found');
			}

			// Get question info for evaluation type
			const [questionInfo] = await tx
				.select({ evaluationType: questionsTable.evaluationType })
				.from(questionsTable)
				.where(eq(questionsTable.id, questionId))
				.limit(1);

			// First try to get audit instance evaluation config
			let evaluationConfig = null;
			if (audit.evaluationConfigId) {
				const [config] = await tx
					.select()
					.from(auditInstanceEvaluationConfigTable)
					.where(eq(auditInstanceEvaluationConfigTable.id, audit.evaluationConfigId));
				evaluationConfig = config;
			}

			// Fallback to plant config if no audit-specific config
			if (!evaluationConfig) {
				evaluationConfig = await PlantEvaluationConfigService.getPlantEvaluationConfig(
					audit.plantId!
				);
			}

			// Calculate normalized value
			const normalizedValue =
				questionInfo?.evaluationType && value
					? normalizeEvaluationValue(value, questionInfo.evaluationType, evaluationConfig)
					: 0;

			const existingAnswer = await tx
				.select()
				.from(auditAnswersTable)
				.where(
					and(eq(auditAnswersTable.auditId, auditId), eq(auditAnswersTable.questionId, questionId))
				);

			let answerId: string;

			if (existingAnswer.length > 0) {
				answerId = existingAnswer[0].id;

				// Prepare update data - only update evaluation value if it's provided
				const updateData: {
					note: string;
					normalizedValue: string;
					evaluationValue?: string;
				} = {
					note: note || '',
					normalizedValue: String(normalizedValue || 0)
				};

				// Only update evaluation value if it's explicitly provided (not null)
				if (value !== null) {
					updateData.evaluationValue = value;
				}

				await tx
					.update(auditAnswersTable)
					.set(updateData)
					.where(
						and(
							eq(auditAnswersTable.auditId, auditId),
							eq(auditAnswersTable.questionId, questionId)
						)
					);

				if (filesToRemove.length > 0) {
					for (const fileUrl of filesToRemove) {
						await tx
							.delete(answersAttachmentsTable)
							.where(
								and(
									eq(answersAttachmentsTable.answerId, answerId),
									eq(answersAttachmentsTable.url, fileUrl)
								)
							);

						try {
							await deleteFile(fileUrl);
						} catch (error) {
							console.error(`Failed to delete physical file ${fileUrl}:`, error);
						}
					}
				}
			} else {
				const [newAnswer] = await tx
					.insert(auditAnswersTable)
					.values({
						auditId,
						questionId,
						evaluationValue: value === null ? 'NotVisited' : value,
						normalizedValue: String(normalizedValue || 0),
						note: note || ''
					})
					.returning();

				answerId = newAnswer.id;
			}

			// add attachments
			if (files.length > 0) {
				// Načti existující přílohy pro tuto odpověď
				const existingAttachments = await tx
					.select({ url: answersAttachmentsTable.url })
					.from(answersAttachmentsTable)
					.where(eq(answersAttachmentsTable.answerId, answerId));
				const existingUrls = new Set(existingAttachments.map((a) => a.url));

				// Filtrování pouze nových příloh
				const newFiles = files.filter((file) => !existingUrls.has(file.url));

				if (newFiles.length > 0) {
					await tx.insert(answersAttachmentsTable).values(
						newFiles.map((file) => ({
							answerId,
							url: file.url,
							filename: file.filename,
							type: file.type
						}))
					);
				}
			}

			//update audit duration
			await tx
				.update(auditInstancesTable)
				.set({ realDuration })
				.where(eq(auditInstancesTable.id, auditId));

			//load updated attachments
			const updatedAttachments = await tx
				.select()
				.from(answersAttachmentsTable)
				.where(eq(answersAttachmentsTable.answerId, answerId));

			return {
				success: true,
				files: updatedAttachments.map((attachment) => ({
					id: attachment.id,
					url: attachment.url,
					filename: attachment.filename,
					type: attachment.type
				}))
			};
		});

		return result;
	} catch (error) {
		console.error('saveQuestionAnswer - error:', error);
		throw error;
	}
}

export async function getAuditAnswers(auditId: string) {
	try {
		const answers = await db
			.select()
			.from(auditAnswersTable)
			.where(eq(auditAnswersTable.auditId, auditId));

		if (answers.length === 0) {
			return {} as AuditAnswers;
		}

		const answerIds = answers.map((answer) => answer.id);

		const attachments = await db
			.select()
			.from(answersAttachmentsTable)
			.where(inArray(answersAttachmentsTable.answerId, answerIds));

		const answersWithFiles = answers.map((answer) => ({
			...answer,
			files: attachments
				.filter((attachment) => attachment.answerId === answer.id)
				.map((attachment) => ({
					id: attachment.id,
					url: attachment.url,
					filename: attachment.filename,
					type: attachment.type
				}))
		}));

		return answersWithFiles.reduce((acc, answer) => {
			acc[answer.questionId!] = {
				questionId: answer.questionId,
				auditId: answer.auditId,
				evaluationValue: answer.evaluationValue,
				note: answer.note || '',
				files: answer.files
			};
			return acc;
		}, {} as AuditAnswers);
	} catch (error) {
		console.error('getAuditAnswers - error:', error);
		throw error;
	}
}

export async function getAuditAnswerForQuestion(auditId: string, questionId: string) {
	try {
		const [answer] = await db
			.select()
			.from(auditAnswersTable)
			.where(
				and(eq(auditAnswersTable.auditId, auditId), eq(auditAnswersTable.questionId, questionId))
			);

		if (!answer) return null;

		const attachments = await db
			.select()
			.from(answersAttachmentsTable)
			.where(eq(answersAttachmentsTable.answerId, answer.id));

		return {
			answerId: answer.id,
			files: attachments.map((attachment) => ({
				id: attachment.id,
				url: attachment.url,
				filename: attachment.filename,
				type: attachment.type
			}))
		};
	} catch (error) {
		console.error('getAuditAnswerForQuestion - error:', error);
		throw error;
	}
}

export function calculateProgress(questions: TemplateQuestions, answers: AuditAnswers): number {
	if (!questions) return 0;

	const allQuestions = Object.values(questions).flatMap((category) => category?.questions || []);

	const totalQuestions = allQuestions.length;

	if (totalQuestions === 0) return 0;

	if (!answers) return 0;

	const answeredCount = Object.entries(answers).filter(([questionId, answer]) => {
		const question = allQuestions.find((q) => q && q.questionId === questionId);
		return (
			question &&
			answer?.evaluationValue !== null &&
			answer?.evaluationValue !== undefined &&
			answer?.evaluationValue !== 'NotVisited'
		);
	}).length;

	return Math.round((answeredCount / totalQuestions) * 100);
}

export function calculateAuditProgress(
	questions: TemplateQuestions,
	answers: AuditAnswers,
	plannedDate: string | Date,
	completionDate: string | Date | null
): number {
	if (answers && Object.keys(answers).length > 0) {
		return calculateProgress(questions, answers);
	}

	const now = new Date();
	const planned = new Date(plannedDate);

	now.setHours(0, 0, 0, 0);
	planned.setHours(0, 0, 0, 0);

	if (!completionDate && planned < now) {
		return -1; //Late
	}

	return 0;
}

export async function deleteAttachment(auditId: string, questionId: string, attachmentId: string) {
	try {
		const result = await db.transaction(async (tx) => {
			const [attachment] = await tx
				.select({
					id: answersAttachmentsTable.id,
					url: answersAttachmentsTable.url,
					answerId: answersAttachmentsTable.answerId
				})
				.from(answersAttachmentsTable)
				.innerJoin(auditAnswersTable, eq(answersAttachmentsTable.answerId, auditAnswersTable.id))
				.where(
					and(
						eq(answersAttachmentsTable.id, attachmentId),
						eq(auditAnswersTable.auditId, auditId),
						eq(auditAnswersTable.questionId, questionId)
					)
				);

			if (!attachment) {
				throw new Error('Attachment not found or does not belong to this question');
			}

			// Delete from DB
			await tx.delete(answersAttachmentsTable).where(eq(answersAttachmentsTable.id, attachmentId));

			try {
				await deleteFile(attachment.url);
			} catch (error) {
				console.error(`Failed to delete physical file ${attachment.url}:`, error);
			}

			return { success: true };
		});

		return result;
	} catch (error) {
		console.error('deleteAttachment - error:', error);
		throw error;
	}
}
