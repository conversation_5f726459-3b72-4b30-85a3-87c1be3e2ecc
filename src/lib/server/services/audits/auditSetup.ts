import { db } from '$lib/db/db.server';
import { auditInstancesTable } from '$lib/db/schema/audits';
import { usersTable, userPlantsTable } from '$lib/db/schema/user';
import type { CreateAuditForm } from '$lib/schemas/audits/audits';
import { eq } from 'drizzle-orm';
import type { AuditorDTO } from '$lib/DTO/auditor';

export async function getAuditors(plantId: string): Promise<AuditorDTO[]> {
	try {
		let result;

		if (plantId) {
			// Filter auditors who have access to the same plants as the current user
			result = await db
				.select({
					id: usersTable.id,
					name: {
						firstName: usersTable.firstName,
						lastName: usersTable.lastName
					},
					email: usersTable.email
				})
				.from(usersTable)
				.innerJoin(userPlantsTable, eq(usersTable.id, userPlantsTable.userId))
				.where(eq(userPlantsTable.plantId, plantId))
				.groupBy(usersTable.id, usersTable.firstName, usersTable.lastName, usersTable.email);
		} else {
			// If no user or no tenants, return all auditors (fallback for admin or system calls)
			result = await db
				.select({
					id: usersTable.id,
					name: {
						firstName: usersTable.firstName,
						lastName: usersTable.lastName
					},
					email: usersTable.email
				})
				.from(usersTable);
		}

		if (!result.length) {
			return [];
		}

		return result;
	} catch (error) {
		console.error('Error fetching auditors:', error);
		throw new Error('Failed to fetch auditors');
	}
}

export async function getAuditorById(auditorId: string): Promise<AuditorDTO | null> {
	try {
		const [result] = await db
			.select({
				id: usersTable.id,
				name: {
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				email: usersTable.email
			})
			.from(usersTable)
			.where(eq(usersTable.id, auditorId));

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error('Error fetching auditor:', error);
		throw new Error('Failed to fetch auditor');
	}
}

export async function updateAuditInstance(auditId: string, data: CreateAuditForm) {
	try {
		return await db
			.update(auditInstancesTable)
			.set({
				workplaceId: data.workplaceId,
				responsiblePersonId: data.responsiblePersonId,
				auditTypeId: data.auditTypeId,
				plannedDate: data.plannedDate
			})
			.where(eq(auditInstancesTable.id, auditId))
			.returning();
	} catch (err) {
		console.error('Error updating audit instance:', err);
		throw new Error('Failed to update audit instance');
	}
}
