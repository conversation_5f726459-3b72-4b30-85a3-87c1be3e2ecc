import { db } from '$lib/db/db.server';
import {
	auditInstanceEvaluationConfigTable,
	auditInstancesTable,
	auditTypesTable,
	auditTypeEvaluationConfigTable
} from '$lib/db/schema/audits';
import { usersTable } from '$lib/db/schema/user';
import { generateAuditCode } from '$lib/utils/codeGenerator';
import { eq, and, not, like, desc, gt } from 'drizzle-orm';
import { workplaceTable } from '$lib/db/schema/workplace';
import { plantsTable, plantsEvaluationConfigurationTable } from '$lib/db/schema/company';
import { t } from '$lib/translations';
import type { CreateAuditForm } from '$lib/schemas/audits/audits';
import type { AuditDefaultValues, AuditDTO } from '$lib/DTO/audits/audits';
import { getAuditAnswers, calculateAuditProgress } from './auditEvaluate';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export async function getAuditInstances(userId: string, plantId: string): Promise<AuditDTO[]> {
	try {
		// Define the type for the audit query result
		type AuditQueryResult = {
			id: string;
			code: string;
			auditTypeId: string;
			workplaceId: string | null;
			responsiblePersonId: string | null;
			plannedEvaluationDate: string;
			completionDate: string | null;
			realDuration: number | null;
			questions: TemplateQuestions;
			createdBy: string | null;
			createdAt: Date | null;
			successRate: number | null;
			rulesResult: string | null;
			evaluationConfigId: string | null;
		};

		let audits: AuditQueryResult[];

		if (userId) {
			// Tenant filter
			let plantsCondition;
			if (plantId) {
				plantsCondition = eq(workplaceTable.plantId, plantId);
			} else {
				plantsCondition = eq(workplaceTable.plantId, 'no-access');
			}

			audits = await db
				.select({
					id: auditInstancesTable.id,
					code: auditInstancesTable.code,
					auditTypeId: auditInstancesTable.auditTypeId,
					workplaceId: auditInstancesTable.workplaceId,
					responsiblePersonId: auditInstancesTable.responsiblePersonId,
					plannedEvaluationDate: auditInstancesTable.plannedDate,
					completionDate: auditInstancesTable.completionDate,
					realDuration: auditInstancesTable.realDuration,
					questions: auditInstancesTable.questions,
					createdBy: auditInstancesTable.createdBy,
					createdAt: auditInstancesTable.createdAt,
					successRate: auditInstancesTable.successRate,
					rulesResult: auditInstancesTable.rulesResult,
					evaluationConfigId: auditInstancesTable.evaluationConfigId
				})
				.from(auditInstancesTable)
				.leftJoin(workplaceTable, eq(auditInstancesTable.workplaceId, workplaceTable.id))
				.leftJoin(plantsTable, eq(workplaceTable.plantId, plantsTable.id))
				.where(
					and(
						// plants filtr
						plantsCondition,
						// only audits whene the user is auditor
						eq(auditInstancesTable.responsiblePersonId, userId),
						not(like(auditInstancesTable.code, 'DELETED_%'))
					)
				)
				.orderBy(desc(auditInstancesTable.createdAt));
		} else {
			audits = await db
				.select({
					id: auditInstancesTable.id,
					code: auditInstancesTable.code,
					auditTypeId: auditInstancesTable.auditTypeId,
					workplaceId: auditInstancesTable.workplaceId,
					responsiblePersonId: auditInstancesTable.responsiblePersonId,
					plannedEvaluationDate: auditInstancesTable.plannedDate,
					completionDate: auditInstancesTable.completionDate,
					realDuration: auditInstancesTable.realDuration,
					questions: auditInstancesTable.questions,
					createdBy: auditInstancesTable.createdBy,
					createdAt: auditInstancesTable.createdAt,
					successRate: auditInstancesTable.successRate,
					rulesResult: auditInstancesTable.rulesResult,
					evaluationConfigId: auditInstancesTable.evaluationConfigId
				})
				.from(auditInstancesTable)
				.where(and(not(like(auditInstancesTable.code, 'DELETED_%'))));
		}

		if (!audits.length) {
			return [];
		}

		const auditsWithDetails = await Promise.all(
			audits.map(async (audit) => {
				// Get audit type
				const [auditType] = await db
					.select({
						id: auditTypesTable.id,
						name: auditTypesTable.name,
						expectedDuration: auditTypesTable.expectedDuration
					})
					.from(auditTypesTable)
					.where(eq(auditTypesTable.id, audit.auditTypeId));

				let evaluationMode = null;
				let auditInstanceConfig = null;
				if (audit.evaluationConfigId) {
					const [instanceConfig] = await db
						.select({
							evaluationMode: auditInstanceEvaluationConfigTable.evaluationMode,
							auditThreshold_average: auditInstanceEvaluationConfigTable.auditThreshold_average,
							auditThreshold_success: auditInstanceEvaluationConfigTable.auditThreshold_success,
							average_averageAuditAnswers:
								auditInstanceEvaluationConfigTable.average_averageAuditAnswers,
							notSuccessful_averageAnswers:
								auditInstanceEvaluationConfigTable.notSuccessful_averageAnswers,
							notSuccessful_badAnswers: auditInstanceEvaluationConfigTable.notSuccessful_badAnswers
						})
						.from(auditInstanceEvaluationConfigTable)
						.where(eq(auditInstanceEvaluationConfigTable.id, audit.evaluationConfigId));

					if (instanceConfig) {
						evaluationMode = instanceConfig.evaluationMode;
						auditInstanceConfig = {
							auditThreshold_average: instanceConfig.auditThreshold_average,
							auditThreshold_success: instanceConfig.auditThreshold_success,
							average_averageAuditAnswers: instanceConfig.average_averageAuditAnswers,
							notSuccessful_averageAnswers: instanceConfig.notSuccessful_averageAnswers,
							notSuccessful_badAnswers: instanceConfig.notSuccessful_badAnswers
						};
					}
				}

				let workplace = null;
				if (audit.workplaceId) {
					const [workplaceResult] = await db
						.select({
							id: workplaceTable.id,
							name: workplaceTable.name,
							code: workplaceTable.code,
							active: workplaceTable.active
						})
						.from(workplaceTable)
						.where(eq(workplaceTable.id, audit.workplaceId));
					// Don't filter soft-deleted workplaces here - we want to show them in audit history
					workplace = workplaceResult;
				}

				let responsiblePerson = null;
				if (audit.responsiblePersonId) {
					const [personResult] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName
						})
						.from(usersTable)
						.where(eq(usersTable.id, audit.responsiblePersonId));
					responsiblePerson = personResult;
				}

				let createdByUser = null;
				if (audit.createdBy) {
					const [createdByResult] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName
						})
						.from(usersTable)
						.where(eq(usersTable.id, audit.createdBy));
					createdByUser = createdByResult;
				}

				const answers = await getAuditAnswers(audit.id);
				const progress = calculateAuditProgress(
					audit.questions,
					answers,
					audit.plannedEvaluationDate,
					audit.completionDate
				);

				return {
					id: audit.id,
					code: audit.code,
					auditType: auditType
						? {
								id: auditType.id,
								name: auditType.name,
								evaluationMode: evaluationMode
							}
						: null,
					workplace: workplace
						? {
								id: workplace.id,
								name: workplace.name,
								code: workplace.code
							}
						: null,
					responsiblePerson: responsiblePerson
						? {
								id: responsiblePerson.id,
								firstName: responsiblePerson.firstName,
								lastName: responsiblePerson.lastName
							}
						: null,
					createdBy: createdByUser
						? {
								id: createdByUser.id,
								firstName: createdByUser.firstName,
								lastName: createdByUser.lastName
							}
						: null,
					createdAt: audit.createdAt ? new Date(audit.createdAt) : null,
					plannedEvaluationDate: audit.plannedEvaluationDate,
					completionDate: audit.completionDate,
					expectedDuration: auditType?.expectedDuration ?? undefined,
					realDuration: audit.realDuration ?? undefined, // Convert null to undefined
					progress,
					questions: audit.questions,
					successRate: audit.successRate ?? undefined, // Convert null to undefined
					rulesResult: audit.rulesResult ?? undefined, // Convert null to undefined
					auditInstanceConfig: auditInstanceConfig
				};
			})
		);

		return auditsWithDetails;
	} catch (error) {
		console.error('Error fetching audits:', error);
		throw new Error('An error occurred while fetching audits');
	}
}

async function getDefaultAuditValues(auditTypeId: string): Promise<AuditDefaultValues> {
	try {
		const [auditType] = await db
			.select({
				questions: auditTypesTable.questions,
				code: auditTypesTable.code
			})
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!auditType) {
			throw new Error(t.get('errors.audits.services.auditTypeNotFound'));
		}

		if (!auditType.questions) {
			throw new Error('Invalid questions format in audit type');
		}

		const sortedQuestions = sortCategoriesByOrder(auditType.questions);

		return {
			auditTypeId,
			questions: sortedQuestions,
			code: await generateAuditCode(auditType.code),
			progress: 0
		};
	} catch (error) {
		console.error('Error in getDefaultAuditValues:', error);
		throw error;
	}
}

//Sort categories by their order value
function sortCategoriesByOrder(questions: TemplateQuestions): TemplateQuestions {
	const sortedKeys = Object.keys(questions).sort((a, b) => {
		const numA = parseInt(a);
		const numB = parseInt(b);
		return numA - numB;
	});

	const sortedQuestions: TemplateQuestions = {};
	sortedKeys.forEach((key) => {
		sortedQuestions[key] = {
			...questions[key],

			questions: questions[key].questions.sort(
				(a, b) => (a.order ?? Number.MAX_SAFE_INTEGER) - (b.order ?? Number.MAX_SAFE_INTEGER)
			)
		};
	});

	return sortedQuestions;
}

export async function createAuditInstance(
	auditTypeId: string,
	formData: CreateAuditForm,
	plantId: string,
	createdBy?: string
) {
	try {
		const defaultValues = await getDefaultAuditValues(auditTypeId);

		const plannedDate = formData.plannedDate
			? new Date(formData.plannedDate).toISOString()
			: new Date().toISOString();

		// Get template config
		const [typeConfig] = await db
			.select()
			.from(auditTypeEvaluationConfigTable)
			.where(eq(auditTypeEvaluationConfigTable.auditTypeId, auditTypeId));

		// Get plant config
		let plantConfig = null;
		if (plantId) {
			[plantConfig] = await db
				.select()
				.from(plantsEvaluationConfigurationTable)
				.where(eq(plantsEvaluationConfigurationTable.plantId, plantId));
			plantConfig = plantConfig!;
		}

		// Merge configs: prefer typeConfig, else plantConfig
		const configData = {
			evaluationMode: typeConfig?.evaluationMode ?? 'percentage',
			evalWeight_wReservations:
				typeConfig?.evalWeight_wReservations ?? plantConfig!.evalWeight_wReservations,
			pointsRangeMin: typeConfig?.pointsRangeMin ?? plantConfig!.pointsRangeMin,
			pointsRangeMax: typeConfig?.pointsRangeMax ?? plantConfig!.pointsRangeMax,
			pointsAvgThreshold: typeConfig?.pointsAvgThreshold ?? plantConfig!.pointsAvgThreshold,
			pointsSuccessThreshold:
				typeConfig?.pointsSuccessThreshold ?? plantConfig!.pointsSuccessThreshold,
			percentageThreshold_average:
				typeConfig?.percentageThreshold_average ?? plantConfig!.percentageThreshold_average,
			percentageThreshold_success:
				typeConfig?.percentageThreshold_success ?? plantConfig!.percentageThreshold_success,
			auditThreshold_average:
				typeConfig?.auditThreshold_average ?? plantConfig!.auditThreshold_average,
			auditThreshold_success:
				typeConfig?.auditThreshold_success ?? plantConfig!.auditThreshold_success,
			average_averageAuditAnswers:
				typeConfig?.average_averageAuditAnswers ?? plantConfig!.average_averageAuditAnswers,
			average_badAuditAnswers:
				typeConfig?.average_badAuditAnswers ?? plantConfig!.average_badAuditAnswers,
			notSuccessful_averageAnswers:
				typeConfig?.notSuccessful_averageAnswers ?? plantConfig!.notSuccessful_averageAnswers,
			notSuccessful_badAnswers:
				typeConfig?.notSuccessful_badAnswers ?? plantConfig!.notSuccessful_badAnswers,
			auditTypeEvalConfigId: typeConfig.id,
			plantEvalConfigId: plantConfig!.id
		};

		// Find last config change
		const typeConfigUpdatedAt = typeConfig?.updatedAt
			? new Date(typeConfig.updatedAt)
			: new Date(0);
		const plantConfigUpdatedAt = plantConfig?.updatedAt
			? new Date(plantConfig.updatedAt)
			: new Date(0);
		const lastConfigChange =
			typeConfigUpdatedAt > plantConfigUpdatedAt ? typeConfigUpdatedAt : plantConfigUpdatedAt;

		// look for the same instance config
		// if found, use it, else create new one
		const existingConfig = await db
			.select({ id: auditInstanceEvaluationConfigTable.id })
			.from(auditInstanceEvaluationConfigTable)
			.where(
				and(
					eq(auditInstanceEvaluationConfigTable.evaluationMode, configData.evaluationMode),
					eq(
						auditInstanceEvaluationConfigTable.evalWeight_wReservations,
						configData.evalWeight_wReservations
					),
					eq(auditInstanceEvaluationConfigTable.pointsRangeMin, configData.pointsRangeMin),
					eq(auditInstanceEvaluationConfigTable.pointsRangeMax, configData.pointsRangeMax),
					eq(auditInstanceEvaluationConfigTable.pointsAvgThreshold, configData.pointsAvgThreshold),
					eq(
						auditInstanceEvaluationConfigTable.pointsSuccessThreshold,
						configData.pointsSuccessThreshold
					),
					eq(auditInstanceEvaluationConfigTable.pointsAvgThreshold, configData.pointsAvgThreshold),
					eq(
						auditInstanceEvaluationConfigTable.pointsSuccessThreshold,
						configData.pointsSuccessThreshold
					),
					eq(
						auditInstanceEvaluationConfigTable.percentageThreshold_average,
						configData.percentageThreshold_average
					),
					eq(
						auditInstanceEvaluationConfigTable.percentageThreshold_success,
						configData.percentageThreshold_success
					),
					eq(
						auditInstanceEvaluationConfigTable.auditThreshold_average,
						configData.auditThreshold_average
					),
					eq(
						auditInstanceEvaluationConfigTable.auditThreshold_success,
						configData.auditThreshold_success
					),
					eq(
						auditInstanceEvaluationConfigTable.average_averageAuditAnswers,
						configData.average_averageAuditAnswers
					),
					eq(
						auditInstanceEvaluationConfigTable.average_badAuditAnswers,
						configData.average_badAuditAnswers
					),
					eq(
						auditInstanceEvaluationConfigTable.notSuccessful_averageAnswers,
						configData.notSuccessful_averageAnswers
					),
					eq(
						auditInstanceEvaluationConfigTable.notSuccessful_badAnswers,
						configData.notSuccessful_badAnswers
					),
					eq(
						auditInstanceEvaluationConfigTable.auditTypeEvalConfigId,
						configData.auditTypeEvalConfigId
					),
					eq(auditInstanceEvaluationConfigTable.plantEvalConfigId, configData.plantEvalConfigId),
					// createdAt > lastConfigChange
					gt(auditInstanceEvaluationConfigTable.createdAt, lastConfigChange)
				)
			)
			.orderBy(desc(auditInstanceEvaluationConfigTable.createdAt))
			.limit(1);

		const result = await db.transaction(async (tx) => {
			let configId;
			if (existingConfig && existingConfig.length > 0) {
				configId = existingConfig[0].id;
			} else {
				const [config] = await tx
					.insert(auditInstanceEvaluationConfigTable)
					.values(configData)
					.returning({ id: auditInstanceEvaluationConfigTable.id });
				configId = config.id;
			}

			const [audit] = await tx
				.insert(auditInstancesTable)
				.values({
					code: defaultValues.code,
					auditTypeId: defaultValues.auditTypeId,
					plannedDate,
					workplaceId: formData.workplaceId,
					responsiblePersonId: formData.responsiblePersonId,
					questions: defaultValues.questions,
					evaluationConfigId: configId,
					createdBy
				})
				.returning({ id: auditInstancesTable.id });

			return audit;
		});

		return result;
	} catch (error) {
		console.error('Error creating audit instance:', error);
		throw new Error('Failed to create audit instance');
	}
}

//Soft delete - code prefixed with "DELETED", not showing in audit list, stays in db
export async function deleteAuditInstance(auditInstanceId: string) {
	try {
		const result = await db.transaction(async (tx) => {
			const [audit] = await tx
				.select({ code: auditInstancesTable.code })
				.from(auditInstancesTable)
				.where(eq(auditInstancesTable.id, auditInstanceId));

			if (!audit) {
				throw new Error('Audit instance not found');
			}

			const updatedCode = audit.code.startsWith('DELETED_') ? audit.code : `DELETED_${audit.code}`;

			return await tx
				.update(auditInstancesTable)
				.set({
					code: updatedCode
				})
				.where(eq(auditInstancesTable.id, auditInstanceId))
				.returning();
		});

		return result;
	} catch (error) {
		console.error('Error soft-deleting audit instance:', error);
		throw new Error('Failed to soft-delete audit instance');
	}
}

export async function updateAuditInstance(auditId: string, data: CreateAuditForm) {
	try {
		// new audit type ?
		const [currentAudit] = await db
			.select({
				auditTypeId: auditInstancesTable.auditTypeId,
				code: auditInstancesTable.code
			})
			.from(auditInstancesTable)
			.where(eq(auditInstancesTable.id, auditId));

		if (!currentAudit) {
			throw new Error('Audit instance not found');
		}

		// if so => generate new code
		let newCode = undefined;
		if (currentAudit.auditTypeId !== data.auditTypeId) {
			const [auditType] = await db
				.select({ code: auditTypesTable.code })
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, data.auditTypeId));

			if (!auditType) {
				throw new Error('New audit type not found');
			}

			newCode = await generateAuditCode(auditType.code);
		}

		return await db
			.update(auditInstancesTable)
			.set({
				workplaceId: data.workplaceId,
				responsiblePersonId: data.responsiblePersonId,
				auditTypeId: data.auditTypeId,
				plannedDate: data.plannedDate,
				...(newCode && { code: newCode })
			})
			.where(eq(auditInstancesTable.id, auditId))
			.returning();
	} catch (err) {
		console.error('Error updating audit instance:', err);
		throw new Error('Failed to update audit instance');
	}
}

export async function getAuditInstanceEvaluationConfig(auditId: string) {
	try {
		const [audit] = await db
			.select({
				evaluationConfigId: auditInstancesTable.evaluationConfigId
			})
			.from(auditInstancesTable)
			.where(eq(auditInstancesTable.id, auditId));

		if (!audit?.evaluationConfigId) {
			throw new Error('Audit instance evaluation config not found');
		}

		const [config] = await db
			.select()
			.from(auditInstanceEvaluationConfigTable)
			.where(eq(auditInstanceEvaluationConfigTable.id, audit.evaluationConfigId));

		return config;
	} catch (error) {
		console.error('Error fetching audit instance evaluation config:', error);
		throw new Error('Failed to fetch audit instance evaluation config');
	}
}
