import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';
import { db } from '$lib/db/db.server';
import {
	auditAnswersTable,
	auditInstancesTable,
	auditInstanceEvaluationConfigTable,
	auditTypesTable,
	questionsTable,
	answersAttachmentsTable
} from '$lib/db/schema/audits';
import { usersTable } from '$lib/db/schema/user';
import { workplaceTable } from '$lib/db/schema/workplace';
import { getFileSignedUrl } from '$lib/server/storage';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
import { calculateAuditProgress } from './auditEvaluate';

import { eq, inArray } from 'drizzle-orm';
import type { AuditAnswers } from '$lib/schemas/audits/auditsAnswers';

export async function getAnswers(auditId: string): Promise<AuditAnswerResultsDTO[]> {
	try {
		const [auditInstance] = await db
			.select({
				id: auditInstancesTable.id,
				questions: auditInstancesTable.questions
			})
			.from(auditInstancesTable)
			.where(eq(auditInstancesTable.id, auditId));

		if (!auditInstance) {
			throw new Error('Audit not found');
		}

		const answers = await db
			.select({
				id: auditAnswersTable.id,
				questionId: questionsTable.id,
				questionText: questionsTable.text,
				questionSubtext: questionsTable.subtext,
				evaluationValue: auditAnswersTable.evaluationValue,
				note: auditAnswersTable.note,
				evaluationType: questionsTable.evaluationType
			})
			.from(auditAnswersTable)
			.innerJoin(questionsTable, eq(auditAnswersTable.questionId, questionsTable.id))
			.where(eq(auditAnswersTable.auditId, auditId));

		if (!answers.length) {
			throw new Error('No answers found for this audit');
		}

		// Fetch attachments for all answers
		const answerIds = answers.map((answer) => answer.id);
		const attachments = await db
			.select({
				id: answersAttachmentsTable.id,
				answerId: answersAttachmentsTable.answerId,
				url: answersAttachmentsTable.url,
				filename: answersAttachmentsTable.filename,
				type: answersAttachmentsTable.type,
				presignedUrl: answersAttachmentsTable.presignedUrl,
				presignedUrlGeneratedAt: answersAttachmentsTable.presignedUrlGeneratedAt
			})
			.from(answersAttachmentsTable)
			.where(inArray(answersAttachmentsTable.answerId, answerIds));

		const questions = auditInstance.questions as TemplateQuestions;
		const answersWithRequired = answers.map((answer) => {
			const questionInfo = Object.values(questions)
				.flatMap((category) => category?.questions || [])
				.find((q) => q.questionId === answer.questionId);

			// Add files to each answer
			const files = attachments
				.filter((attachment) => attachment.answerId === answer.id)
				.map((attachment) => ({
					id: attachment.id,
					url: attachment.url,
					filename: attachment.filename,
					type: attachment.type
				}));

			return {
				...answer,
				required: questionInfo?.required || false,
				files: files,
				evaluationType: answer.evaluationType
			} as AuditAnswerResultsDTO;
		});

		return answersWithRequired;
	} catch (error) {
		console.error('Error fetching answers:', error);
		throw new Error('Failed to fetch answers');
	}
}

export async function getAnswersWithRefreshedUrls(
	auditId: string
): Promise<AuditAnswerResultsDTO[]> {
	try {
		const answers = await getAnswers(auditId);

		const answerIds = answers.map((answer) => answer.id);
		const attachmentsWithCache = await db
			.select({
				id: answersAttachmentsTable.id,
				answerId: answersAttachmentsTable.answerId,
				url: answersAttachmentsTable.url,
				filename: answersAttachmentsTable.filename,
				type: answersAttachmentsTable.type,
				presignedUrl: answersAttachmentsTable.presignedUrl,
				presignedUrlGeneratedAt: answersAttachmentsTable.presignedUrlGeneratedAt
			})
			.from(answersAttachmentsTable)
			.where(inArray(answersAttachmentsTable.answerId, answerIds));

		const now = new Date();
		const fourDaysAgo = new Date(now.getTime() - 4 * 24 * 60 * 60 * 1000);

		const attachmentsToUpdate: Array<{ id: string; url: string; newPresignedUrl: string }> = [];

		for (const attachment of attachmentsWithCache) {
			const isCacheValid =
				attachment.presignedUrl &&
				attachment.presignedUrlGeneratedAt &&
				new Date(attachment.presignedUrlGeneratedAt) > fourDaysAgo;

			if (!isCacheValid) {
				try {
					const newPresignedUrl = await getFileSignedUrl(attachment.url);
					attachmentsToUpdate.push({
						id: attachment.id,
						url: attachment.url,
						newPresignedUrl
					});

					attachment.presignedUrl = newPresignedUrl;
				} catch (error) {
					console.error(`Error generating presigned URL for attachment ${attachment.id}:`, error);
				}
			}
		}

		if (attachmentsToUpdate.length > 0) {
			await Promise.all(
				attachmentsToUpdate.map((update) =>
					db
						.update(answersAttachmentsTable)
						.set({
							presignedUrl: update.newPresignedUrl,
							presignedUrlGeneratedAt: now
						})
						.where(eq(answersAttachmentsTable.id, update.id))
				)
			);
		}

		const answersWithUpdatedUrls = answers.map((answer) => {
			const files = answer.files.map((file) => {
				const attachmentWithCache = attachmentsWithCache.find((a) => a.id === file.id);

				return {
					...file,
					url: attachmentWithCache?.presignedUrl || file.url
				};
			});

			return { ...answer, files };
		});

		return answersWithUpdatedUrls;
	} catch (error) {
		console.error('Error fetching answers with refreshed URLs:', error);
		throw new Error('Failed to fetch answers with refreshed URLs');
	}
}

export async function getAuditInfo(auditId: string) {
	try {
		// 1. Fetch basic audit data
		const [auditInstance] = await db
			.select({
				id: auditInstancesTable.id,
				code: auditInstancesTable.code,
				workplaceId: auditInstancesTable.workplaceId,
				auditTypeId: auditInstancesTable.auditTypeId,
				responsiblePersonId: auditInstancesTable.responsiblePersonId,
				plannedDate: auditInstancesTable.plannedDate,
				completionDate: auditInstancesTable.completionDate,
				realDuration: auditInstancesTable.realDuration,
				questions: auditInstancesTable.questions,
				successRate: auditInstancesTable.successRate,
				evaluationConfigId: auditInstancesTable.evaluationConfigId
			})
			.from(auditInstancesTable)
			.where(eq(auditInstancesTable.id, auditId));

		if (!auditInstance) {
			return null;
		}

		// 2. Fetch auditor (responsible person)
		let auditor = null;
		if (auditInstance.responsiblePersonId) {
			const [auditorData] = await db
				.select({
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				})
				.from(usersTable)
				.where(eq(usersTable.id, auditInstance.responsiblePersonId));

			auditor = auditorData || null;
		}

		// 3. Fetch audit type and its responsible person
		let auditType = null;
		if (auditInstance.auditTypeId) {
			const [auditTypeData] = await db
				.select({
					id: auditTypesTable.id,
					name: auditTypesTable.name,
					responsiblePersonId: auditTypesTable.responsiblePersonId,
					expectedDuration: auditTypesTable.expectedDuration
				})
				.from(auditTypesTable)
				.where(eq(auditTypesTable.id, auditInstance.auditTypeId));

			if (auditTypeData) {
				// 3.1 Fetch audit type responsible person
				let responsiblePerson = null;
				if (auditTypeData.responsiblePersonId) {
					const [personData] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName
						})
						.from(usersTable)
						.where(eq(usersTable.id, auditTypeData.responsiblePersonId));

					responsiblePerson = personData || null;
				}

				auditType = {
					id: auditTypeData.id,
					name: auditTypeData.name,
					expectedDuration: auditTypeData.expectedDuration,
					responsiblePerson
				};
			}
		}

		// 4. Fetch workplace and its responsible person
		let workplace = null;
		if (auditInstance.workplaceId) {
			const [workplaceData] = await db
				.select({
					id: workplaceTable.id,
					name: workplaceTable.name,
					responsiblePersonId: workplaceTable.responsiblePersonId
				})
				.from(workplaceTable)
				.where(eq(workplaceTable.id, auditInstance.workplaceId));

			if (workplaceData) {
				// 4.1 Fetch workplace responsible person
				let responsiblePerson = null;
				if (workplaceData.responsiblePersonId) {
					const [personData] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName,
							email: usersTable.email
						})
						.from(usersTable)
						.where(eq(usersTable.id, workplaceData.responsiblePersonId));

					responsiblePerson = personData || null;
				}

				workplace = {
					id: workplaceData.id,
					name: workplaceData.name,
					responsiblePerson
				};
			}
		}

		// 5. Get answers and calculate progress
		const answers = await getAnswers(auditId).catch(() => []);
		const answersMap = answers.reduce((acc, answer) => {
			acc[answer.questionId] = {
				questionId: answer.questionId,
				auditId: auditId,
				evaluationValue: answer.evaluationValue,
				note: answer.note || undefined,
				files: []
			};
			return acc;
		}, {} as AuditAnswers);

		const progress = calculateAuditProgress(
			auditInstance.questions as TemplateQuestions,
			answersMap,
			auditInstance.plannedDate,
			auditInstance.completionDate
		);

		// 6. Get evaluation config for this audit instance
		let evaluationConfig = null;
		if (auditInstance.evaluationConfigId) {
			const [instanceConfig] = await db
				.select()
				.from(auditInstanceEvaluationConfigTable)
				.where(eq(auditInstanceEvaluationConfigTable.id, auditInstance.evaluationConfigId));
			evaluationConfig = instanceConfig;
		}

		// 7. Assemble the final result
		return {
			id: auditInstance.id,
			code: auditInstance.code,
			auditor,
			auditType,
			workplace,
			plannedDate: auditInstance.plannedDate,
			expectedDuration: auditType?.expectedDuration,
			realDuration: auditInstance.realDuration,
			completionDate: auditInstance.completionDate,
			successRate: auditInstance.successRate,
			evaluationConfig,
			progress
		};
	} catch (error) {
		console.error('Error fetching audit info:', error);
		throw new Error('Failed to fetch audit info');
	}
}

export async function getResultsQuestions(auditId: string) {
	try {
		const [auditInstance] = await db
			.select({
				questions: auditInstancesTable.questions
			})
			.from(auditInstancesTable)
			.where(eq(auditInstancesTable.id, auditId));

		if (!auditInstance) {
			throw new Error('Audit not found');
		}

		return auditInstance.questions as TemplateQuestions;
	} catch (error) {
		console.error('Error fetching questions:', error);
		throw new Error('Failed to fetch questions');
	}
}
