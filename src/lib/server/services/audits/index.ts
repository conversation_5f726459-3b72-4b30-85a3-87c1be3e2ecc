import {
	completeAudit,
	deleteAttachment,
	getAuditAnswerForQuestion,
	getAuditAnswers,
	getEvaluationQuestions,
	saveQuestionAnswer,
	updateAuditProgress
} from './auditEvaluate';
import {
	createAuditInstance,
	deleteAuditInstance,
	getAuditInstanceEvaluationConfig,
	getAuditInstances,
	updateAuditInstance
} from './audits';
import { getAuditorById, getAuditors } from './auditSetup';
import {
	getAnswers,
	getAnswersWithRefreshedUrls,
	getAuditInfo,
	getResultsQuestions
} from './auditsResult';

export const AuditService = {
	getAuditInstances,
	createAuditInstance,
	getAuditors,
	getAuditorById,
	updateAuditInstance
};

export const AuditSetupService = {
	deleteAuditInstance
};

export const AuditEvaluateService = {
	getEvaluationQuestions,
	updateAuditProgress,
	completeAudit,
	saveQuestionAnswer,
	getAuditAnswers,
	getAuditAnswerForQuestion,
	deleteAttachment
};

export const AuditResultService = {
	getAnswers,
	getAnswersWithRefreshedUrls,
	getAuditInfo,
	getResultsQuestions
};

export const AuditEvaluationConfigService = {
	getAuditInstanceEvaluationConfig
};
