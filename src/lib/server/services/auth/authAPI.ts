import type { AppUser, Session, SessionValidationResult } from '$lib/models/authModel';
import { db } from '$lib/db/db.server';
import { sessionsTable, usersTable, usersWorkInfoTable } from '$lib/db/schema/user';

import { sha256 } from '@oslojs/crypto/sha2';
import { encodeBase32LowerCaseNoPadding, encodeHexLowerCase } from '@oslojs/encoding';
import { eq, isNull } from 'drizzle-orm';
import { plantRolesTable, userRolePlantsTable } from '$lib/db/schema/roles';

export function generateSessionToken(): string {
	const bytes = new Uint8Array(20);
	crypto.getRandomValues(bytes);
	const token = encodeBase32LowerCaseNoPadding(bytes);
	return token;
}

export async function createSession(token: string, userId: string): Promise<Session> {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const session: Session = {
		id: sessionId,
		userId,
		expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7)
	};
	await db.insert(sessionsTable).values(session);
	return session;
}

export async function validateSessionToken(token: string): Promise<SessionValidationResult> {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const result = await db
		.select({ user: usersTable, session: sessionsTable })
		.from(sessionsTable)
		.innerJoin(usersTable, eq(sessionsTable.userId, usersTable.id))
		.where(eq(sessionsTable.id, sessionId));
	if (result.length < 1) {
		return { session: null, user: null, plantRoles: null };
	}
	const { user, session } = result[0];
	if (Date.now() >= session.expiresAt.getTime()) {
		await db.delete(sessionsTable).where(eq(sessionsTable.id, sessionId));
		return { session: null, user: null, plantRoles: null };
	}
	if (Date.now() >= session.expiresAt.getTime() - 1000 * 60 * 60 * 24 * 3) {
		session.expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 7);
		await db
			.update(sessionsTable)
			.set({
				expiresAt: session.expiresAt
			})
			.where(eq(sessionsTable.id, session.id));
	}

	//Get plant roles for user
	const userPlantRoles = await db
		.select({
			roleId: userRolePlantsTable.roleId,
			plantId: userRolePlantsTable.plantId
		})
		.from(userRolePlantsTable)
		.where(eq(userRolePlantsTable.userId, user.id));

	// Check if user has superadmin role (plantId = null)
	const superAdminRoles = await db
		.select({ id: plantRolesTable.id })
		.from(plantRolesTable)
		.where(eq(plantRolesTable.name, 'superadmin') && isNull(plantRolesTable.plantId));

	const isSuperAdmin = userPlantRoles.some(
		(r) => superAdminRoles.some((sa) => sa.id === r.roleId) && r.plantId === null
	);

	if (!isSuperAdmin && userPlantRoles.length === 0) {
		return { session: null, user: null, plantRoles: null };
	}

	//Primary tenant
	const workInfo = await db
		.select({
			plantId: usersWorkInfoTable.plantId,
			companyId: usersWorkInfoTable.companyId
		})
		.from(usersWorkInfoTable)
		.where(eq(usersWorkInfoTable.userId, user.id))
		.limit(1);

	const plants = await db
		.select({ plantId: userRolePlantsTable.plantId })
		.from(userRolePlantsTable)
		.where(eq(userRolePlantsTable.userId, user.id));

	const plantIds: string[] = [];

	if (workInfo[0]) {
		plantIds.push(workInfo[0].plantId); // users plant first
	}

	// remaining plants
	plants.forEach((t) => {
		if (t.plantId !== null && !plantIds.includes(t.plantId)) {
			plantIds.push(t.plantId);
		}
	});

	const appUser: AppUser = {
		...user,
		companyId: workInfo[0]?.companyId || '',
		plants: plantIds,
		mainPlantId: workInfo[0]?.plantId || ''
	};

	return { session, user: appUser, plantRoles: userPlantRoles };
}

export async function invalidateSession(sessionId: string): Promise<void> {
	await db.delete(sessionsTable).where(eq(sessionsTable.id, sessionId));
}

export async function invalidateAllSessions(userId: string): Promise<void> {
	await db.delete(sessionsTable).where(eq(sessionsTable.userId, userId));
}
