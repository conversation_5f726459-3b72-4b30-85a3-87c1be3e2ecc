import { db } from '$lib/db/db.server';
import { userAuthsTable } from '$lib/db/schema/auth';
import { eq } from 'drizzle-orm';
import { hashPassword, verifyPasswordHash } from './utils';

export class InvalidCurrentPasswordError extends Error {
	constructor() {
		super('Current password is incorrect');
		this.name = 'InvalidCurrentPasswordError';
	}
}

export async function changePassword(
	currentPassword: string,
	newPassword: string,
	userId: string
): Promise<boolean> {
	try {
		const [userAuth] = await db
			.select()
			.from(userAuthsTable)
			.where(eq(userAuthsTable.userId, userId))
			.limit(1);

		if (!userAuth || !userAuth.password) {
			throw new Error('User not found or no password set');
		}

		const isCurrentPasswordValid = await verifyPasswordHash(currentPassword, userAuth.password);
		if (!isCurrentPasswordValid) {
			throw new InvalidCurrentPasswordError();
		}

		const hashedNewPassword = await hashPassword(newPassword);

		await db
			.update(userAuthsTable)
			.set({
				password: hashedNewPassword
			})
			.where(eq(userAuthsTable.userId, userId));

		return true;
	} catch (error) {
		console.error('Change password error:', error);
		throw error;
	}
}
