import {
	createSession,
	generateSessionToken,
	invalidateAllSessions,
	invalidateSession,
	validateSessionToken
} from './authAPI';
import { login } from './login';
import { register } from './register';
import { createPasswordReset, resetPassword } from './passwordReset';
import { changePassword } from './changePassword';

export const AuthService = {
	generateSessionToken,
	createSession,
	validateSessionToken,
	invalidateSession,
	invalidateAllSessions,
	register,
	login,
	createPasswordReset,
	resetPassword,
	changePassword
};
