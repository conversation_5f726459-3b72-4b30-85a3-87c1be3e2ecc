import type { LoginForm } from '$lib/schemas/auth';
import { db } from '$lib/db/db.server';
import { userAuthsTable } from '$lib/db/schema/auth';
import { usersTable } from '$lib/db/schema/user';
import { eq } from 'drizzle-orm';
import { verifyPasswordHash } from './utils';
import { userRolePlantsTable } from '$lib/db/schema/roles';

export async function login(data: LoginForm): Promise<string | { error: string } | null> {
	try {
		const { email, password } = data;
		//console.log('Login attempt:', { email });

		const [loginData] = await db
			.select({ id: usersTable.id, password: userAuthsTable.password, active: usersTable.active })
			.from(usersTable)
			.innerJoin(userAuthsTable, eq(usersTable.id, userAuthsTable.userId))
			.where(eq(usersTable.email, email))
			.limit(1);

		//console.log('DB query result:', loginData);

		if (!loginData) {
			console.log('No user found with this email:' + email);
			return null;
		}

		const { id, password: hashedPassword } = loginData;

		if (!hashedPassword) {
			return null;
		}

		const correctPassword = await verifyPasswordHash(password, hashedPassword);
		//console.log('Password verification result:', correctPassword);

		if (!correctPassword) {
			return null;
		}

		if (loginData.active === false) {
			return { error: 'accDeactivated' };
		}

		const hasRole = await db
			.select({ userId: userRolePlantsTable.userId })
			.from(userRolePlantsTable)
			.where(eq(userRolePlantsTable.userId, id));

		if (hasRole.length === 0) {
			return { error: 'noPlantRole' };
		}

		return id;
	} catch (error) {
		console.error('Login error:', error);
		throw error;
	}
}
