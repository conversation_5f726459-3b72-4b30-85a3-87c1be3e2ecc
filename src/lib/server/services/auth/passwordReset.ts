import { db } from '$lib/db/db.server';
import { userAuthsTable } from '$lib/db/schema/auth';
import { eq } from 'drizzle-orm';
import { hashPassword } from './utils';
import { randomBytes } from 'crypto';
import { usersTable } from '$lib/db/schema/user';
import { EmailService } from '$lib/server/email';

// Generate token for password reset
export function generateResetToken(): string {
	return randomBytes(32).toString('hex');
}

// Save token for password reset
export async function createPasswordReset(
	email: string,
	locale: string,
	origin: string
): Promise<boolean> {
	try {
		const user = await db
			.select({ id: usersTable.id })
			.from(usersTable)
			.where(eq(usersTable.email, email.toLowerCase()))
			.limit(1);

		if (!user || user.length === 0) {
			return false;
		}

		const resetToken = generateResetToken();
		const expiresAt = new Date();
		expiresAt.setMinutes(expiresAt.getMinutes() + 30); // Token valid for 30 minutes

		await db
			.update(userAuthsTable)
			.set({
				passwordReset: resetToken,
				passwordResetExpiration: expiresAt
			})
			.where(eq(userAuthsTable.userId, user[0].id));

		const resetLink = `${origin}/reset-password/${resetToken}`;

		await EmailService.forgottenPassword(email, {
			link: resetLink,
			locale
		});
		return true;
	} catch (error) {
		console.error('Password reset error:', error);
		return false;
	}
}

export async function resetPassword(token: string, newPassword: string): Promise<boolean> {
	try {
		const now = new Date();
		const hashedPassword = await hashPassword(newPassword);

		const [userAuth] = await db
			.select()
			.from(userAuthsTable)
			.where(eq(userAuthsTable.passwordReset, token))
			.limit(1);

		if (!userAuth || !userAuth.passwordResetExpiration || userAuth.passwordResetExpiration < now) {
			return false;
		}

		await db
			.update(userAuthsTable)
			.set({
				password: hashedPassword,
				passwordReset: null,
				passwordResetExpiration: null
			})
			.where(eq(userAuthsTable.id, userAuth.id));

		return true;
	} catch (error) {
		console.error('Reset password error:', error);
		return false;
	}
}
