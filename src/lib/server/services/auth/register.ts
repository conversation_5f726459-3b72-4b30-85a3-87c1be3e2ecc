import { usersTable } from '$lib/db/schema/user';
import { hashPassword } from './utils';
import { generateSessionToken, createSession } from './authAPI';
import { userAuthsTable } from '$lib/db/schema/auth';
import { db } from '$lib/db/db.server';

function capitalizeFirstLetter(str: string): string {
	if (!str) return str;
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export async function register(data: {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	password: string;
}) {
	const hashedPassword = await hashPassword(data.password);

	const [user] = await db
		.insert(usersTable)
		.values({
			firstName: capitalizeFirstLetter(data.firstName),
			lastName: capitalizeFirstLetter(data.lastName),
			email: data.email.toLowerCase(),
			phone: data.phone
		})
		.returning();

	await db.insert(userAuthsTable).values({
		userId: user.id,
		password: hashedPassword
	});

	const token = generateSessionToken();

	const session = await createSession(token, user.id);

	return { user, session, token };
}
