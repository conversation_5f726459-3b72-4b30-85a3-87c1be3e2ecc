import { AuditService } from '../audits';
import { mapAuditToListType } from '$lib/DTO/audits/audits';
import { auditAnswersTable, auditInstancesTable } from '$lib/db/schema/audits';
import { workplaceTable } from '$lib/db/schema/workplace';
import { db } from '$lib/db/db.server';
import { and, eq, isNotNull, like, not } from 'drizzle-orm';

function parseDate(dateStr: string) {
	return new Date(dateStr);
}

export async function getDashboardAudits(userId: string, plantId: string) {
	if (!userId) {
		return {
			lastFinished: [],
			comingSoon: [],
			overdue: []
		};
	}

	const auditsRaw = await AuditService.getAuditInstances(userId, plantId);
	const audits = auditsRaw.map(mapAuditToListType);

	const lastFinished = audits
		.filter((a) => a.completionDate)
		.sort((a, b) =>
			b.completionDate && a.completionDate ? b.completionDate.localeCompare(a.completionDate) : 0
		)
		.slice(0, 5);

	const today = new Date();
	today.setHours(0, 0, 0, 0);

	const comingSoon = audits
		.filter((a) => {
			if (!a.completionDate) {
				const plannedDate = parseDate(a.plannedDate);
				plannedDate.setHours(0, 0, 0, 0);
				return (
					plannedDate >= today && plannedDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
				);
			}
			return false;
		})
		.sort((a, b) => parseDate(a.plannedDate).getTime() - parseDate(b.plannedDate).getTime())
		.slice(0, 5);

	const overdue = audits
		.filter((a) => {
			if (!a.completionDate) {
				const plannedDate = parseDate(a.plannedDate);
				plannedDate.setHours(0, 0, 0, 0);
				return plannedDate < today;
			}
			return false;
		})
		.sort((a, b) => parseDate(a.plannedDate).getTime() - parseDate(b.plannedDate).getTime())
		.slice(0, 5);

	return {
		lastFinished,
		comingSoon,
		overdue
	};
}

export async function getAuditStats(plantId: string) {
	try {
		const conditions = [not(like(auditInstancesTable.code, 'DELETED_%'))];

		if (plantId) {
			conditions.push(eq(workplaceTable.plantId, plantId));
		}

		const auditsWithAnswers = await db
			.select({
				auditId: auditInstancesTable.id,
				completionDate: auditInstancesTable.completionDate,
				plannedDate: auditInstancesTable.plannedDate,
				questions: auditInstancesTable.questions,
				hasAnswers: auditAnswersTable.id
			})
			.from(auditInstancesTable)
			.leftJoin(auditAnswersTable, eq(auditInstancesTable.id, auditAnswersTable.auditId))
			.leftJoin(workplaceTable, eq(auditInstancesTable.workplaceId, workplaceTable.id))
			.where(and(...conditions));

		const auditMap = new Map();

		for (const row of auditsWithAnswers) {
			if (!auditMap.has(row.auditId)) {
				auditMap.set(row.auditId, {
					id: row.auditId,
					completionDate: row.completionDate,
					plannedDate: row.plannedDate,
					questions: row.questions,
					hasAnswers: false
				});
			}

			if (row.hasAnswers) {
				auditMap.get(row.auditId).hasAnswers = true;
			}
		}

		const audits = Array.from(auditMap.values());
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		let closed = 0;
		let planned = 0;
		let inProgress = 0;
		let overdue = 0;

		for (const audit of audits) {
			if (audit.completionDate) {
				closed++;
			} else {
				const plannedDate = new Date(audit.plannedDate);
				plannedDate.setHours(0, 0, 0, 0);

				if (audit.hasAnswers) {
					inProgress++;
				} else if (plannedDate < today) {
					overdue++;
				} else {
					planned++;
				}
			}
		}

		return {
			closed,
			planned,
			inProgress,
			overdue
		};
	} catch (error) {
		console.error('Error fetching audit stats:', error);
		throw new Error('Failed to fetch audit stats');
	}
}

export async function calculateAuditSuccessRate(plantId: string): Promise<number> {
	try {
		const conditions = [
			isNotNull(auditInstancesTable.completionDate),
			not(like(auditInstancesTable.code, 'DELETED_%')),
			isNotNull(auditAnswersTable.normalizedValue)
		];

		if (plantId) {
			conditions.push(eq(workplaceTable.plantId, plantId));
		}

		const answers = await db
			.select({
				normalizedValue: auditAnswersTable.normalizedValue
			})
			.from(auditAnswersTable)
			.innerJoin(auditInstancesTable, eq(auditAnswersTable.auditId, auditInstancesTable.id))
			.leftJoin(workplaceTable, eq(auditInstancesTable.workplaceId, workplaceTable.id))
			.where(and(...conditions));

		if (answers.length === 0) return 0;

		// Convert normalized values to numbers and filter out invalid values
		const validValues = answers
			.map((answer) => (answer.normalizedValue ? parseFloat(answer.normalizedValue) : null))
			.filter((value): value is number => value !== null && !isNaN(value));

		if (validValues.length === 0) return 0;

		// Calculate average of normalized values
		const totalScore = validValues.reduce((sum, value) => sum + value, 0);
		const averageScore = totalScore / validValues.length;

		// Convert to percentage
		return Math.round(averageScore * 100);
	} catch (error) {
		console.error('Error calculating success rate:', error);
		return 0;
	}
}

export async function getAuditCompletionChartData(plantId: string) {
	try {
		const conditions = [not(like(auditInstancesTable.code, 'DELETED_%'))];

		// Tenant filtering
		if (plantId) {
			conditions.push(eq(workplaceTable.plantId, plantId));
		}

		const audits = await db
			.select({
				plannedDate: auditInstancesTable.plannedDate,
				completionDate: auditInstancesTable.completionDate,
				workplaceId: auditInstancesTable.workplaceId,
				plantId: workplaceTable.plantId
			})
			.from(auditInstancesTable)
			.leftJoin(workplaceTable, eq(auditInstancesTable.workplaceId, workplaceTable.id))
			.where(and(...conditions));

		const nineMonthsAgo = new Date();
		nineMonthsAgo.setMonth(nineMonthsAgo.getMonth() - 7);
		nineMonthsAgo.setDate(1);

		const weeklyData = new Map();

		const endDate = new Date();
		endDate.setDate(endDate.getDate() + 7);
		const currentDate = new Date(nineMonthsAgo);

		while (currentDate <= endDate) {
			const weekStart = getWeekStart(currentDate);
			const weekKey = formatDate(weekStart);

			const monthDisplay = `${weekStart.getMonth() + 1}/${weekStart.getFullYear()}`;

			const isFirstWeekOfMonth = weekStart.getDate() <= 7;

			weeklyData.set(weekKey, {
				date: new Date(weekStart),
				planned: 0,
				completed: 0,
				monthDisplay,
				isFirstWeekOfMonth
			});

			currentDate.setDate(currentDate.getDate() + 7);
		}

		for (const audit of audits) {
			if (!audit.plannedDate) continue;

			const plannedDate = new Date(audit.plannedDate);

			if (plannedDate >= nineMonthsAgo) {
				const weekStart = getWeekStart(plannedDate);
				const weekKey = formatDate(weekStart);

				if (weeklyData.has(weekKey)) {
					const weekData = weeklyData.get(weekKey);
					weekData.planned++;

					if (audit.completionDate) {
						weekData.completed++;
					}
				}
			}
		}

		const chartData = Array.from(weeklyData.entries())
			.map(([weekKey, data]) => ({
				date: data.date,
				week: weekKey,
				planned: data.planned,
				completed: data.completed,
				monthDisplay: data.monthDisplay,
				isFirstWeekOfMonth: data.isFirstWeekOfMonth
			}))
			.sort((a, b) => a.date.getTime() - b.date.getTime());

		return chartData;
	} catch (error) {
		console.error('Error fetching audit completion chart data:', error);
		return [];
	}
}

function getWeekStart(date: Date | string): Date {
	const d = new Date(date);
	const day = d.getDay();
	const diff = d.getDate() - day + (day === 0 ? -6 : 1);
	return new Date(d.setDate(diff));
}

function formatDate(date: Date): string {
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
		date.getDate()
	).padStart(2, '0')}`;
}
