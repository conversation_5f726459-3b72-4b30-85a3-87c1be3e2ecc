import { EmailService } from '$lib/server/email';
import { db } from '$lib/db/db.server';
import { usersTable } from '$lib/db/schema/user';
import { eq } from 'drizzle-orm';

export async function sendEmail(
	userId: string,
	auditTypeName: string,
	message: string,
	questionId: string,
	locale: string
) {
	try {
		const user = await db
			.select({
				email: usersTable.email,
				firstName: usersTable.firstName,
				lastName: usersTable.lastName
			})
			.from(usersTable)
			.where(eq(usersTable.id, userId))
			.limit(1);

		if (!user || user.length === 0) {
			console.error('User not found:', userId);
			return false;
		}

		return await EmailService.contactEmail(user[0].email, {
			message,
			auditTypeName,
			questionId,
			senderName: `${user[0].firstName} ${user[0].lastName}`,
			locale
		});
	} catch (error) {
		console.error('Error sending email:', error);
		return false;
	}
}
