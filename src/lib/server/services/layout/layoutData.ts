import { db } from '$lib/db/db.server';
import { auditTypesTable } from '$lib/db/schema/audits';
import { workplaceTable } from '$lib/db/schema/workplace';
import { usersTable } from '$lib/db/schema/user';
import { eq, and, not, like } from 'drizzle-orm';
import { PlantLanguageService } from '../tenants';
import { userRolePlantsTable } from '$lib/db/schema/roles';

export async function loadLayoutData(plantId: string) {
	// Batch
	const [auditTypes, workplaces, auditors, languages] = await Promise.all([
		db
			.select({
				id: auditTypesTable.id,
				name: auditTypesTable.name,
				code: auditTypesTable.code
			})
			.from(auditTypesTable)
			.where(
				and(
					eq(auditTypesTable.plantId, plantId),
					eq(auditTypesTable.active, true),
					not(like(auditTypesTable.code, 'DELETED_%'))
				)
			),

		// Active workplaces
		db
			.select({
				id: workplaceTable.id,
				name: workplaceTable.name,
				code: workplaceTable.code
			})
			.from(workplaceTable)
			.where(and(eq(workplaceTable.plantId, plantId), eq(workplaceTable.active, true))),

		// Users with access via roles
		db
			.select({
				id: usersTable.id,
				name: {
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				},
				email: usersTable.email
			})
			.from(usersTable)
			.innerJoin(userRolePlantsTable, eq(usersTable.id, userRolePlantsTable.userId))
			.where(eq(userRolePlantsTable.plantId, plantId))
			.groupBy(usersTable.id, usersTable.firstName, usersTable.lastName, usersTable.email),

		PlantLanguageService.getPlantSupportedLanguages(plantId)
	]);

	return {
		auditTypes,
		workplaces,
		auditors,
		supportedLanguages: languages
	};
}
