import type { QuestionsDTO, QuestionWithTagsDTO } from '$lib/DTO/questions/questions';
import { db } from '$lib/db/db.server';
import {
	questionsTable,
	tagsTable,
	questionTagsTable,
	auditTypesTable
} from '$lib/db/schema/audits';
import { eq, desc, inArray, and, sql } from 'drizzle-orm';
import { getTagsForQuestion, assignTagsToQuestion, createTag } from '../tags/tags';

export async function getQuestions(
	plantId: string,
	includeDeleted: boolean = false
): Promise<QuestionsDTO[]> {
	try {
		const conditions = [];

		if (!includeDeleted) {
			conditions.push(eq(questionsTable.deleted, false));
		}

		if (plantId) {
			conditions.push(eq(questionsTable.plantId, plantId));
		}

		const result = await db
			.select()
			.from(questionsTable)
			.where(conditions.length > 0 ? and(...conditions) : undefined)
			.orderBy(desc(questionsTable.createdAt));

		if (!result.length) {
			return [];
		}

		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch questions');
	}
}

export async function createQuestion(data: QuestionsDTO, plantId: string) {
	try {
		const questionData = {
			...data,
			plantId
		};

		const [result] = await db.insert(questionsTable).values(questionData).returning();

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to create question');
	}
}

export async function editQuestion(id: string, data: QuestionsDTO) {
	try {
		const [result] = await db
			.update(questionsTable)
			.set(data)
			.where(eq(questionsTable.id, id))
			.returning();

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to edit question');
	}
}

export async function deleteQuestion(id: string) {
	try {
		// Check if question is used in any audit type using simple LIKE on text conversion
		const auditTypesUsingQuestion = await db
			.select({
				id: auditTypesTable.id,
				name: auditTypesTable.name,
				code: auditTypesTable.code
			})
			.from(auditTypesTable)
			.where(sql`${auditTypesTable.questions}::text LIKE ${`%${id}%`}`)
			.limit(2);

		if (auditTypesUsingQuestion.length > 0) {
			return {
				success: false,
				isUsed: true,
				usedInAuditTypes: auditTypesUsingQuestion,
				message: 'Question is used in audit types and cannot be deleted'
			};
		}

		await db.delete(questionTagsTable).where(eq(questionTagsTable.questionId, id));

		const [result] = await db
			.update(questionsTable)
			.set({ deleted: true })
			.where(eq(questionsTable.id, id))
			.returning();

		if (!result) {
			return {
				success: false,
				isUsed: false,
				message: 'Question not found'
			};
		}

		return {
			success: true,
			isUsed: false,
			data: result
		};
	} catch (error) {
		console.error('Delete question error:', error);
		throw new Error('Failed to delete question');
	}
}

export async function restoreQuestion(id: string) {
	try {
		const [result] = await db
			.update(questionsTable)
			.set({ deleted: false })
			.where(eq(questionsTable.id, id))
			.returning();

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to restore question');
	}
}

export async function getQuestionById(questionId: string) {
	if (!questionId) {
		return null;
	}

	try {
		const [result] = await db
			.select()
			.from(questionsTable)
			.where(eq(questionsTable.id, questionId));

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch question');
	}
}

// Functions with tags support
export async function getQuestionsWithTags(
	plantId: string,
	includeDeleted: boolean = false
): Promise<QuestionWithTagsDTO[]> {
	try {
		const conditions = [];

		if (!includeDeleted) {
			conditions.push(eq(questionsTable.deleted, false));
		}

		if (plantId) {
			conditions.push(eq(questionsTable.plantId, plantId));
		}

		// Get all questions with their tags in one query using LEFT JOIN
		const baseQuery = db
			.select({
				id: questionsTable.id,
				text: questionsTable.text,
				subtext: questionsTable.subtext,
				evaluationType: questionsTable.evaluationType,
				deleted: questionsTable.deleted,
				createdAt: questionsTable.createdAt,
				tagId: tagsTable.id,
				tagName: tagsTable.name,
				tagColor: tagsTable.color,
				tagTextColor: tagsTable.textColor
			})
			.from(questionsTable)
			.leftJoin(questionTagsTable, eq(questionsTable.id, questionTagsTable.questionId))
			.leftJoin(tagsTable, eq(questionTagsTable.tagId, tagsTable.id));

		const result =
			conditions.length > 0
				? await baseQuery.where(and(...conditions)).orderBy(desc(questionsTable.createdAt))
				: await baseQuery.orderBy(desc(questionsTable.createdAt));

		const questionsMap = new Map<string, QuestionWithTagsDTO>();

		for (const row of result) {
			const questionId = row.id!;

			if (!questionsMap.has(questionId)) {
				questionsMap.set(questionId, {
					id: row.id,
					text: row.text,
					subtext: row.subtext,
					evaluationType: row.evaluationType,
					deleted: row.deleted,
					createdAt: row.createdAt,
					tags: []
				});
			}

			// Add tag if it exists
			if (row.tagId && row.tagName) {
				const question = questionsMap.get(questionId)!;
				question.tags!.push({
					id: row.tagId,
					name: row.tagName,
					color: row.tagColor!,
					textColor: row.tagTextColor!
				});
			}
		}

		const finalResult = Array.from(questionsMap.values());

		return finalResult;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch questions with tags');
	}
}

export async function getQuestionsWithTagsFiltered(
	plantId: string,
	includeDeleted: boolean = false,
	filters?: {
		evaluationTypes?: string[];
		tagIds?: string[];
	}
): Promise<QuestionWithTagsDTO[]> {
	try {
		// Build where conditions
		const whereConditions = [];

		// Base condition for deleted status
		if (!includeDeleted) {
			whereConditions.push(eq(questionsTable.deleted, false));
		}

		// Plant filtering
		if (plantId) {
			whereConditions.push(eq(questionsTable.plantId, plantId));
		}

		// Evaluation type filter
		if (filters?.evaluationTypes && filters.evaluationTypes.length > 0) {
			whereConditions.push(inArray(questionsTable.evaluationType, filters.evaluationTypes));
		}

		// Build the query with where conditions
		const baseQuery = db
			.select({
				id: questionsTable.id,
				text: questionsTable.text,
				subtext: questionsTable.subtext,
				evaluationType: questionsTable.evaluationType,
				deleted: questionsTable.deleted,
				createdAt: questionsTable.createdAt,
				tagId: tagsTable.id,
				tagName: tagsTable.name,
				tagColor: tagsTable.color,
				tagTextColor: tagsTable.textColor
			})
			.from(questionsTable)
			.leftJoin(questionTagsTable, eq(questionsTable.id, questionTagsTable.questionId))
			.leftJoin(tagsTable, eq(questionTagsTable.tagId, tagsTable.id));

		// Apply where conditions and execute the query
		const result =
			whereConditions.length > 0
				? await baseQuery.where(and(...whereConditions)).orderBy(desc(questionsTable.createdAt))
				: await baseQuery.orderBy(desc(questionsTable.createdAt));

		// Group results by question
		const questionsMap = new Map<string, QuestionWithTagsDTO>();

		for (const row of result) {
			const questionId = row.id!;

			if (!questionsMap.has(questionId)) {
				questionsMap.set(questionId, {
					id: row.id,
					text: row.text,
					subtext: row.subtext,
					evaluationType: row.evaluationType,
					deleted: row.deleted,
					createdAt: row.createdAt,
					tags: []
				});
			}

			// Add tag if it exists
			if (row.tagId && row.tagName) {
				const question = questionsMap.get(questionId)!;
				question.tags!.push({
					id: row.tagId,
					name: row.tagName,
					color: row.tagColor!,
					textColor: row.tagTextColor!
				});
			}
		}

		let finalResult = Array.from(questionsMap.values());

		// Apply tag filtering after grouping (since we need to check if question has ANY of the specified tags)
		if (filters?.tagIds && filters.tagIds.length > 0) {
			finalResult = finalResult.filter((question) =>
				question.tags?.some((tag) => tag.id && filters.tagIds!.includes(tag.id))
			);
		}

		return finalResult;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch filtered questions with tags');
	}
}

export async function getQuestionByIdWithTags(
	questionId: string,
	plantId?: string
): Promise<QuestionWithTagsDTO | null> {
	if (!questionId) {
		return null;
	}

	try {
		const question = await getQuestionById(questionId);

		if (!question) {
			return null;
		}

		const tags = await getTagsForQuestion(questionId, plantId);

		return {
			...question,
			tags
		};
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch question with tags');
	}
}

// Helper function to process both tag IDs and names, creating new tags as needed
async function processTagIdsAndNames(
	tagIds: string[],
	tagNames: string[],
	plantId: string
): Promise<string[]> {
	const processedIds: string[] = [];

	for (let i = 0; i < tagIds.length; i++) {
		const tagId = tagIds[i];
		const tagName = tagNames[i];

		if (tagId.startsWith('temp-')) {
			// This is a temporary tag, create a new real tag
			try {
				const newTag = await createTag(
					{
						name: tagName,
						color: '',
						textColor: ''
					},
					plantId
				);
				if (newTag && newTag.id) {
					processedIds.push(newTag.id);
				}
			} catch (error) {
				console.error(`Failed to create tag "${tagName}":`, error);
				// Skip this tag if creation fails
			}
		} else {
			// This is a real tag ID, use as is
			processedIds.push(tagId);
		}
	}

	return processedIds;
}

export async function createQuestionWithTagsAndNames(
	data: QuestionsDTO,
	tagIds: string[] = [],
	tagNames: string[] = [],
	plantId: string
): Promise<QuestionWithTagsDTO | null> {
	try {
		const question = await createQuestion(data, plantId);

		if (!question || !question.id) {
			return null;
		}

		if (tagIds.length > 0) {
			const processedTagIds = await processTagIdsAndNames(tagIds, tagNames, plantId);
			await assignTagsToQuestion(question.id, processedTagIds);
		}

		const tags = await getTagsForQuestion(question.id, plantId);

		return {
			...question,
			tags
		};
	} catch (error) {
		console.error(error);
		throw new Error('Failed to create question with tags and names');
	}
}

export async function editQuestionWithTagsAndNames(
	id: string,
	data: QuestionsDTO,
	tagIds: string[] = [],
	tagNames: string[] = [],
	plantId: string
): Promise<QuestionWithTagsDTO | null> {
	try {
		const question = await editQuestion(id, data);

		if (!question) {
			return null;
		}

		const processedTagIds = await processTagIdsAndNames(tagIds, tagNames, plantId);
		await assignTagsToQuestion(id, processedTagIds);
		const tags = await getTagsForQuestion(id, plantId);

		return {
			...question,
			tags
		};
	} catch (error) {
		console.error(error);
		throw new Error('Failed to edit question with tags and names');
	}
}
