import { db } from '$lib/db/db.server';
import { plantRolesTable } from '$lib/db/schema/roles';
import { eq } from 'drizzle-orm';

/**
 * Default role templates for new plants
 */
const DEFAULT_PLANT_ROLES = [
	{
		name: 'auditor',
		permissions: {
			audits: ['viewAudits', 'viewAuditResults', 'evaluateAudit', 'exportAudit'],
			workplaces: ['viewWorkplaces', 'viewWorkplaceDetails'],
			auditTypes: [],
			plantSettings: [],
			plantEvalTypes: [],
			checkpoints: [],
			tags: []
		}
	},
	{
		name: 'workplaceManager',
		permissions: {
			audits: [
				'viewAudits',
				'createAudit',
				'editAudit',
				'deleteAudit',
				'viewAuditResults',
				'exportAudit'
			],
			workplaces: ['viewWorkplaces', 'editWorkplace', 'viewWorkplaceDetails'],
			auditTypes: [],
			plantSettings: [],
			plantEvalTypes: [],
			checkpoints: [],
			tags: []
		}
	},
	{
		name: 'manager',
		permissions: {
			audits: ['viewAudits', 'viewAuditResults', 'exportAudit'],
			workplaces: ['viewWorkplaces', 'editWorkplace', 'viewWorkplaceDetails'],
			auditTypes: [],
			plantSettings: [],
			plantEvalTypes: [],
			checkpoints: [],
			tags: []
		}
	},
	{
		name: 'admin',
		permissions: {
			audits: ['*'],
			workplaces: ['*'],
			auditTypes: ['*'],
			plantSettings: ['*'],
			plantEvalTypes: ['*'],
			checkpoints: ['*'],
			tags: ['*']
		}
	}
];

/**
 * Create default roles for a new plant
 */
export async function createDefaultPlantRoles(plantId: string): Promise<void> {
	try {
		const rolesToInsert = DEFAULT_PLANT_ROLES.map((role) => ({
			name: role.name,
			permissions: role.permissions,
			plantId: plantId
		}));

		await db.insert(plantRolesTable).values(rolesToInsert);

		console.log(`✅ Created ${DEFAULT_PLANT_ROLES.length} default roles for plant ${plantId}`);
	} catch (error) {
		console.error('Error creating default plant roles:', error);
		throw new Error('Failed to create default plant roles');
	}
}

/**
 * Create superadmin role (plantId = null)
 */
export async function createSuperAdminRole(): Promise<void> {
	try {
		const existingSuperAdmin = await db
			.select()
			.from(plantRolesTable)
			.where(eq(plantRolesTable.name, 'superadmin'))
			.limit(1);

		if (existingSuperAdmin.length > 0) {
			console.log('Superadmin role already exists');
			return;
		}

		await db.insert(plantRolesTable).values({
			name: 'superadmin',
			permissions: { '*': ['*'] },
			plantId: null
		});

		console.log('✅ Created superadmin role');
	} catch (error) {
		console.error('Error creating superadmin role:', error);
		throw new Error('Failed to create superadmin role');
	}
}

/**
 * Get all roles for a specific plant
 */
export async function getPlantRoles(plantId: string) {
	try {
		return await db.select().from(plantRolesTable).where(eq(plantRolesTable.plantId, plantId));
	} catch (error) {
		console.error('Error fetching plant roles:', error);
		throw new Error('Failed to fetch plant roles');
	}
}

/**
 * Get role by ID
 */
export async function getPlantRoleById(roleId: string) {
	try {
		const [role] = await db
			.select()
			.from(plantRolesTable)
			.where(eq(plantRolesTable.id, roleId))
			.limit(1);

		return role || null;
	} catch (error) {
		console.error('Error fetching plant role:', error);
		throw new Error('Failed to fetch plant role');
	}
}
