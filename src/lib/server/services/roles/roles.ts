import { db } from '$lib/db/db.server';
import { rolesTable, userRolePlantsTable } from '$lib/db/schema/roles';
import type { Role } from '$lib/models/roleModel';
import { and, eq, isNull } from 'drizzle-orm';

export async function loadPlantRole(
	userId: string,
	plantId: string | null,
	roleId: string
): Promise<Role | null> {
	try {
		const role = await db.transaction(async (tx) => {
			//Case for superadmin
			const plantCondition =
				plantId === null
					? isNull(userRolePlantsTable.plantId)
					: eq(userRolePlantsTable.plantId, plantId);

			const [userPlantRole] = await tx
				.select()
				.from(userRolePlantsTable)
				.where(
					and(
						eq(userRolePlantsTable.userId, userId),
						plantCondition,
						eq(userRolePlantsTable.roleId, roleId)
					)
				);

			if (!userPlantRole) {
				return null; // User does not have this role in the plant
			}

			const [roleResult] = await tx.select().from(rolesTable).where(eq(rolesTable.id, roleId));

			return roleResult ?? null;
		});
		return role;
	} catch (error) {
		console.error('Error loading plant role:', error);
		throw new Error('Failed to load plant role');
	}
}
