import { db } from '$lib/db/db.server';
import { auditAnswersTable } from '$lib/db/schema/audits';
import { eq } from 'drizzle-orm';

/**
 * Normalize evaluation value to 0-1 scale based on evaluation type
 */
export function normalizeEvaluationValue(
	value: string,
	evaluationType: string,
	config?: { pointsRangeMin?: number; pointsRangeMax?: number } | null
): number | null {
	if (!value || value === 'na' || value === 'NotVisited') {
		return null;
	}

	switch (evaluationType) {
		case 'oknok': {
			if (value === 'ok') return 1;
			if (value === 'nok') return 0;
			return null;
		}

		case 'yesno': {
			if (value === 'yes') return 1;
			if (value === 'no') return 0;
			return null;
		}

		case 'yesno_inverse': {
			if (value === 'no_inverse') return 1;
			if (value === 'yes_inverse') return 0;
			return null;
		}

		case 'meetsreservations': {
			if (value === 'meets') return 1;
			if (value === 'wreservations') return 0.5;
			if (value === 'doesntmeet') return 0;
			return null;
		}

		case 'points': {
			const pointValue = parseFloat(value);
			if (isNaN(pointValue)) return null;

			// Use config range if available, otherwise default to 0-10
			const min = config?.pointsRangeMin ?? 0;
			const max = config?.pointsRangeMax ?? 10;
			const range = max - min;

			if (range <= 0) return null;

			// Normalize: (value - min) / (max - min)
			const normalized = (pointValue - min) / range;
			return Math.max(0, Math.min(1, normalized));
		}

		case 'percentage': {
			const percentValue = parseFloat(value);
			if (isNaN(percentValue)) return null;
			return Math.max(0, Math.min(1, percentValue / 100));
		}

		default: {
			const lowerValue = value.toLowerCase();
			if (lowerValue === 'yes' || lowerValue === 'ok' || lowerValue === 'no_inverse') return 1;
			if (lowerValue === 'no' || lowerValue === 'nok' || lowerValue === 'yes_inverse') return 0;
			return null;
		}
	}
}

/*
 * Calculate the success rate of audit using pre-computed normalized values
 */
export async function computeSuccessRateOfAudit(auditId: string): Promise<number> {
	try {
		const answers = await db
			.select({
				normalizedValue: auditAnswersTable.normalizedValue
			})
			.from(auditAnswersTable)
			.where(eq(auditAnswersTable.auditId, auditId));

		if (answers.length === 0) {
			return 0;
		}

		// Filter out null values and convert to numbers
		const validValues = answers
			.map((answer) => (answer.normalizedValue ? parseFloat(answer.normalizedValue) : null))
			.filter((value): value is number => value !== null && !isNaN(value));

		if (validValues.length === 0) {
			return 0;
		}

		const average = validValues.reduce((sum, value) => sum + value, 0) / validValues.length;
		return Math.round(average * 100);
	} catch (error) {
		console.error('Error calculating audit success rate:', error);
		return 0;
	}
}
