import { db } from '$lib/db/db.server';
import { auditTypeEvaluationConfigTable } from '$lib/db/schema/audits';
import { plantsEvaluationConfigurationTable } from '$lib/db/schema/company';
import { eq } from 'drizzle-orm';
import type {
	EvaluationConfigForm,
	PlantEvaluationConfigForm
} from '$lib/schemas/evaluationConfig';

export async function getAuditTypeEvaluationConfig(auditTypeId: string, plantId: string) {
	const [typeConfig] = await db
		.select()
		.from(auditTypeEvaluationConfigTable)
		.where(eq(auditTypeEvaluationConfigTable.auditTypeId, auditTypeId));

	const [plantConfig] = await db
		.select()
		.from(plantsEvaluationConfigurationTable)
		.where(eq(plantsEvaluationConfigurationTable.plantId, plantId));

	if (!plantConfig) {
		console.warn('Plant evaluation config not found\n Using default values from plant config');
	}

	return {
		evaluationMode: typeConfig?.evaluationMode ?? 'percentage',
		evalWeight_wReservations: parseFloat(
			typeConfig?.evalWeight_wReservations ?? plantConfig?.evalWeight_wReservations ?? '0.5'
		),
		pointsRangeMin: typeConfig?.pointsRangeMin ?? plantConfig?.pointsRangeMin ?? 0,
		pointsRangeMax: typeConfig?.pointsRangeMax ?? plantConfig?.pointsRangeMax ?? 100,
		pointsAvgThreshold: typeConfig?.pointsAvgThreshold ?? plantConfig?.pointsAvgThreshold ?? 5,
		pointsSuccessThreshold:
			typeConfig?.pointsSuccessThreshold ?? plantConfig?.pointsSuccessThreshold ?? 8,
		percentageThreshold_average:
			typeConfig?.percentageThreshold_average ?? plantConfig?.percentageThreshold_average ?? 70,
		percentageThreshold_success:
			typeConfig?.percentageThreshold_success ?? plantConfig?.percentageThreshold_success ?? 90,
		auditThreshold_average:
			typeConfig?.auditThreshold_average ?? plantConfig?.auditThreshold_average ?? 70,
		auditThreshold_success:
			typeConfig?.auditThreshold_success ?? plantConfig?.auditThreshold_success ?? 90,
		average_averageAuditAnswers:
			typeConfig?.average_averageAuditAnswers ?? plantConfig?.average_averageAuditAnswers ?? null,
		average_badAuditAnswers:
			typeConfig?.average_badAuditAnswers ?? plantConfig?.average_badAuditAnswers ?? null,
		notSuccessful_averageAnswers:
			typeConfig?.notSuccessful_averageAnswers ?? plantConfig?.notSuccessful_averageAnswers ?? null,
		notSuccessful_badAnswers:
			typeConfig?.notSuccessful_badAnswers ?? plantConfig?.notSuccessful_badAnswers ?? null
	};
}

export async function saveAuditTypeEvaluationConfig(
	auditTypeId: string,
	plantId: string,
	data: EvaluationConfigForm
) {
	const [plantConfig] = await db
		.select()
		.from(plantsEvaluationConfigurationTable)
		.where(eq(plantsEvaluationConfigurationTable.plantId, plantId));

	if (!plantConfig) {
		throw new Error('Plant evaluation config not found');
	}

	const [existingConfig] = await db
		.select()
		.from(auditTypeEvaluationConfigTable)
		.where(eq(auditTypeEvaluationConfigTable.auditTypeId, auditTypeId));

	const configData: Record<string, unknown> = { auditTypeId };

	configData.evaluationMode = data.evaluationMode;

	if (
		parseFloat(data.evalWeight_wReservations.toString()) !==
		parseFloat(plantConfig.evalWeight_wReservations ?? '0.5')
	) {
		configData.evalWeight_wReservations = data.evalWeight_wReservations.toString();
	}

	if (data.pointsRangeMin !== (plantConfig.pointsRangeMin ?? 0)) {
		configData.pointsRangeMin = data.pointsRangeMin;
	}

	if (data.pointsRangeMax !== (plantConfig.pointsRangeMax ?? 100)) {
		configData.pointsRangeMax = data.pointsRangeMax;
	}

	if (data.pointsAvgThreshold !== (plantConfig.pointsAvgThreshold ?? 5)) {
		configData.pointsAvgThreshold = data.pointsAvgThreshold;
	}

	if (data.pointsSuccessThreshold !== (plantConfig.pointsSuccessThreshold ?? 8)) {
		configData.pointsSuccessThreshold = data.pointsSuccessThreshold;
	}

	if (data.percentageThreshold_average !== (plantConfig.percentageThreshold_average ?? 70)) {
		configData.percentageThreshold_average = data.percentageThreshold_average;
	}

	if (data.percentageThreshold_success !== (plantConfig.percentageThreshold_success ?? 90)) {
		configData.percentageThreshold_success = data.percentageThreshold_success;
	}

	if (data.auditThreshold_average !== (plantConfig.auditThreshold_average ?? 70)) {
		configData.auditThreshold_average = data.auditThreshold_average;
	}

	if (data.auditThreshold_success !== (plantConfig.auditThreshold_success ?? 90)) {
		configData.auditThreshold_success = data.auditThreshold_success;
	}

	if (
		data.average_averageAuditAnswers !== null &&
		data.average_averageAuditAnswers !== (plantConfig.average_averageAuditAnswers ?? null)
	) {
		configData.average_averageAuditAnswers = data.average_averageAuditAnswers;
	}

	if (
		data.average_badAuditAnswers !== null &&
		data.average_badAuditAnswers !== (plantConfig.average_badAuditAnswers ?? null)
	) {
		configData.average_badAuditAnswers = data.average_badAuditAnswers;
	}

	if (
		data.notSuccessful_averageAnswers !== null &&
		data.notSuccessful_averageAnswers !== (plantConfig.notSuccessful_averageAnswers ?? null)
	) {
		configData.notSuccessful_averageAnswers = data.notSuccessful_averageAnswers;
	}

	if (
		data.notSuccessful_badAnswers !== null &&
		data.notSuccessful_badAnswers !== (plantConfig.notSuccessful_badAnswers ?? null)
	) {
		configData.notSuccessful_badAnswers = data.notSuccessful_badAnswers;
	}

	if (existingConfig) {
		return await db
			.update(auditTypeEvaluationConfigTable)
			.set({
				...configData,
				updatedAt: new Date()
			})
			.where(eq(auditTypeEvaluationConfigTable.auditTypeId, auditTypeId))
			.returning();
	} else {
		return await db
			.insert(auditTypeEvaluationConfigTable)
			.values(configData as typeof auditTypeEvaluationConfigTable.$inferInsert)
			.returning();
	}
}

export async function getPlantEvaluationConfig(plantId: string) {
	const [plantConfig] = await db
		.select()
		.from(plantsEvaluationConfigurationTable)
		.where(eq(plantsEvaluationConfigurationTable.plantId, plantId));

	if (!plantConfig) {
		console.warn('Plant evaluation config not found\n Using default values from plant config');
	}

	return plantConfig;
}

export async function savePlantEvaluationConfig(plantId: string, data: PlantEvaluationConfigForm) {
	try {
		const result = await db
			.update(plantsEvaluationConfigurationTable)
			.set({
				evalWeight_wReservations: data.evalWeight_wReservations.toString(),
				pointsRangeMin: data.pointsRangeMin,
				pointsRangeMax: data.pointsRangeMax,
				pointsAvgThreshold: data.pointsAvgThreshold,
				pointsSuccessThreshold: data.pointsSuccessThreshold,
				percentageThreshold_average: data.percentageThreshold_average,
				percentageThreshold_success: data.percentageThreshold_success,
				auditThreshold_average: data.auditThreshold_average,
				auditThreshold_success: data.auditThreshold_success,
				average_averageAuditAnswers: data.average_averageAuditAnswers ?? undefined,
				average_badAuditAnswers: data.average_badAuditAnswers ?? undefined,
				notSuccessful_averageAnswers: data.notSuccessful_averageAnswers ?? undefined,
				notSuccessful_badAnswers: data.notSuccessful_badAnswers ?? undefined,
				updatedAt: new Date()
			})
			.where(eq(plantsEvaluationConfigurationTable.plantId, plantId))
			.returning();

		return result;
	} catch (error) {
		console.error('Error saving plant evaluation config:', error);
		throw new Error('Failed to save plant evaluation config');
	}
}
