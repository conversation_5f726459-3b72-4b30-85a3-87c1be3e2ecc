import { eq } from 'drizzle-orm';

import type { CompaniesListDTO } from '$lib/DTO/admin/companies/CompaniesListDTO';
import { companyTable } from '$lib/db/schema/company';
import { db } from '$lib/db/db.server';
import type { CompanyForm } from '$lib/schemas/company';

export async function getAllCompanies(showDeleted: boolean = false): Promise<CompaniesListDTO[]> {
	try {
		const result = await db
			.select()
			.from(companyTable)
			.where(showDeleted ? undefined : eq(companyTable.deleted, false));
		return result ?? [];
	} catch (error) {
		console.error('Error getting all companies:', error);
		return [];
	}
}

export async function getCompanyById(id: string): Promise<CompaniesListDTO | null> {
	try {
		const [result] = await db.select().from(companyTable).where(eq(companyTable.id, id));
		if (!result) return null;

		return result;
	} catch (error) {
		console.error('Error getting company by id:', error);
		return null;
	}
}

export async function createCompany(data: CompanyForm): Promise<CompaniesListDTO> {
	try {
		const [result] = await db.insert(companyTable).values(data).returning();

		if (!result) {
			throw new Error('Failed to create company');
		}
		return result;
	} catch (error) {
		console.error('Error creating company:', error);
		throw new Error('Failed to create company');
	}
}

export async function updateCompany(id: string, data: CompanyForm): Promise<CompaniesListDTO> {
	try {
		const [result] = await db
			.update(companyTable)
			.set(data)
			.where(eq(companyTable.id, id))
			.returning();

		if (!result) {
			throw new Error('Company not found');
		}

		return result;
	} catch (error) {
		console.error('Error updating company:', error);
		throw new Error('Failed to update company');
	}
}

export async function deleteCompany(id: string) {
	try {
		const result = await db
			.update(companyTable)
			.set({ deleted: true, deletedAt: new Date() })
			.where(eq(companyTable.id, id))
			.returning();
		if (result.length === 0) {
			return { success: false, error: 'Company not found' };
		}
		return { success: true };
	} catch (error) {
		console.error('Error deleting company:', error);
		return { success: false, error: 'Failed to delete company' };
	}
}
