import {
	createCompany,
	deleteCompany,
	getAllCompanies,
	getCompanyById,
	updateCompany
} from './companies';
import { createPlant, deletePlant, getAllPlants, getPlantById, updatePlant } from './plants';
import {
	getAllUsers,
	updateUser,
	assignUserToPlantAndCompany,
	updateUserAssignment,
	deleteUser
} from './users';

export const SuperAdminService = {
	//users
	getAllUsers,
	getAllPlants,
	updateUser,
	deleteUser,
	assignUserToPlantAndCompany,
	updateUserAssignment
};

export const SuperAdminCompanyService = {
	getAllCompanies,
	getCompanyById,
	createCompany,
	updateCompany,
	deleteCompany
};

export const SuperAdminPlantService = {
	getAllPlants,
	getPlantById,
	createPlant,
	updatePlant,
	deletePlant
};
