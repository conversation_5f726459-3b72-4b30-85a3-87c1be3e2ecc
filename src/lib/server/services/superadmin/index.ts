import {
	createCompany,
	deleteCompany,
	getAllCompanies,
	getCompanyById,
	updateCompany
} from './companies';
import { createPlant, deletePlant, getAllPlants, getPlantById, updatePlant } from './plants';
import {
	getAllUsers,
	updateUser,
	assignUserToPlantAndCompany,
	updateUserAssignment,
	deleteUser,
	getUserById,
	getAvailablePlantRoles,
	getUserPlantRoleAssignments,
	assignUserToPlantWithRole,
	removeUserFromPlant
} from './users';

export const SuperAdminService = {
	//users
	getAllUsers,
	getUserById,
	getAllPlants,
	updateUser,
	deleteUser,
	assignUserToPlantAndCompany,
	updateUserAssignment,
	// New plant-role functions
	getAvailablePlantRoles,
	getUserPlantRoleAssignments,
	assignUserToPlantWithRole,
	removeUserFromPlant
};

export const SuperAdminCompanyService = {
	getAllCompanies,
	getCompanyById,
	createCompany,
	updateCompany,
	deleteCompany
};

export const SuperAdminPlantService = {
	getAllPlants,
	getPlantById,
	createPlant,
	updatePlant,
	deletePlant
};
