import { eq } from 'drizzle-orm';
import { db } from '$lib/db/db.server';
import {
	plantsTable,
	companyTable,
	plantsConfigurationTable,
	plantsEvaluationConfigurationTable
} from '$lib/db/schema/company';
import type { PlantForm } from '$lib/schemas/company';
import type { PlantsListDTO } from '$lib/DTO/admin/plants/PlantsListDTO';

export async function getAllPlants(showDeleted: boolean = false): Promise<PlantsListDTO[]> {
	try {
		const result = await db
			.select({
				id: plantsTable.id,
				name: plantsTable.name,
				code: plantsTable.code,
				slug: plantsTable.slug,
				active: plantsTable.active,
				url: plantsTable.url,
				countryCode: plantsTable.countryCode,
				gpsLocation: plantsTable.gpsLocation,
				numberOfLicenses: plantsTable.numberOfLicenses,
				company: {
					id: companyTable.id,
					name: companyTable.name,
					code: companyTable.code
				},
				eKaizenFormURL: plantsTable.eKaizenFormURL,
				tasksEnabled: plantsTable.tasksEnabled,

				createdAt: plantsTable.createdAt,
				updatedAt: plantsTable.updatedAt
			})
			.from(plantsTable)
			.leftJoin(companyTable, eq(plantsTable.companyId, companyTable.id))
			.where(showDeleted ? undefined : eq(plantsTable.deleted, false));

		return result;
	} catch (error) {
		console.error('Error getting all plants:', error);
		return [];
	}
}

export async function getPlantById(id: string): Promise<PlantsListDTO | null> {
	try {
		const [result] = await db
			.select({
				id: plantsTable.id,
				name: plantsTable.name,
				code: plantsTable.code,
				slug: plantsTable.slug,
				active: plantsTable.active,
				url: plantsTable.url,
				countryCode: plantsTable.countryCode,
				gpsLocation: plantsTable.gpsLocation,
				numberOfLicenses: plantsTable.numberOfLicenses,
				company: {
					id: companyTable.id,
					name: companyTable.name,
					code: companyTable.code
				},
				eKaizenFormURL: plantsTable.eKaizenFormURL,
				tasksEnabled: plantsTable.tasksEnabled,

				createdAt: plantsTable.createdAt,
				updatedAt: plantsTable.updatedAt
			})
			.from(plantsTable)
			.leftJoin(companyTable, eq(plantsTable.companyId, companyTable.id))
			.where(eq(plantsTable.id, id));

		return result || null;
	} catch (error) {
		console.error('Error getting plant by id:', error);
		return null;
	}
}

//Create plant and add config and evaluation config for newly created plant :-)
export async function createPlant(data: PlantForm): Promise<PlantsListDTO | null> {
	try {
		const [result] = await db.transaction(async (tx) => {
			const [newPlant] = await tx.insert(plantsTable).values(data).returning();

			await tx.insert(plantsConfigurationTable).values({
				plantId: newPlant.id,
				supportedLanguages: ['en'],
				defaultLanguage: 'en'
			});

			await tx.insert(plantsEvaluationConfigurationTable).values({
				plantId: newPlant.id
			});

			return [newPlant];
		});

		return await getPlantById(result.id);
	} catch (error) {
		console.error('Error creating plant:', error);
		throw new Error('Failed to create plant');
	}
}

export async function updatePlant(id: string, data: PlantForm): Promise<PlantsListDTO | null> {
	try {
		const [updatedPlant] = await db
			.update(plantsTable)
			.set(data)
			.where(eq(plantsTable.id, id))
			.returning();

		if (!updatedPlant) {
			throw new Error('Plant not found');
		}
		return await getPlantById(updatedPlant.id);
	} catch (error) {
		console.error('Error updating plant:', error);
		throw new Error('Failed to update plant');
	}
}

export async function deletePlant(id: string) {
	try {
		const result = await db
			.update(plantsTable)
			.set({ deleted: true, deletedAt: new Date() })
			.where(eq(plantsTable.id, id))
			.returning();
		if (result.length === 0) {
			return { success: false, error: 'Plant not found' };
		}
		return { success: true };
	} catch (error) {
		console.error('Error deleting plant:', error);
		throw new Error('Failed to delete plant');
	}
}
