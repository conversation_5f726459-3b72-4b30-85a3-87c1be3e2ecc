import { db } from '$lib/db/db.server';
import { usersTable, usersWorkInfoTable } from '$lib/db/schema/user';
import { companyTable, plantsTable } from '$lib/db/schema/company';
import { userRolePlantsTable, plantRolesTable } from '$lib/db/schema/roles';
import type { UserListDTO } from '$lib/DTO/admin/users/UsersListDTO';
import type { UpdateUserForm } from '$lib/schemas/admin';
import { eq, and } from 'drizzle-orm';

export async function getAllUsers(showDeleted: boolean = false): Promise<UserListDTO[]> {
	try {
		const result = await db
			.select({
				id: usersTable.id,
				firstName: usersTable.firstName,
				lastName: usersTable.lastName,
				email: usersTable.email,
				phone: usersTable.phone,
				active: usersTable.active,
				cardNumber: usersWorkInfoTable.cardNumber,
				company: {
					id: companyTable.id,
					name: companyTable.name,
					code: companyTable.code
				},
				mainPlant: {
					id: plantsTable.id,
					name: plantsTable.name,
					code: plantsTable.code
				}
			})
			.from(usersTable)
			.leftJoin(companyTable, eq(usersTable.companyId, companyTable.id))
			.leftJoin(usersWorkInfoTable, eq(usersTable.id, usersWorkInfoTable.userId))
			.leftJoin(plantsTable, eq(usersWorkInfoTable.plantId, plantsTable.id))
			.where(showDeleted ? undefined : eq(usersTable.deleted, false));

		return result || [];
	} catch (error) {
		console.error('Error fetching existing users:', error);
		throw new Error('Failed to fetch existing users');
	}
}

export async function getUserById(userId: string): Promise<UserListDTO | null> {
	try {
		const [result] = await db
			.select({
				id: usersTable.id,
				firstName: usersTable.firstName,
				lastName: usersTable.lastName,
				email: usersTable.email,
				phone: usersTable.phone,
				active: usersTable.active,
				cardNumber: usersWorkInfoTable.cardNumber,
				company: {
					id: companyTable.id,
					name: companyTable.name,
					code: companyTable.code
				},
				mainPlant: {
					id: plantsTable.id,
					name: plantsTable.name,
					code: plantsTable.code
				}
			})
			.from(usersTable)
			.leftJoin(companyTable, eq(usersTable.companyId, companyTable.id))
			.leftJoin(usersWorkInfoTable, eq(usersTable.id, usersWorkInfoTable.userId))
			.leftJoin(plantsTable, eq(usersWorkInfoTable.plantId, plantsTable.id))
			.where(eq(usersTable.id, userId));

		return result ?? null;
	} catch (error) {
		console.error('Error fetching user by id:', error);
		throw new Error('Failed to fetch user');
	}
}

export async function updateUser(data: UpdateUserForm): Promise<UserListDTO | null> {
	try {
		return await db.transaction(async (tx) => {
			// Update user basic info
			const [updatedUser] = await tx
				.update(usersTable)
				.set({
					firstName: data.firstName,
					lastName: data.lastName,
					email: data.email,
					phone: data.phone,
					active: data.active,
					companyId: data.companyId
				})
				.where(eq(usersTable.id, data.id))
				.returning();

			if (!updatedUser) {
				throw new Error('Failed to update user');
			}

			// Update plant assignment properly (no more delete+insert!)
			if (data.plantId) {
				// Check if user_plants record exists
				const existingUserPlant = await tx
					.select()
					.from(userRolePlantsTable)
					.where(eq(userRolePlantsTable.userId, data.id))
					.limit(1);

				if (existingUserPlant.length > 0) {
					// Update existing user_plants record
					await tx
						.update(userRolePlantsTable)
						.set({ plantId: data.plantId })
						.where(eq(userRolePlantsTable.userId, data.id));
				} else {
					// Note: This function is deprecated - use assignUserToPlantWithRole instead
					// For backward compatibility, we'll assign a default role
					const defaultRole = await tx
						.select({ id: plantRolesTable.id })
						.from(plantRolesTable)
						.where(
							and(eq(plantRolesTable.plantId, data.plantId), eq(plantRolesTable.name, 'auditor'))
						)
						.limit(1);

					if (defaultRole.length === 0) {
						throw new Error('No default role found for plant');
					}

					await tx.insert(userRolePlantsTable).values({
						userId: data.id,
						plantId: data.plantId,
						roleId: defaultRole[0].id
					});
				}

				// Check if users_work_info record exists
				const existingWorkInfo = await tx
					.select()
					.from(usersWorkInfoTable)
					.where(eq(usersWorkInfoTable.userId, data.id))
					.limit(1);

				if (existingWorkInfo.length > 0) {
					// Update existing users_work_info record
					await tx
						.update(usersWorkInfoTable)
						.set({
							plantId: data.plantId,
							companyId: data.companyId,
							cardNumber: data.cardNumber || existingWorkInfo[0].cardNumber
						})
						.where(eq(usersWorkInfoTable.userId, data.id));
				} else {
					// Create new users_work_info record
					if (data.companyId && data.plantId) {
						await tx.insert(usersWorkInfoTable).values({
							userId: data.id,
							plantId: data.plantId,
							companyId: data.companyId,
							cardNumber: data.cardNumber || ''
						});
					} else {
						throw new Error('companyId and plantId are required to insert into usersWorkInfoTable');
					}
				}
			}

			// Fetch updated user with relations
			const result = await tx
				.select({
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName,
					email: usersTable.email,
					phone: usersTable.phone,
					active: usersTable.active,
					cardNumber: usersWorkInfoTable.cardNumber,
					company: {
						id: companyTable.id,
						name: companyTable.name,
						code: companyTable.code
					},
					mainPlant: {
						id: plantsTable.id,
						name: plantsTable.name,
						code: plantsTable.code
					}
				})
				.from(usersTable)
				.leftJoin(companyTable, eq(usersTable.companyId, companyTable.id))
				.leftJoin(usersWorkInfoTable, eq(usersTable.id, usersWorkInfoTable.userId))
				.leftJoin(plantsTable, eq(usersWorkInfoTable.plantId, plantsTable.id))
				.where(eq(usersTable.id, data.id));

			const user = result[0];
			return user
				? {
						...user,
						cardNumber: user.cardNumber
					}
				: null;
		});
	} catch (error) {
		console.error('Error updating user:', error);
		throw new Error('Failed to update user');
	}
}

/**
 * Assign user to a plant and company after registration
 * This creates records in user_plants and users_work_info tables
 */
export async function assignUserToPlantAndCompany(
	userId: string,
	plantId: string,
	companyId: string,
	cardNumber?: string
): Promise<{ success: boolean }> {
	try {
		return await db.transaction(async (tx) => {
			// Update user's companyId if provided
			if (companyId) {
				await tx.update(usersTable).set({ companyId }).where(eq(usersTable.id, userId));
			}

			// Note: This function is deprecated - use assignUserToPlantWithRole instead
			// For backward compatibility, we'll assign a default role
			const defaultRole = await tx
				.select({ id: plantRolesTable.id })
				.from(plantRolesTable)
				.where(and(eq(plantRolesTable.plantId, plantId), eq(plantRolesTable.name, 'auditor')))
				.limit(1);

			if (defaultRole.length === 0) {
				throw new Error('No default role found for plant');
			}

			await tx.insert(userRolePlantsTable).values({
				userId,
				plantId,
				roleId: defaultRole[0].id
			});

			// Create users_work_info record with plant assignment and card number
			await tx.insert(usersWorkInfoTable).values({
				userId,
				plantId,
				companyId,
				cardNumber: cardNumber || ''
			});

			return { success: true };
		});
	} catch (error) {
		console.error('Error assigning user to plant and company:', error);
		throw new Error('Failed to assign user to plant and company');
	}
}

/**
 * Update user's plant and company assignment using proper updates instead of delete+insert
 */
export async function updateUserAssignment(
	userId: string,
	plantId?: string,
	companyId?: string,
	cardNumber?: string
) {
	try {
		return await db.transaction(async (tx) => {
			// Update user's companyId if provided
			if (companyId) {
				await tx.update(usersTable).set({ companyId }).where(eq(usersTable.id, userId));
			}

			if (plantId) {
				// Check if user_plants record exists
				const existingUserPlant = await tx
					.select()
					.from(userRolePlantsTable)
					.where(eq(userRolePlantsTable.userId, userId))
					.limit(1);

				if (existingUserPlant.length > 0) {
					// Update existing user_plants record
					await tx
						.update(userRolePlantsTable)
						.set({ plantId })
						.where(eq(userRolePlantsTable.userId, userId));
				} else {
					// Note: This function is deprecated - use assignUserToPlantWithRole instead
					// For backward compatibility, we'll assign a default role
					const defaultRole = await tx
						.select({ id: plantRolesTable.id })
						.from(plantRolesTable)
						.where(and(eq(plantRolesTable.plantId, plantId), eq(plantRolesTable.name, 'auditor')))
						.limit(1);

					if (defaultRole.length === 0) {
						throw new Error('No default role found for plant');
					}

					await tx.insert(userRolePlantsTable).values({
						userId,
						plantId,
						roleId: defaultRole[0].id
					});
				}

				// Check if users_work_info record exists
				const existingWorkInfo = await tx
					.select()
					.from(usersWorkInfoTable)
					.where(eq(usersWorkInfoTable.userId, userId))
					.limit(1);

				if (existingWorkInfo.length > 0) {
					// Update existing users_work_info record
					await tx
						.update(usersWorkInfoTable)
						.set({
							companyId,
							plantId,
							cardNumber: cardNumber || existingWorkInfo[0].cardNumber
						})
						.where(eq(usersWorkInfoTable.userId, userId));
				} else {
					// Create new users_work_info record
					if (companyId && plantId) {
						await tx.insert(usersWorkInfoTable).values({
							userId,
							companyId,
							plantId,
							cardNumber: cardNumber || ''
						});
					} else {
						throw new Error('companyId and plantId are required to insert into usersWorkInfoTable');
					}
				}
			}

			return { success: true };
		});
	} catch (error) {
		console.error('Error updating user assignment:', error);
		throw new Error('Failed to update user assignment');
	}
}

export async function deleteUser(userId: string): Promise<{ success: boolean }> {
	try {
		await db.transaction(async (tx) => {
			await tx
				.update(usersTable)
				.set({ deleted: true, deletedAt: new Date() })
				.where(eq(usersTable.id, userId));

			await tx
				.update(usersWorkInfoTable)
				.set({ deleted: true, deletedAt: new Date() })
				.where(eq(usersWorkInfoTable.userId, userId));
			await tx
				.update(userRolePlantsTable)
				.set({ deletedUser: true, deletedAt: new Date() })
				.where(eq(userRolePlantsTable.userId, userId));
		});

		return { success: true };
	} catch (error) {
		console.error('Error deleting user:', error);
		throw new Error('Failed to delete user');
	}
}

/**
 * Get available roles for a specific plant
 */
export async function getAvailablePlantRoles(plantId: string) {
	try {
		return await db
			.select({
				id: plantRolesTable.id,
				name: plantRolesTable.name,
				permissions: plantRolesTable.permissions
			})
			.from(plantRolesTable)
			.where(eq(plantRolesTable.plantId, plantId));
	} catch (error) {
		console.error('Error fetching available plant roles:', error);
		throw new Error('Failed to fetch available plant roles');
	}
}

/**
 * Get user's current plant role assignments with role details
 */
export async function getUserPlantRoleAssignments(userId: string) {
	try {
		return await db
			.select({
				id: userRolePlantsTable.id,
				userId: userRolePlantsTable.userId,
				roleId: userRolePlantsTable.roleId,
				plantId: userRolePlantsTable.plantId,
				roleName: plantRolesTable.name,
				rolePermissions: plantRolesTable.permissions,
				plant: {
					id: plantsTable.id,
					name: plantsTable.name,
					code: plantsTable.code
				}
			})
			.from(userRolePlantsTable)
			.innerJoin(plantRolesTable, eq(userRolePlantsTable.roleId, plantRolesTable.id))
			.leftJoin(plantsTable, eq(userRolePlantsTable.plantId, plantsTable.id))
			.where(eq(userRolePlantsTable.userId, userId));
	} catch (error) {
		console.error('Error fetching user plant role assignments:', error);
		throw new Error('Failed to fetch user plant role assignments');
	}
}

/**
 * Assign user to plant with specific role
 */
export async function assignUserToPlantWithRole(
	userId: string,
	plantId: string,
	roleId: string,
	companyId: string,
	cardNumber?: string,
	isMainPlant: boolean = false
): Promise<{ success: boolean }> {
	try {
		return await db.transaction(async (tx) => {
			// Update user's companyId if provided
			if (companyId) {
				await tx.update(usersTable).set({ companyId }).where(eq(usersTable.id, userId));
			}

			// Check if user already has this role in this plant
			const existingAssignment = await tx
				.select()
				.from(userRolePlantsTable)
				.where(
					and(
						eq(userRolePlantsTable.userId, userId),
						eq(userRolePlantsTable.plantId, plantId),
						eq(userRolePlantsTable.roleId, roleId)
					)
				)
				.limit(1);

			if (existingAssignment.length === 0) {
				// Create user_role_plants record
				await tx.insert(userRolePlantsTable).values({
					userId,
					plantId,
					roleId
				});
			}

			// If this is the main plant, update work info
			if (isMainPlant) {
				const existingWorkInfo = await tx
					.select()
					.from(usersWorkInfoTable)
					.where(eq(usersWorkInfoTable.userId, userId))
					.limit(1);

				if (existingWorkInfo.length > 0) {
					await tx
						.update(usersWorkInfoTable)
						.set({
							plantId,
							companyId,
							cardNumber: cardNumber || existingWorkInfo[0].cardNumber
						})
						.where(eq(usersWorkInfoTable.userId, userId));
				} else {
					await tx.insert(usersWorkInfoTable).values({
						userId,
						plantId,
						companyId,
						cardNumber: cardNumber || ''
					});
				}
			}

			return { success: true };
		});
	} catch (error) {
		console.error('Error assigning user to plant with role:', error);
		throw new Error('Failed to assign user to plant with role');
	}
}

/**
 * Remove user from plant (remove all roles in that plant)
 */
export async function removeUserFromPlant(
	userId: string,
	plantId: string
): Promise<{ success: boolean }> {
	try {
		await db
			.delete(userRolePlantsTable)
			.where(and(eq(userRolePlantsTable.userId, userId), eq(userRolePlantsTable.plantId, plantId)));

		return { success: true };
	} catch (error) {
		console.error('Error removing user from plant:', error);
		throw new Error('Failed to remove user from plant');
	}
}
