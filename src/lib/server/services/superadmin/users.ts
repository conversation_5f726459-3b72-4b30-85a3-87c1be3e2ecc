import { db } from '$lib/db/db.server';
import { usersTable, usersWorkInfoTable, userPlantsTable } from '$lib/db/schema/user';
import { companyTable, plantsTable } from '$lib/db/schema/company';
import type { UserListDTO } from '$lib/DTO/admin/users/UsersListDTO';
import type { UpdateUserForm } from '$lib/schemas/admin';
import { eq } from 'drizzle-orm';

export async function getAllUsers(showDeleted: boolean = false): Promise<UserListDTO[]> {
	try {
		const result = await db
			.select({
				id: usersTable.id,
				firstName: usersTable.firstName,
				lastName: usersTable.lastName,
				email: usersTable.email,
				phone: usersTable.phone,
				active: usersTable.active,
				cardNumber: usersWorkInfoTable.cardNumber,
				company: {
					id: companyTable.id,
					name: companyTable.name,
					code: companyTable.code
				},
				mainPlant: {
					id: plantsTable.id,
					name: plantsTable.name,
					code: plantsTable.code
				}
			})
			.from(usersTable)
			.leftJoin(companyTable, eq(usersTable.companyId, companyTable.id))
			.leftJoin(usersWorkInfoTable, eq(usersTable.id, usersWorkInfoTable.userId))
			.leftJoin(plantsTable, eq(usersWorkInfoTable.plantId, plantsTable.id))
			.where(showDeleted ? undefined : eq(usersTable.deleted, false));

		return result || [];
	} catch (error) {
		console.error('Error fetching existing users:', error);
		throw new Error('Failed to fetch existing users');
	}
}

export async function updateUser(data: UpdateUserForm): Promise<UserListDTO | null> {
	try {
		return await db.transaction(async (tx) => {
			// Update user basic info
			const [updatedUser] = await tx
				.update(usersTable)
				.set({
					firstName: data.firstName,
					lastName: data.lastName,
					email: data.email,
					phone: data.phone,
					active: data.active,
					companyId: data.companyId
				})
				.where(eq(usersTable.id, data.id))
				.returning();

			if (!updatedUser) {
				throw new Error('Failed to update user');
			}

			// Update plant assignment properly (no more delete+insert!)
			if (data.plantId) {
				// Check if user_plants record exists
				const existingUserPlant = await tx
					.select()
					.from(userPlantsTable)
					.where(eq(userPlantsTable.userId, data.id))
					.limit(1);

				if (existingUserPlant.length > 0) {
					// Update existing user_plants record
					await tx
						.update(userPlantsTable)
						.set({ plantId: data.plantId })
						.where(eq(userPlantsTable.userId, data.id));
				} else {
					// Create new user_plants record
					await tx.insert(userPlantsTable).values({
						userId: data.id,
						plantId: data.plantId
					});
				}

				// Check if users_work_info record exists
				const existingWorkInfo = await tx
					.select()
					.from(usersWorkInfoTable)
					.where(eq(usersWorkInfoTable.userId, data.id))
					.limit(1);

				if (existingWorkInfo.length > 0) {
					// Update existing users_work_info record
					await tx
						.update(usersWorkInfoTable)
						.set({
							plantId: data.plantId,
							companyId: data.companyId,
							cardNumber: data.cardNumber || existingWorkInfo[0].cardNumber
						})
						.where(eq(usersWorkInfoTable.userId, data.id));
				} else {
					// Create new users_work_info record
					if (data.companyId && data.plantId) {
						await tx.insert(usersWorkInfoTable).values({
							userId: data.id,
							plantId: data.plantId,
							companyId: data.companyId,
							cardNumber: data.cardNumber || ''
						});
					} else {
						throw new Error('companyId and plantId are required to insert into usersWorkInfoTable');
					}
				}
			}

			// Fetch updated user with relations
			const result = await tx
				.select({
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName,
					email: usersTable.email,
					phone: usersTable.phone,
					active: usersTable.active,
					cardNumber: usersWorkInfoTable.cardNumber,
					company: {
						id: companyTable.id,
						name: companyTable.name,
						code: companyTable.code
					},
					mainPlant: {
						id: plantsTable.id,
						name: plantsTable.name,
						code: plantsTable.code
					}
				})
				.from(usersTable)
				.leftJoin(companyTable, eq(usersTable.companyId, companyTable.id))
				.leftJoin(usersWorkInfoTable, eq(usersTable.id, usersWorkInfoTable.userId))
				.leftJoin(plantsTable, eq(usersWorkInfoTable.plantId, plantsTable.id))
				.where(eq(usersTable.id, data.id));

			const user = result[0];
			return user
				? {
						...user,
						cardNumber: user.cardNumber
					}
				: null;
		});
	} catch (error) {
		console.error('Error updating user:', error);
		throw new Error('Failed to update user');
	}
}

/**
 * Assign user to a plant and company after registration
 * This creates records in user_plants and users_work_info tables
 */
export async function assignUserToPlantAndCompany(
	userId: string,
	plantId: string,
	companyId: string,
	cardNumber?: string
): Promise<{ success: boolean }> {
	try {
		return await db.transaction(async (tx) => {
			// Update user's companyId if provided
			if (companyId) {
				await tx.update(usersTable).set({ companyId }).where(eq(usersTable.id, userId));
			}

			// Create user_plants record to allow user access to the plant
			await tx.insert(userPlantsTable).values({
				userId,
				plantId
			});

			// Create users_work_info record with plant assignment and card number
			await tx.insert(usersWorkInfoTable).values({
				userId,
				plantId,
				companyId,
				cardNumber: cardNumber || ''
			});

			return { success: true };
		});
	} catch (error) {
		console.error('Error assigning user to plant and company:', error);
		throw new Error('Failed to assign user to plant and company');
	}
}

/**
 * Update user's plant and company assignment using proper updates instead of delete+insert
 */
export async function updateUserAssignment(
	userId: string,
	plantId?: string,
	companyId?: string,
	cardNumber?: string
) {
	try {
		return await db.transaction(async (tx) => {
			// Update user's companyId if provided
			if (companyId) {
				await tx.update(usersTable).set({ companyId }).where(eq(usersTable.id, userId));
			}

			if (plantId) {
				// Check if user_plants record exists
				const existingUserPlant = await tx
					.select()
					.from(userPlantsTable)
					.where(eq(userPlantsTable.userId, userId))
					.limit(1);

				if (existingUserPlant.length > 0) {
					// Update existing user_plants record
					await tx
						.update(userPlantsTable)
						.set({ plantId })
						.where(eq(userPlantsTable.userId, userId));
				} else {
					// Create new user_plants record
					await tx.insert(userPlantsTable).values({
						userId,
						plantId
					});
				}

				// Check if users_work_info record exists
				const existingWorkInfo = await tx
					.select()
					.from(usersWorkInfoTable)
					.where(eq(usersWorkInfoTable.userId, userId))
					.limit(1);

				if (existingWorkInfo.length > 0) {
					// Update existing users_work_info record
					await tx
						.update(usersWorkInfoTable)
						.set({
							companyId,
							plantId,
							cardNumber: cardNumber || existingWorkInfo[0].cardNumber
						})
						.where(eq(usersWorkInfoTable.userId, userId));
				} else {
					// Create new users_work_info record
					if (companyId && plantId) {
						await tx.insert(usersWorkInfoTable).values({
							userId,
							companyId,
							plantId,
							cardNumber: cardNumber || ''
						});
					} else {
						throw new Error('companyId and plantId are required to insert into usersWorkInfoTable');
					}
				}
			}

			return { success: true };
		});
	} catch (error) {
		console.error('Error updating user assignment:', error);
		throw new Error('Failed to update user assignment');
	}
}

export async function deleteUser(userId: string): Promise<{ success: boolean }> {
	try {
		await db.transaction(async (tx) => {
			await tx
				.update(usersTable)
				.set({ deleted: true, deletedAt: new Date() })
				.where(eq(usersTable.id, userId));

			await tx
				.update(usersWorkInfoTable)
				.set({ deleted: true, deletedAt: new Date() })
				.where(eq(usersWorkInfoTable.userId, userId));
			await tx
				.update(userPlantsTable)
				.set({ deletedUser: true, deletedAt: new Date() })
				.where(eq(userPlantsTable.userId, userId));
		});

		return { success: true };
	} catch (error) {
		console.error('Error deleting user:', error);
		throw new Error('Failed to delete user');
	}
}
