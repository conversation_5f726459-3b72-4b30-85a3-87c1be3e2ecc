import type { TagDTO } from '$lib/DTO/tags';
import { db } from '$lib/db/db.server';
import { tagsTable, questionTagsTable } from '$lib/db/schema/audits';
import { eq, and } from 'drizzle-orm';

export async function getTags(plantId: string): Promise<TagDTO[]> {
	try {
		const conditions = [];

		if (plantId) {
			conditions.push(eq(tagsTable.plantId, plantId));
		}

		const result = await db
			.select()
			.from(tagsTable)
			.where(conditions.length > 0 ? and(...conditions) : undefined)
			.orderBy(tagsTable.name);

		return result;
	} catch (error) {
		console.error('Error fetching tags:', error);
		throw new Error('Failed to fetch tags');
	}
}

export async function createTag(data: Omit<TagDTO, 'id'>, plantId: string): Promise<TagDTO | null> {
	try {
		// Capitalize the first letter of the tag name
		const capitalizedName = data.name.charAt(0).toUpperCase() + data.name.slice(1);

		const tagData = {
			...data,
			plantId
		};

		const [result] = await db
			.insert(tagsTable)
			.values({
				...tagData,
				name: capitalizedName
			})
			.returning();

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error('Error creating tag:', error);
		throw new Error('Failed to create tag');
	}
}

export async function getTagById(tagId: string): Promise<TagDTO | null> {
	try {
		const result = await db.select().from(tagsTable).where(eq(tagsTable.id, tagId)).limit(1);

		return result[0] || null;
	} catch (error) {
		console.error('Error fetching tag by ID:', error);
		throw new Error('Failed to fetch tag');
	}
}

export async function updateTag(plantId: string, data: TagDTO): Promise<TagDTO | null> {
	try {
		// Capitalize the first letter of the tag name if name is being updated
		const updatedData = data.name
			? { ...data, name: data.name.charAt(0).toUpperCase() + data.name.slice(1) }
			: data;

		if (!data.id) {
			throw new Error('Tag ID is required for update');
		}
		const [result] = await db
			.update(tagsTable)
			.set(updatedData)
			.where(and(eq(tagsTable.id, data.id as string), eq(tagsTable.plantId, plantId)))
			.returning();

		if (!result) {
			return null;
		}

		return result;
	} catch (error) {
		console.error('Error updating tag:', error);
		throw new Error('Failed to update tag');
	}
}

export async function deleteTag(id: string, plantId: string): Promise<boolean> {
	try {
		// First delete all question-tag relationships
		await db.delete(questionTagsTable).where(eq(questionTagsTable.tagId, id));

		// Then delete the tag itself
		const result = await db
			.delete(tagsTable)
			.where(and(eq(tagsTable.id, id), eq(tagsTable.plantId, plantId)));

		return result.length > 0;
	} catch (error) {
		console.error('Error deleting tag:', error);
		throw new Error('Failed to delete tag');
	}
}

export async function getTagsForQuestion(questionId: string, plantId?: string): Promise<TagDTO[]> {
	try {
		const conditions = [eq(questionTagsTable.questionId, questionId)];

		if (plantId) {
			conditions.push(eq(tagsTable.plantId, plantId));
		}

		const result = await db
			.select({
				id: tagsTable.id,
				name: tagsTable.name,
				color: tagsTable.color,
				textColor: tagsTable.textColor
			})
			.from(tagsTable)
			.innerJoin(questionTagsTable, eq(tagsTable.id, questionTagsTable.tagId))
			.where(and(...conditions))
			.orderBy(tagsTable.name);

		return result;
	} catch (error) {
		console.error('Error fetching tags for question:', error);
		throw new Error('Failed to fetch tags for question');
	}
}

export async function assignTagsToQuestion(questionId: string, tagIds: string[]): Promise<void> {
	try {
		// First remove existing tags for this question
		await db.delete(questionTagsTable).where(eq(questionTagsTable.questionId, questionId));

		// Then add new tags if any
		if (tagIds.length > 0) {
			const values = tagIds.map((tagId) => ({
				questionId,
				tagId
			}));

			await db.insert(questionTagsTable).values(values);
		}
	} catch (error) {
		console.error('Error assigning tags to question:', error);
		throw new Error('Failed to assign tags to question');
	}
}

export async function removeTagFromQuestion(questionId: string, tagId: string): Promise<boolean> {
	try {
		const result = await db
			.delete(questionTagsTable)
			.where(eq(questionTagsTable.questionId, questionId) && eq(questionTagsTable.tagId, tagId));

		return result.length > 0;
	} catch (error) {
		console.error('Error removing tag from question:', error);
		throw new Error('Failed to remove tag from question');
	}
}

export async function getQuestionsByTag(tagId: string): Promise<string[]> {
	try {
		const result = await db
			.select({ questionId: questionTagsTable.questionId })
			.from(questionTagsTable)
			.where(eq(questionTagsTable.tagId, tagId));

		return result.map((row) => row.questionId);
	} catch (error) {
		console.error('Error fetching questions by tag:', error);
		throw new Error('Failed to fetch questions by tag');
	}
}
