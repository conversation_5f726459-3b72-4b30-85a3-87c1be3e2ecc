import { db } from '$lib/db/db.server';
import { companyTable, plantsTable } from '$lib/db/schema/company';
import { usersWorkInfoTable } from '$lib/db/schema/user';
import { Result } from '@badrap/result';
import { eq, and } from 'drizzle-orm';

export async function getAllCompanies() {
	try {
		const companies = await db.select().from(companyTable);
		return companies ?? [];
	} catch (error) {
		console.error('Error getting companies:', error);
		return [];
	}
}

export async function getCompanyById(id: string) {
	try {
		const [company] = await db
			.select()
			.from(companyTable)
			.where(and(eq(companyTable.id, id), eq(companyTable.deleted, false)));
		return company ?? null;
	} catch (error) {
		console.error('Error getting company:', error);
		throw new Error('Failed to get company');
	}
}

export async function getCompanyIdBySlug(slug: string) {
	try {
		const [company] = await db
			.select({ id: companyTable.id })
			.from(companyTable)
			.where(eq(companyTable.slug, slug));
		return company?.id ?? null;
	} catch (error) {
		console.error('Error getting company ID by slug:', error);
		throw new Error('Failed to get company ID by slug');
	}
}

export async function getCompanyBySlug(slug: string) {
	try {
		// Search for company using slug column
		const [company] = await db
			.select()
			.from(companyTable)
			.where(and(eq(companyTable.slug, slug), eq(companyTable.deleted, false)));
		return company ?? null;
	} catch (error) {
		console.error('Error getting company by slug:', error);
		return null;
	}
}

export async function getUserCompanySlug(userId: string) {
	try {
		const [company] = await db
			.select({ slug: companyTable.slug })
			.from(companyTable)
			.innerJoin(usersWorkInfoTable, eq(usersWorkInfoTable.companyId, companyTable.id))
			.where(eq(usersWorkInfoTable.userId, userId));
		return company?.slug ?? null;
	} catch (error) {
		console.error('Error getting user company slug:', error);
		return null;
	}
}

export async function getPlantsByCompanyId(companyId: string) {
	try {
		const plants = await db.select().from(plantsTable).where(eq(plantsTable.companyId, companyId));
		return plants ?? null;
	} catch (error) {
		console.error('Error getting plants:', error);
		return Result.err();
	}
}

export async function getPlantsWInfoByCompanyId(companyId: string) {
	try {
		const result = await db
			.select()
			.from(plantsTable)
			.leftJoin(companyTable, eq(plantsTable.companyId, companyTable.id))
			.where(eq(plantsTable.companyId, companyId));
		return result ?? null;
	} catch (error) {
		console.error('Error getting plants:', error);
		return Result.err();
	}
}
