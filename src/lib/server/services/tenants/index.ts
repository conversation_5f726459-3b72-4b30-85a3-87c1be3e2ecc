import { getAllCompanies, getCompanyById, getCompanyBySlug, getUserCompanySlug } from './company';
import {
	getPlantById,
	getPlantBySlug,
	getPlantBySlugAndCompany,
	getPlantIdBySlug,
	getPlants,
	getUserMainPlantSlug,
	getCompanyIdByPlantId,
	getPlantGeneralSettings,
	updatePlantGeneralSettings
} from './plant';
import { getPlantEvaluationConfig } from './plantEvaluationConfig';
import { getPlantDefaultLanguage, getPlantSupportedLanguages } from './plantLanguageService';

export const CompanyService = {
	getAllCompanies,
	getCompanyById,
	getCompanyBySlug,
	getUserCompanySlug
};

export const PlantsService = {
	getPlants,
	getPlantBySlug,
	getPlantBySlugAndCompany,
	getPlantIdBySlug,
	getUserMainPlantSlug,
	getPlantById,
	getCompanyIdByPlantId
};

export const PlantAdminService = {
	getPlantGeneralSettings,
	updatePlantGeneralSettings
};

export const PlantLanguageService = {
	getPlantDefaultLanguage,
	getPlantSupportedLanguages
};

export const PlantEvaluationConfigService = {
	getPlantEvaluationConfig
};
