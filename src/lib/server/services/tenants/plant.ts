import { db } from '$lib/db/db.server';
import { plantsConfigurationTable, plantsTable } from '$lib/db/schema/company';
import { usersWorkInfoTable } from '$lib/db/schema/user';
import { eq, and } from 'drizzle-orm';

export async function getPlants() {
	try {
		const plants = await db.select().from(plantsTable);
		return plants;
	} catch (error) {
		console.error('Error getting plants:', error);
		throw new Error('Failed to get plants');
	}
}

export async function getPlantBySlug(slug: string) {
	try {
		const [plant] = await db
			.select()
			.from(plantsTable)
			.where(and(eq(plantsTable.slug, slug), eq(plantsTable.deleted, false)));
		return plant ?? null;
	} catch (error) {
		console.error('Error getting plant by slug:', error);
		throw new Error('Failed to get plant by slug');
	}
}

export async function getPlantBySlugAndCompany(slug: string, companyId: string) {
	try {
		const [plant] = await db
			.select()
			.from(plantsTable)
			.where(
				and(
					eq(plantsTable.slug, slug),
					eq(plantsTable.companyId, companyId),
					eq(plantsTable.deleted, false)
				)
			);
		return plant ?? null;
	} catch (error) {
		console.error('Error getting plant by slug and company:', error);
		throw new Error('Failed to get plant by slug and company');
	}
}

export async function getPlantIdBySlug(slug: string) {
	try {
		const [plant] = await db
			.select({ id: plantsTable.id })
			.from(plantsTable)
			.where(eq(plantsTable.slug, slug));
		return plant?.id ?? null;
	} catch (error) {
		console.error('Error getting plant ID by slug:', error);
		throw new Error('Failed to get plant ID by slug');
	}
}

export async function getPlantById(id: string) {
	try {
		const [plant] = await db
			.select()
			.from(plantsTable)
			.where(and(eq(plantsTable.id, id), eq(plantsTable.deleted, false)));
		return plant;
	} catch (error) {
		console.error('Error getting plant:', error);
		throw new Error('Failed to get plant');
	}
}

export async function getUserMainPlantSlug(userId: string): Promise<string | null> {
	try {
		const [workInfo] = await db
			.select({ plantId: usersWorkInfoTable.plantId })
			.from(usersWorkInfoTable)
			.where(eq(usersWorkInfoTable.userId, userId))
			.limit(1);

		if (workInfo) {
			// Get the slug for the primary plant
			const [plant] = await db
				.select({ slug: plantsTable.slug })
				.from(plantsTable)
				.where(eq(plantsTable.id, workInfo.plantId));

			if (plant) {
				return plant.slug;
			}
		}
		return null;
	} catch (error) {
		console.error('Error getting user main plant slug:', error);
		return null;
	}
}

export async function getCompanyIdByPlantId(plantId: string): Promise<string | null> {
	try {
		const [plant] = await db
			.select({ companyId: plantsTable.companyId })
			.from(plantsTable)
			.where(eq(plantsTable.id, plantId));
		return plant?.companyId ?? null;
	} catch (error) {
		console.error('Error getting company ID by plant ID:', error);
		throw new Error('Failed to get company ID by plant ID');
	}
}

export async function getPlantGeneralSettings(plantId: string) {
	try {
		const [settings] = await db
			.select({
				eKaizenFormURL: plantsTable.eKaizenFormURL,
				tasksEnabled: plantsTable.tasksEnabled,
				supportedLanguages: plantsConfigurationTable.supportedLanguages,
				defaultLanguage: plantsConfigurationTable.defaultLanguage
			})
			.from(plantsTable)
			.leftJoin(plantsConfigurationTable, eq(plantsTable.id, plantsConfigurationTable.plantId))
			.where(eq(plantsTable.id, plantId));

		// Clean duplicates
		if (settings && settings.supportedLanguages) {
			settings.supportedLanguages = Array.from(new Set(settings.supportedLanguages));
		}

		return settings;
	} catch (error) {
		console.error('Error getting plant general settings for plant ID:', plantId, '\nError:', error);
		throw new Error('Failed to get plant general settings');
	}
}

export async function updatePlantGeneralSettings(
	plantId: string,
	data: {
		eKaizenFormURL?: string;
		tasksEnabled?: boolean;
		supportedLanguages?: string[];
		defaultLanguage?: string;
	}
) {
	try {
		if (data.eKaizenFormURL !== undefined || data.tasksEnabled !== undefined) {
			await db
				.update(plantsTable)
				.set({
					...(data.eKaizenFormURL !== undefined && { eKaizenFormURL: data.eKaizenFormURL }),
					...(data.tasksEnabled !== undefined && { tasksEnabled: data.tasksEnabled })
				})
				.where(eq(plantsTable.id, plantId));
		}

		if (data.supportedLanguages !== undefined || data.defaultLanguage !== undefined) {
			// Remove duplicates from supportedLanguages before saving
			const cleanSupportedLanguages = data.supportedLanguages
				? Array.from(new Set(data.supportedLanguages))
				: undefined;

			await db
				.update(plantsConfigurationTable)
				.set({
					...(cleanSupportedLanguages !== undefined && {
						supportedLanguages: cleanSupportedLanguages
					}),
					...(data.defaultLanguage !== undefined && { defaultLanguage: data.defaultLanguage })
				})
				.where(eq(plantsConfigurationTable.plantId, plantId));
		}

		return true;
	} catch (error) {
		console.error('Error updating plant general settings:', error);
		throw new Error('Failed to update plant general settings');
	}
}
