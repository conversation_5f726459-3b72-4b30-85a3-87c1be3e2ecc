import { db } from '$lib/db/db.server';
import { plantsEvaluationConfigurationTable } from '$lib/db/schema/company';
import { eq } from 'drizzle-orm';
import type { PlantEvaluationConfig } from '$lib/db/schema/company';

/**
 * Get plant evaluation configuration by plant ID
 * Returns null if no configuration exists
 */
export async function getPlantEvaluationConfig(
	plantId: string
): Promise<PlantEvaluationConfig | null> {
	try {
		const [config] = await db
			.select()
			.from(plantsEvaluationConfigurationTable)
			.where(eq(plantsEvaluationConfigurationTable.plantId, plantId));

		return config ?? null;
	} catch (error) {
		console.error('Error getting plant evaluation config:', error);
		throw new Error('Failed to get plant evaluation configuration');
	}
}
