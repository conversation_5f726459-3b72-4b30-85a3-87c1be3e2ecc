import { db } from '$lib/db/db.server';
import { plantsConfigurationTable } from '$lib/db/schema/company';
import { eq } from 'drizzle-orm';

/**
 * Get the default language for a specific plant
 * @param plantId - The ID of the plant
 * @returns The default language code (e.g., 'cs', 'en') or 'en' as fallback
 */
export async function getPlantDefaultLanguage(plantId: string): Promise<string> {
	try {
		const [config] = await db
			.select({
				defaultLanguage: plantsConfigurationTable.defaultLanguage
			})
			.from(plantsConfigurationTable)
			.where(eq(plantsConfigurationTable.plantId, plantId))
			.limit(1);

		return config?.defaultLanguage || 'en';
	} catch (error) {
		console.error('Error getting plant default language for plant ID:', plantId, '\nError:', error);
		return 'en'; // Safe fallback
	}
}

/**
 * Get the supported languages for a specific plant
 * @param plantId - The ID of the plant
 * @returns An array of supported language codes or ['en'] as fallback
 */
export async function getPlantSupportedLanguages(plantId: string): Promise<string[]> {
	try {
		const [config] = await db
			.select({
				supportedLanguages: plantsConfigurationTable.supportedLanguages
			})
			.from(plantsConfigurationTable)
			.where(eq(plantsConfigurationTable.plantId, plantId))
			.limit(1);

		return config?.supportedLanguages || ['en'];
	} catch (error) {
		console.error(
			'Error getting plant supported languages for plant ID:',
			plantId,
			'\nError:',
			error
		);
		return ['en'];
	}
}
