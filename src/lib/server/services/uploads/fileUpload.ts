import { UPLOAD_DIR } from '$lib/constants/file';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export async function saveFile(file: File): Promise<string> {
	try {
		const fileExtension = path.extname(file.name);
		const fileName = `${uuidv4()}${fileExtension}`;
		const filePath = path.join(UPLOAD_DIR, fileName);

		const arrayBuffer = await file.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);

		fs.writeFileSync(filePath, buffer);

		const finalPath = `/uploads/${fileName}`; //TODO: Change for hosting
		return finalPath;
	} catch (error) {
		console.error('Error in saveFile:', error);
		throw new Error('Failed to save file');
	}
}

export function deleteFile(url: string): void {
	try {
		const fileName = path.basename(url);
		const filePath = path.join(UPLOAD_DIR, fileName);

		if (fs.existsSync(filePath)) {
			fs.unlinkSync(filePath);
		}
	} catch (error) {
		console.error('Error in deleteFile:', error);
		throw new Error('Failed to delete file');
	}
}
