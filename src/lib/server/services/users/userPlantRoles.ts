import { db } from '$lib/db/db.server';
import { userRolePlantsTable, plantRolesTable } from '$lib/db/schema/roles';
import { usersWorkInfoTable } from '$lib/db/schema/user';
import { eq, and } from 'drizzle-orm';

export type UserPlantRoleAssignment = {
	userId: string;
	plantId: string;
	roleId: string;
};

export type MultiPlantUserData = {
	userId: string;
	mainPlantId: string;
	additionalPlantRoles: UserPlantRoleAssignment[];
};

/**
 * Assign user to multiple plants with roles
 */
export async function assignUserToMultiplePlants(data: MultiPlantUserData): Promise<void> {
	try {
		await db.transaction(async (tx) => {
			
			await tx
				.update(usersWorkInfoTable)
				.set({ plantId: data.mainPlantId })
				.where(eq(usersWorkInfoTable.userId, data.userId));

			// Remove existing plant role assignments
			await tx
				.delete(userRolePlantsTable)
				.where(eq(userRolePlantsTable.userId, data.userId));

			// Add new plant role assignments
			if (data.additionalPlantRoles.length > 0) {
				await tx
					.insert(userRolePlantsTable)
					.values(data.additionalPlantRoles.map(assignment => ({
						userId: assignment.userId,
						roleId: assignment.roleId,
						plantId: assignment.plantId
					})));
			}
		});

		console.log(`✅ Updated plant assignments for user ${data.userId}`);
	} catch (error) {
		console.error('Error assigning user to plants:', error);
		throw new Error('Failed to assign user to plants');
	}
}

/**
 * Get user's current plant role assignments
 */
export async function getUserPlantRoles(userId: string) {
	try {
		return await db
			.select({
				id: userRolePlantsTable.id,
				userId: userRolePlantsTable.userId,
				roleId: userRolePlantsTable.roleId,
				plantId: userRolePlantsTable.plantId,
				roleName: plantRolesTable.name,
				rolePermissions: plantRolesTable.permissions
			})
			.from(userRolePlantsTable)
			.innerJoin(plantRolesTable, eq(userRolePlantsTable.roleId, plantRolesTable.id))
			.where(eq(userRolePlantsTable.userId, userId));
	} catch (error) {
		console.error('Error fetching user plant roles:', error);
		throw new Error('Failed to fetch user plant roles');
	}
}

/**
 * Add single plant role assignment
 */
export async function addUserPlantRole(assignment: UserPlantRoleAssignment): Promise<void> {
	try {
		// Check if assignment already exists
		const existing = await db
			.select()
			.from(userRolePlantsTable)
			.where(
				and(
					eq(userRolePlantsTable.userId, assignment.userId),
					eq(userRolePlantsTable.plantId, assignment.plantId),
					eq(userRolePlantsTable.roleId, assignment.roleId)
				)
			)
			.limit(1);

		if (existing.length > 0) {
			console.log('User already has this role in this plant');
			return;
		}

		await db.insert(userRolePlantsTable).values({
			userId: assignment.userId,
			roleId: assignment.roleId,
			plantId: assignment.plantId
		});

		console.log(`✅ Added plant role assignment for user ${assignment.userId}`);
	} catch (error) {
		console.error('Error adding user plant role:', error);
		throw new Error('Failed to add user plant role');
	}
}

/**
 * Remove user from plant
 */
export async function removeUserFromPlant(userId: string, plantId: string): Promise<void> {
	try {
		await db
			.delete(userRolePlantsTable)
			.where(
				and(
					eq(userRolePlantsTable.userId, userId),
					eq(userRolePlantsTable.plantId, plantId)
				)
			);

		console.log(`✅ Removed user ${userId} from plant ${plantId}`);
	} catch (error) {
		console.error('Error removing user from plant:', error);
		throw new Error('Failed to remove user from plant');
	}
}

/**
 * Get available roles for a plant
 */
export async function getAvailablePlantRoles(plantId: string) {
	try {
		return await db
			.select()
			.from(plantRolesTable)
			.where(eq(plantRolesTable.plantId, plantId));
	} catch (error) {
		console.error('Error fetching available plant roles:', error);
		throw new Error('Failed to fetch available plant roles');
	}
}
