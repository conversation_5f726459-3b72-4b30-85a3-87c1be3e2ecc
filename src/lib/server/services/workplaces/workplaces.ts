import type { AuditDTO } from '$lib/DTO/audits/audits';
import type { WorkplaceDTO } from '$lib/DTO/workplaces/workplaces';
import type { WorkplaceForm, WorkplaceInfoForm } from '$lib/schemas/workplace';
import { db } from '$lib/db/db.server';
import { auditInstancesTable, auditTypesTable } from '$lib/db/schema/audits';
import { usersTable } from '$lib/db/schema/user';
import { workplaceTable } from '$lib/db/schema/workplace';
import { and, eq, like, not } from 'drizzle-orm';
import { calculateAuditProgress, getAuditAnswers } from '../audits/auditEvaluate';

export async function getWorkplaces(
	plantId: string,
	getOnlyActive: boolean = false
): Promise<WorkplaceDTO[]> {
	try {
		const conditions = [];

		// Filter out soft-deleted workplaces
		conditions.push(not(like(workplaceTable.code, 'DELETED_%')));

		if (getOnlyActive) {
			conditions.push(eq(workplaceTable.active, true));
		}

		if (plantId) {
			conditions.push(eq(workplaceTable.plantId, plantId));
		}

		const query = db
			.select({
				id: workplaceTable.id,
				name: workplaceTable.name,
				eKaizenWorkstationId: workplaceTable.eKaizenWorkstationId,
				code: workplaceTable.code,
				active: workplaceTable.active,
				responsiblePerson: {
					id: usersTable.id,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				}
			})
			.from(workplaceTable)
			.leftJoin(usersTable, eq(workplaceTable.responsiblePersonId, usersTable.id))
			.where(conditions.length > 0 ? and(...conditions) : undefined);

		const result = await query;

		if (!result.length) {
			return [];
		}

		return result.map((workplace) => ({
			...workplace,
			responsiblePerson: workplace.responsiblePerson!.id
				? {
						id: workplace.responsiblePerson!.id,
						firstName: workplace.responsiblePerson!.firstName ?? '',
						lastName: workplace.responsiblePerson!.lastName ?? ''
					}
				: null
		}));
	} catch (error) {
		console.error('Error fetching workplaces:', error);
		throw new Error('Failed to fetch workplaces');
	}
}

export async function getWorkplaceById(workplaceId: string) {
	try {
		const [result] = await db
			.select()
			.from(workplaceTable)
			.where(and(eq(workplaceTable.id, workplaceId), not(like(workplaceTable.code, 'DELETED_%'))));
		return result ?? null;
	} catch (error) {
		console.error('Error fetching workplace:', error);
		throw new Error('Failed to fetch workplace');
	}
}

export async function getWorkplaceDetails(workplaceId: string): Promise<WorkplaceDTO | null> {
	try {
		const [result] = await db
			.select({
				id: workplaceTable.id,
				name: workplaceTable.name,
				eKaizenWorkstationId: workplaceTable.eKaizenWorkstationId,
				code: workplaceTable.code,
				active: workplaceTable.active,
				description: workplaceTable.description,
				responsiblePerson: {
					id: workplaceTable.responsiblePersonId,
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				}
			})
			.from(workplaceTable)
			.leftJoin(usersTable, eq(workplaceTable.responsiblePersonId, usersTable.id))
			.where(and(eq(workplaceTable.id, workplaceId), not(like(workplaceTable.code, 'DELETED_%'))));

		if (!result) {
			return null;
		}

		return {
			...result,
			responsiblePerson: result.responsiblePerson.id
				? {
						id: result.responsiblePerson.id,
						firstName: result.responsiblePerson.firstName ?? '',
						lastName: result.responsiblePerson.lastName ?? ''
					}
				: null,
			description: result.description ?? undefined
		};
	} catch (error) {
		console.error('Error fetching workplace details:', error);
		throw new Error('Failed to fetch workplace details');
	}
}

export async function getAuditsForWorkplace(workplaceId: string): Promise<AuditDTO[]> {
	try {
		const audits = await db
			.select({
				id: auditInstancesTable.id,
				code: auditInstancesTable.code,
				auditTypeId: auditInstancesTable.auditTypeId,
				workplaceId: auditInstancesTable.workplaceId,
				responsiblePersonId: auditInstancesTable.responsiblePersonId,
				plannedEvaluationDate: auditInstancesTable.plannedDate,
				completionDate: auditInstancesTable.completionDate,
				realDuration: auditInstancesTable.realDuration,
				questions: auditInstancesTable.questions,
				createdAt: auditInstancesTable.createdAt,
				createdBy: auditInstancesTable.createdBy,
				successRate: auditInstancesTable.successRate,
				rulesResult: auditInstancesTable.rulesResult,
				evaluationConfigId: auditInstancesTable.evaluationConfigId
			})
			.from(auditInstancesTable)
			.where(
				and(
					eq(auditInstancesTable.workplaceId, workplaceId),
					not(like(auditInstancesTable.code, 'DELETED_%'))
				)
			);

		if (!audits.length) {
			return [];
		}

		const auditsWithDetails = await Promise.all(
			audits.map(async (audit) => {
				const [auditType] = await db
					.select({
						id: auditTypesTable.id,
						name: auditTypesTable.name,
						expectedDuration: auditTypesTable.expectedDuration
					})
					.from(auditTypesTable)
					.where(eq(auditTypesTable.id, audit.auditTypeId));

				let workplace = null;
				if (audit.workplaceId) {
					const [workplaceResult] = await db
						.select({
							id: workplaceTable.id,
							name: workplaceTable.name,
							code: workplaceTable.code,
							active: workplaceTable.active
						})
						.from(workplaceTable)
						.where(eq(workplaceTable.id, audit.workplaceId));
					workplace = workplaceResult;
				}

				let responsiblePerson = null;
				if (audit.responsiblePersonId) {
					const [personResult] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName
						})
						.from(usersTable)
						.where(eq(usersTable.id, audit.responsiblePersonId));
					responsiblePerson = personResult;
				}

				let createdByUser = null;
				if (audit.createdBy) {
					const [createdByResult] = await db
						.select({
							id: usersTable.id,
							firstName: usersTable.firstName,
							lastName: usersTable.lastName
						})
						.from(usersTable)
						.where(eq(usersTable.id, audit.createdBy));
					createdByUser = createdByResult;
				}

				const answers = await getAuditAnswers(audit.id);
				const progress = calculateAuditProgress(
					audit.questions,
					answers,
					audit.plannedEvaluationDate,
					audit.completionDate
				);

				return {
					id: audit.id,
					code: audit.code,
					auditType: auditType
						? {
								id: auditType.id,
								name: auditType.name
							}
						: null,
					workplace: workplace
						? {
								id: workplace.id,
								name: workplace.name,
								code: workplace.code
							}
						: null,
					responsiblePerson: responsiblePerson
						? {
								id: responsiblePerson.id,
								firstName: responsiblePerson.firstName,
								lastName: responsiblePerson.lastName
							}
						: null,
					createdBy: createdByUser
						? {
								id: createdByUser.id,
								firstName: createdByUser.firstName,
								lastName: createdByUser.lastName
							}
						: null,
					createdAt: audit.createdAt,
					plannedEvaluationDate: audit.plannedEvaluationDate,
					completionDate: audit.completionDate,
					expectedDuration: auditType?.expectedDuration ?? undefined,
					realDuration: audit.realDuration,
					progress,
					successRate: audit.successRate,
					rulesResult: audit.rulesResult,
					evaluationConfigId: audit.evaluationConfigId,
					questions: audit.questions
				};
			})
		);

		return auditsWithDetails;
	} catch (error) {
		console.error('Error fetching audits for workplace:', error);
		throw new Error('Failed to fetch audits for workplace');
	}
}

export async function getResponsiblePeople() {
	try {
		const result = await db
			.select({
				id: usersTable.id,
				name: {
					firstName: usersTable.firstName,
					lastName: usersTable.lastName
				}
			})
			.from(usersTable);

		if (!result.length) {
			return [];
		}

		return result;
	} catch (error) {
		console.error('Error fetching responsible people:', error);
		throw new Error('Failed to fetch responsible people');
	}
}

export async function createWorkplace(data: WorkplaceForm & { plantId?: string | null }) {
	try {
		const [result] = await db
			.insert(workplaceTable)
			.values({
				...data,
				plantId: data.plantId || null
			})
			.returning({ id: workplaceTable.id });
		return result;
	} catch (error) {
		console.error('Error creating workplace:', error);
		throw new Error('Failed to create workplace');
	}
}

export async function editWorkplace(workplaceId: string, data: WorkplaceInfoForm) {
	try {
		const [result] = await db
			.update(workplaceTable)
			.set({
				name: data.name,
				code: data.code,
				eKaizenWorkstationId: data.eKaizenWorkstationId ?? null,
				responsiblePersonId: data.responsiblePersonId,
				active: data.active,
				description: data.description ?? null
			})
			.where(eq(workplaceTable.id, workplaceId))
			.returning();

		return result ?? null;
	} catch (error) {
		console.error('Error updating workplace:', error);
		throw new Error('Failed to update workplace');
	}
}

export async function deleteWorkplace(workplaceId: string) {
	try {
		return await db.transaction(async (tx) => {
			// Get the workplace first to get its current code
			const workplace = await tx
				.select({ code: workplaceTable.code })
				.from(workplaceTable)
				.where(eq(workplaceTable.id, workplaceId))
				.limit(1);

			if (!workplace || workplace.length === 0) {
				throw new Error('Workplace not found');
			}

			// Soft delete by prefixing the code with DELETED_
			const updatedCode = workplace[0].code.startsWith('DELETED_')
				? workplace[0].code
				: `DELETED_${workplace[0].code}`;

			// Update workplace with DELETED_ prefix and set active to false
			const result = await tx
				.update(workplaceTable)
				.set({
					code: updatedCode,
					active: false
				})
				.where(eq(workplaceTable.id, workplaceId))
				.returning();

			return result;
		});
	} catch (error) {
		console.error('Error soft-deleting workplace:', error);
		throw new Error('Failed to delete workplace');
	}
}
