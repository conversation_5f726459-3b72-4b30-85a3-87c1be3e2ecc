import {
	GetObjectCommand,
	PutObjectCommand,
	DeleteObjectCommand,
	S3Client,
	type GetObjectCommandInput,
	type PutObjectCommandInput,
	type DeleteObjectCommandInput
} from '@aws-sdk/client-s3';
import {
	SECRET_AWS_ACCESS_KEY_ID,
	SECRET_AWS_SECRET_ACCESS_KEY,
	SECRET_DATABASE_URL
} from '$env/static/private';
import { SECRET_AWS_REGION, SECRET_AWS_BUCKET } from '$env/static/private';
import { getPlantIdBySlug } from '$lib/server/services/tenants/plant';
import crypto from 'crypto';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const BUCKET_NAME = SECRET_AWS_BUCKET;

// Detect environment from DATABASE_URL
const getEnvironment = (): string => {
	const dbUrl = SECRET_DATABASE_URL;
	if (dbUrl.includes('/demo')) return 'demo';
	if (dbUrl.includes('/dev')) return 'dev';
	if (dbUrl.includes('/prod')) return 'prod';
	return 'dev'; // fallback to dev
};

const ENVIRONMENT_PREFIX = getEnvironment();

// Define S3 folders as constants
export const S3_FOLDERS = {
	AUDITS_ATTACHMENTS: 'attachments',
	LOGOS: 'logos',
	PLANTS: 'plants'
} as const;

const s3 = new S3Client({
	credentials: {
		accessKeyId: SECRET_AWS_ACCESS_KEY_ID,
		secretAccessKey: SECRET_AWS_SECRET_ACCESS_KEY
	},
	region: SECRET_AWS_REGION
});

const getRandomName = () => crypto.randomBytes(32).toString('hex');

/**
 * Generate a unique S3 key for an object with environment prefix
 * @param folder Folder name (e.g., 'attachments', 'logos')
 * @param fileName Name of the file (if not provided, a random one is generated)
 * @param plantId Plant ID for multi-tenant isolation (optional for backward compatibility)
 * @param companyId Company ID for multi-tenant isolation (required for new structure)
 * @returns Key of the S3 object with environment prefix, company ID, and folder structure
 */
const generateS3Key = (
	folder: string,
	fileName?: string,
	plantId?: string,
	companyId?: string
): string => {
	const name = fileName || getRandomName();

	if (companyId) {
		if (folder === S3_FOLDERS.LOGOS) {
			// Logos: environment/companyId/logos/filename
			return `${ENVIRONMENT_PREFIX}/${companyId}/${folder}/${name}`;
		} else if (folder === S3_FOLDERS.AUDITS_ATTACHMENTS && plantId) {
			// Plant attachments: environment/companyId/plants/plantId/attachments/filename
			console.log('DEBUG - generateS3Key params:', {
				folder,
				plantId,
				companyId,
				S3_FOLDERS_PLANTS: S3_FOLDERS.PLANTS,
				ENVIRONMENT_PREFIX
			});

			const result = `${ENVIRONMENT_PREFIX}/${companyId}/${S3_FOLDERS.PLANTS}/${plantId}/${folder}/${name}`;
			console.log('DEBUG - generated key:', result);
			return result;
		}
	}

	// Fallback
	return `${ENVIRONMENT_PREFIX}/${folder}/${name}`;
};

/**
 * Extract S3 key from a presigned URL or directly from the key string
 * @param urlOrKey Presigned URL or S3 key string
 * @returns Key of the S3 object
 */
const extractS3Key = (urlOrKey: string): string => {
	try {
		// if it's a URL, parse it to get the key
		const url = new URL(urlOrKey);
		// s3 url without leading slash
		return url.pathname.substring(1);
	} catch {
		// if it fails, assume it's already a key
		return urlOrKey;
	}
};

/**
 * Get current environment prefix for storage keys
 * @returns Current environment prefix (e.g., 'demo', 'dev')
 */
export const getStorageEnvironmentPrefix = (): string => {
	return ENVIRONMENT_PREFIX;
};

export const uploadToStorage = async (file: File, plantSlug?: string): Promise<string> => {
	// If plantSlug is provided, get the plantId and companyId for new folder structure
	let plantId: string | undefined;
	let companyId: string | undefined;

	if (plantSlug) {
		plantId = await getPlantIdBySlug(plantSlug);
		if (plantId) {
			// Import PlantsService here to avoid circular dependency
			const { PlantsService } = await import('$lib/server/services/tenants');
			const resultCompanyId = await PlantsService.getCompanyIdByPlantId(plantId);
			companyId = resultCompanyId || undefined;
		}
	}

	// Generate S3 key with new structure for audits attachments
	const fileName = generateS3Key(S3_FOLDERS.AUDITS_ATTACHMENTS, undefined, plantId, companyId);
	const buffer = await file.arrayBuffer();

	const params: PutObjectCommandInput = {
		Bucket: BUCKET_NAME,
		Key: fileName,
		Body: Buffer.from(buffer),
		ContentType: file.type
	};

	const command = new PutObjectCommand(params);

	try {
		await s3.send(command);
		return fileName;
	} catch (e) {
		throw new Error(`Failed to upload file: ${e instanceof Error ? e.message : 'Unknown error'}`);
	}
};

export const getFileSignedUrl = async (urlOrKey: string): Promise<string> => {
	// get key from URL or use it directly if it's already a key
	let fileName = extractS3Key(urlOrKey);

	// Ensure the key has environment prefix for proper isolation
	fileName = ensureEnvironmentPrefix(fileName);

	const params: GetObjectCommandInput = {
		Bucket: BUCKET_NAME,
		Key: fileName
	};

	const command = new GetObjectCommand(params);

	try {
		// 4 day expiration length (4 × 24 × 60 × 60 = 345600 secs)
		const signedUrl = await getSignedUrl(s3, command, { expiresIn: 345600 });
		return signedUrl;
	} catch (e) {
		throw new Error(
			`Failed to get signed URL: ${e instanceof Error ? e.message : 'Unknown error'}`
		);
	}
};

export const deleteFile = async (fileName: string): Promise<void> => {
	// Ensure the key has environment prefix for proper isolation
	const keyWithPrefix = ensureEnvironmentPrefix(fileName);

	const params: DeleteObjectCommandInput = {
		Bucket: BUCKET_NAME,
		Key: keyWithPrefix
	};

	const command = new DeleteObjectCommand(params);

	try {
		await s3.send(command);
		console.log('File deleted successfully:', keyWithPrefix);
	} catch (e) {
		throw new Error(`Failed to delete file: ${e instanceof Error ? e.message : 'Unknown error'}`);
	}
};

/**
 * Upload file to specific folder in S3 bucket with automatic environment prefixing
 * @param file File to upload
 * @param folder Folder name (e.g., 'attachments', 'logos', 'documents')
 * @param plantSlug Optional plant slug for multi-tenant isolation
 * @param companySlug Optional company slug for validation
 * @returns S3 key of uploaded file (with environment prefix)
 */
export const uploadToFolder = async (
	file: File,
	folder: string,
	plantSlug?: string,
	companySlug?: string
): Promise<string> => {
	let plantId: string | undefined;
	let companyId: string | undefined;

	// Get company ID from company slug
	if (companySlug) {
		const { CompanyService } = await import('$lib/server/services/tenants');
		const company = await CompanyService.getCompanyBySlug(companySlug);
		if (!company) {
			throw new Error(`Company with slug "${companySlug}" not found`);
		}
		companyId = company.id;
	}

	// Get plant ID and validate it belongs to the company
	if (plantSlug) {
		plantId = await getPlantIdBySlug(plantSlug);
		if (plantId && companyId) {
			// Validate that plant belongs to the company
			const { PlantsService } = await import('$lib/server/services/tenants');
			const plantCompanyId = await PlantsService.getCompanyIdByPlantId(plantId);
			if (plantCompanyId !== companyId) {
				throw new Error(`Plant "${plantSlug}" does not belong to company "${companySlug}"`);
			}
		}
	}

	const fileName = generateS3Key(folder, undefined, plantId, companyId);
	const buffer = await file.arrayBuffer();

	const params: PutObjectCommandInput = {
		Bucket: BUCKET_NAME,
		Key: fileName,
		Body: Buffer.from(buffer),
		ContentType: file.type
	};

	const command = new PutObjectCommand(params);

	try {
		await s3.send(command);
		return fileName;
	} catch (e) {
		throw new Error(`Failed to upload file: ${e instanceof Error ? e.message : 'Unknown error'}`);
	}
};

/**
 * Upload audit attachment to S3 bucket using new structure
 * @param file File to upload
 * @param plantSlug Optional plant slug for multi-tenant isolation
 * @param companySlug Optional company slug for validation
 * @returns S3 key of uploaded file
 */
export const uploadAuditAttachment = async (
	file: File,
	plantSlug?: string,
	companySlug?: string
): Promise<string> => {
	return uploadToFolder(file, S3_FOLDERS.AUDITS_ATTACHMENTS, plantSlug, companySlug);
};

/**
 * Upload logo to S3 bucket using new structure (environment/companyId/logos/)
 * @param file File to upload
 * @param companyId Company ID for multi-tenant isolation
 * @param fileName Optional specific filename (defaults to logo.{extension})
 * @returns S3 key of uploaded file
 */
export const uploadLogo = async (
	file: File,
	companyId: string,
	fileName?: string
): Promise<string> => {
	// Generate filename if not provided
	const fileExtension = file.name.split('.').pop() || 'png';
	const logoFileName = fileName || `logo.${fileExtension}`;

	const key = generateS3Key(S3_FOLDERS.LOGOS, logoFileName, undefined, companyId);
	const buffer = await file.arrayBuffer();

	const params: PutObjectCommandInput = {
		Bucket: BUCKET_NAME,
		Key: key,
		Body: Buffer.from(buffer),
		ContentType: file.type
	};

	const command = new PutObjectCommand(params);

	try {
		await s3.send(command);
		return key;
	} catch (e) {
		throw new Error(`Failed to upload logo: ${e instanceof Error ? e.message : 'Unknown error'}`);
	}
};

/**
 * Ensure S3 key has environment prefix. If not, add it.
 * This helps with migration from old keys without environment prefix.
 * @param key S3 key that might or might not have environment prefix
 * @returns S3 key with environment prefix
 */
export const ensureEnvironmentPrefix = (key: string): string => {
	// If key already starts with environment prefix, return as is
	if (key.startsWith(`${ENVIRONMENT_PREFIX}/`)) {
		return key;
	}

	// If key doesn't have environment prefix, add it
	// This assumes the key is in format "folder/filename"
	return `${ENVIRONMENT_PREFIX}/${key}`;
};

/**
 * Check if S3 key has environment prefix
 * @param key S3 key to check
 * @returns true if key has environment prefix, false otherwise
 */
export const hasEnvironmentPrefix = (key: string): boolean => {
	return key.startsWith(`${ENVIRONMENT_PREFIX}/`);
};

/**
 * Get file buffer from S3 bucket
 * @param key S3 key of the file
 * @returns Buffer of the file or null if not found
 */
export const getFileBuffer = async (key: string): Promise<Buffer | null> => {
	try {
		// Ensure the key has environment prefix for proper isolation
		const keyWithPrefix = ensureEnvironmentPrefix(key);

		const params: GetObjectCommandInput = {
			Bucket: BUCKET_NAME,
			Key: keyWithPrefix
		};

		const command = new GetObjectCommand(params);
		const response = await s3.send(command);
		if (response.Body) {
			// Convert the stream to buffer using AWS SDK utility
			const bytes = await response.Body.transformToByteArray();
			return Buffer.from(bytes);
		}

		return null;
	} catch (e) {
		console.warn('Failed to get file buffer:', e instanceof Error ? e.message : 'Unknown error');
		return null;
	}
};

/**
 * Get logo for plant from S3 bucket
 * @param plantId Plant ID for multi-tenant isolation
 * @returns Buffer of the logo or null if not found
 */
export const getPlantLogo = async (plantId: string): Promise<Buffer | null> => {
	// Try to get logo from new structure first (environment/companyId/logos/)
	try {
		const { PlantsService } = await import('$lib/server/services/tenants');
		const companyId = await PlantsService.getCompanyIdByPlantId(plantId);

		if (companyId) {
			// Try new structure: environment/companyId/logos/logo.{ext}
			const logoExtensions = ['png', 'jpg', 'jpeg'];

			for (const ext of logoExtensions) {
				const logoKey = `${companyId}/${S3_FOLDERS.LOGOS}/logo.${ext}`;
				const logoBuffer = await getFileBuffer(logoKey);

				if (logoBuffer) {
					return logoBuffer;
				}
			}
		}
	} catch (error) {
		console.warn('Error trying new logo structure:', error);
	}

	// Fallback to old structure: environment/plantId/logos/logo.{ext}
	const logoExtensions = ['png', 'jpg', 'jpeg'];

	for (const ext of logoExtensions) {
		const logoKey = `${plantId}/${S3_FOLDERS.LOGOS}/logo.${ext}`;
		const logoBuffer = await getFileBuffer(logoKey);

		if (logoBuffer) {
			return logoBuffer;
		}
	}

	return null;
};

/**
 * Get logo for company from S3 bucket using new structure
 * @param companyId Company ID for multi-tenant isolation
 * @returns Buffer of the logo or null if not found
 */
export const getCompanyLogo = async (companyId: string): Promise<Buffer | null> => {
	// Try logo.png first, then logo.jpg, then logo.jpeg
	const logoExtensions = ['png', 'jpg', 'jpeg'];

	for (const ext of logoExtensions) {
		const logoKey = `${companyId}/${S3_FOLDERS.LOGOS}/logo.${ext}`;
		const logoBuffer = await getFileBuffer(logoKey);

		if (logoBuffer) {
			return logoBuffer;
		}
	}

	return null;
};
