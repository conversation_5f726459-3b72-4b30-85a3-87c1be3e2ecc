import type { TDocumentDefinitions, StyleDictionary } from 'pdfmake/interfaces';
import { t } from '$lib/translations';
import { getProgressInfo } from '$lib/utils/progress';

const styles: StyleDictionary = {
	header: {
		fontSize: 28,
		bold: true,
		color: '#1a365d',
		margin: [0, 0, 0, 20],
		alignment: 'center'
	},
	subheader: {
		fontSize: 18,
		bold: true,
		color: '#2d3748',
		margin: [0, 25, 0, 15],
		decoration: 'underline',
		decorationStyle: 'solid',
		decorationColor: '#4a5568'
	},
	infoLabel: {
		bold: true,
		color: '#4a5568',
		fontSize: 11,
		margin: [0, 0, 0, 0]
	},
	infoValue: {
		color: '#2d3748',
		fontSize: 10,
		margin: [0, 0, 0, 0]
	},
	statusDefault: { color: '#3182ce', fontSize: 10, bold: true },
	statusFilled: { color: '#4299e1', fontSize: 10, bold: true },
	statusWarning: { color: '#d69e2e', fontSize: 10, bold: true },
	statusSuccess: { color: '#38a169', fontSize: 10, bold: true },
	statusDestructive: { color: '#e53e3e', fontSize: 10, bold: true },
	footer: {
		fontSize: 10,
		color: '#718096',
		alignment: 'center',
		margin: [0, 30, 0, 0]
	},
	tableHeader: {
		fillColor: '#2d3748',
		color: '#ffffff',
		bold: true,
		fontSize: 12,
		margin: [0, 0, 0, 0]
	},
	questionText: {
		fontSize: 11,
		color: '#2d3748',
		margin: [0, 4, 0, 4]
	},
	answerText: {
		fontSize: 11,
		bold: true,
		margin: [0, 4, 0, 4],
		alignment: 'center'
	},
	answerYes: {
		fontSize: 11,
		bold: true,
		color: '#38a169',
		margin: [0, 4, 0, 4],
		alignment: 'center'
	},
	answerNo: {
		fontSize: 11,
		bold: true,
		color: '#e53e3e',
		margin: [0, 4, 0, 4],
		alignment: 'center'
	},
	answerNeutral: {
		fontSize: 11,
		bold: true,
		color: '#718096',
		margin: [0, 4, 0, 4],
		alignment: 'center'
	},
	noteText: {
		fontSize: 11,
		color: '#4a5568',
		italics: true,
		margin: [0, 4, 0, 4]
	},
	divider: {
		margin: [0, 20, 0, 20]
	}
};

// Function to get PNG logo as base64 from S3 bucket
async function getLogoBase64(companyId?: string): Promise<string | null> {
	try {
		if (!companyId) {
			console.warn('No companyId provided for logo, continuing without logo');
			return null;
		}

		// Import storage functions
		const { getCompanyLogo } = await import('$lib/server/storage');

		const logoBuffer = await getCompanyLogo(companyId);

		if (logoBuffer) {
			// Detect image type from buffer
			const isPng = logoBuffer
				.subarray(0, 8)
				.equals(Buffer.from([0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]));
			const isJpeg = logoBuffer.subarray(0, 3).equals(Buffer.from([0xff, 0xd8, 0xff]));

			let mimeType = 'image/png'; // default
			if (isJpeg) mimeType = 'image/jpeg';
			else if (isPng) mimeType = 'image/png';

			return `data:${mimeType};base64,${logoBuffer.toString('base64')}`;
		}

		console.warn('Logo not found in S3 bucket for companyId:', companyId);
		return null;
	} catch (error) {
		console.warn('Error loading logo from S3:', error);
		return null;
	}
}

export async function createAuditPdfDefinition(
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	auditInfo: any,
	flatQuestions: {
		id: string;
		text: string;
		subtext: string;
		answer: { value: string; note: string } | null;
	}[],
	userLanguage: string,
	plantId?: string,
	successRate?: number,
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	companyInfo?: any
): Promise<TDocumentDefinitions> {
	const progressInfo = getProgressInfo(auditInfo.progress, auditInfo.completionDate, successRate);
	const logoBase64 = await getLogoBase64(companyInfo?.id);

	return {
		content: [
			...(logoBase64
				? [
						{
							image: logoBase64,
							width: 120,
							height: 60,
							fit: [120, 60] as [number, number],
							alignment: 'right' as const,
							margin: [0, 0, 0, 20] as [number, number, number, number]
						}
					]
				: []),

			// Header
			{
				text: `${t.get('emails.resultsPdf.title')}: ${auditInfo.code}`,
				style: 'header'
			},
			{
				text: `${t.get('emails.resultsPdf.auditInfo.generated')}: ${new Date().toLocaleDateString(userLanguage)}`,
				alignment: 'center',
				margin: [0, 0, 0, 30],
				color: '#718096',
				fontSize: 12
			},

			// Audit info
			{
				text: t.get('emails.resultsPdf.auditInfo.title'),
				style: 'subheader'
			},
			{
				table: {
					widths: [75, 125, 90, '*'],
					body: [
						[
							{
								text: `${t.get('emails.resultsPdf.auditInfo.code')}:`,
								style: 'infoLabel',
								noWrap: true
							},
							{ text: auditInfo.code || '-', style: 'infoValue' },
							{
								text: `${t.get('emails.resultsPdf.auditInfo.plannedDate')}:`,
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: auditInfo.plannedDate
									? new Date(auditInfo.plannedDate).toLocaleDateString(userLanguage)
									: '-',
								style: 'infoValue'
							}
						],
						[
							{
								text: `${t.get('emails.resultsPdf.auditInfo.status')}:`,
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: progressInfo.text,
								style: `status${progressInfo.variant.charAt(0).toUpperCase() + progressInfo.variant.slice(1)}`
							},
							{
								text: auditInfo.completionDate
									? `${t.get('emails.resultsPdf.auditInfo.completionDate')}:`
									: '',
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: auditInfo.completionDate
									? new Date(auditInfo.completionDate).toLocaleDateString(userLanguage)
									: '',
								style: 'infoValue'
							}
						],
						[
							{
								text: `${t.get('emails.resultsPdf.auditInfo.auditor')}:`,
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: auditInfo.auditor
									? `${auditInfo.auditor.firstName} ${auditInfo.auditor.lastName}`
									: 'N/A',
								style: 'infoValue'
							},
							{
								text: auditInfo.workplace
									? `${t.get('emails.resultsPdf.auditInfo.workplace')}:`
									: '',
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: auditInfo.workplace ? auditInfo.workplace.name || '-' : '',
								style: 'infoValue'
							}
						],
						[
							{
								text: auditInfo.auditType
									? `${t.get('emails.resultsPdf.auditInfo.auditType')}:`
									: '',
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: auditInfo.auditType ? auditInfo.auditType.name || '-' : '',
								style: 'infoValue'
							},
							{
								text:
									typeof successRate === 'number'
										? `${t.get('emails.resultsPdf.auditInfo.successRate')}:`
										: '',
								style: 'infoLabel',
								noWrap: true
							},
							{
								text: typeof successRate === 'number' ? `${successRate}%` : '-',
								style: 'infoValue'
							}
						]
					]
				},
				layout: {
					hLineWidth: () => 0,
					vLineWidth: () => 0,
					paddingLeft: () => 0,
					paddingRight: () => 15,
					paddingTop: () => 3,
					paddingBottom: () => 3
				},
				margin: [0, 0, 0, 25]
			},

			// Divider
			{
				canvas: [
					{ type: 'line', x1: 0, y1: 0, x2: 515, y2: 0, lineWidth: 1, lineColor: '#e2e8f0' }
				],
				margin: [0, 10, 0, 30]
			},

			// Questions
			{
				text: t.get('emails.resultsPdf.questions.title'),
				style: 'subheader'
			},
			flatQuestions.length > 0
				? {
						table: {
							headerRows: 1,
							widths: ['*', 'auto', 'auto'],
							body: [
								// Table header
								[
									{ text: t.get('emails.resultsPdf.questions.text'), style: 'tableHeader' },
									{ text: t.get('emails.resultsPdf.questions.answer'), style: 'tableHeader' },
									{ text: t.get('emails.resultsPdf.questions.note'), style: 'tableHeader' }
								],
								// Questions rows
								...flatQuestions.map((q) => [
									{
										text: q.text,
										style: 'questionText',
										verticalAlignment: 'middle',
										margin: [0, 12, 0, 12]
									},
									{
										text: q.answer
											? (() => {
													switch (q.answer.value) {
														case 'yes':
															return {
																text: `+ ${t.get('emails.resultsPdf.evaluationTypes.values.yes').toUpperCase()}`,
																style: 'answerYes'
															};
														case 'no':
															return {
																text: `- ${t.get('emails.resultsPdf.evaluationTypes.values.no').toUpperCase()}`,
																style: 'answerNo'
															};
														case 'ok':
															return {
																text: `+ ${t.get('emails.resultsPdf.evaluationTypes.values.ok').toUpperCase()}`,
																style: 'answerYes'
															};
														case 'nok':
															return {
																text: `- ${t.get('emails.resultsPdf.evaluationTypes.values.nok').toUpperCase()}`,
																style: 'answerNo'
															};
														case 'na':
															return {
																text: `• ${t.get('emails.resultsPdf.evaluationTypes.values.na').toUpperCase()}`,
																style: 'answerNeutral'
															};
														default:
															return {
																text: `• ${q.answer.value.toUpperCase()}`,
																style: 'answerText'
															};
													}
												})()
											: {
													text: `- ${t.get('emails.resultsPdf.questions.notEvaluated')}`,
													style: 'answerNeutral'
												},
										verticalAlignment: 'middle',
										margin: [0, 12, 0, 12]
									},
									{
										text: q.answer?.note || '-',
										style: 'noteText',
										verticalAlignment: 'middle',
										margin: [0, 12, 0, 12]
									}
								])
							]
						},
						layout: {
							hLineWidth: function (i: number) {
								return i === 0 ? 2 : 1;
							},
							vLineWidth: function () {
								return 1;
							},
							hLineColor: function (i: number) {
								return i === 0 ? '#2d3748' : '#e2e8f0';
							},
							vLineColor: function () {
								return '#e2e8f0';
							},
							fillColor: function (rowIndex: number) {
								return rowIndex % 2 === 0 ? '#f7fafc' : null;
							},
							paddingLeft: function () {
								return 12;
							},
							paddingRight: function () {
								return 12;
							},
							paddingTop: function () {
								return 0;
							},
							paddingBottom: function () {
								return 0;
							}
						}
					}
				: {
						text: t.get('emails.resultsPdf.questions.noQuestions'),
						italics: true,
						color: '#718096',
						margin: [0, 10, 0, 0]
					},

			// Footer
			{
				text: `© ${new Date().getFullYear()} LeanAudit - ${t.get('emails.resultsPdf.footer')}`,
				style: 'footer'
			}
		],
		styles: styles,
		defaultStyle: {
			font: 'Roboto'
		},
		pageSize: 'A4',
		pageMargins: [40, 40, 40, 40]
	};
}
