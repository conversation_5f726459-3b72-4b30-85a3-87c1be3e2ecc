import type { PermissionCode, RolePermissions } from '$lib/models/authModel';

export class Authorization {
	private permissions: RolePermissions;

	constructor(permissions: RolePermissions) {
		this.permissions = permissions;
	}

	public can(code: PermissionCode, action: string): boolean {
		return (
			this.permissions?.['*']?.includes('*') || //Has all permissions in all areas = superadmin
			this.permissions?.[code]?.includes('*') || //Has all permissions in this code/area
			this.permissions?.[code]?.includes(action) //Has permission for specific action in specific area
		);
	}

	public anyAction(code: PermissionCode): boolean {
		return this.permissions?.[code]?.length > 0;
	}

	public hasWildcard(code: PermissionCode): boolean {
		return this.permissions?.[code]?.includes('*');
	}

	public isSuperAdmin(roleName: string): boolean {
		return this.permissions?.['*']?.includes('*') && roleName === 'superadmin';
	}
}
