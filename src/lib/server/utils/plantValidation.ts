import { error } from '@sveltejs/kit';
import { CompanyService, PlantsService } from '$lib/server/services/tenants';
import type { AppUser } from '$lib/models/authModel';
import type { InferSelectModel } from 'drizzle-orm';
import { companyTable, plantsTable } from '$lib/db/schema/company';

type Company = InferSelectModel<typeof companyTable>;
type Plant = InferSelectModel<typeof plantsTable>;

export type ValidatedPlantContext = {
	plantId: string;
	companyId: string;
	plantSlug: string;
	companySlug: string;
	plant: Plant;
	company: Company;
};

/**
 * Validates that user has access to the specified company-plant combination
 * and returns validated context for use in services
 */
export async function validatePlantCompanyAccess(
	companySlug: string,
	plantSlug: string,
	user: AppUser
): Promise<ValidatedPlantContext> {
	// Get company by slug
	const company = await CompanyService.getCompanyBySlug(companySlug);
	if (!company) {
		throw error(404, 'Company not found');
	}

	// Get plant by slug within the company
	const plant = await PlantsService.getPlantBySlugAndCompany(plantSlug, company.id);
	if (!plant) {
		throw error(404, 'Plant not found in this company');
	}

	// Check if user has access to this plant
	const hasAccess = user.plants.includes(plant.id);
	if (!hasAccess) {
		throw error(403, 'Access denied to this plant');
	}

	return {
		plantId: plant.id,
		companyId: company.id,
		plantSlug,
		companySlug,
		plant,
		company
	};
}

/**
 * Lightweight version that only returns IDs for performance-critical operations
 */
export async function validatePlantCompanyAccessLight(
	companySlug: string,
	plantSlug: string,
	user: AppUser
): Promise<{ plantId: string; companyId: string }> {
	const context = await validatePlantCompanyAccess(companySlug, plantSlug, user);
	return {
		plantId: context.plantId,
		companyId: context.companyId
	};
}
