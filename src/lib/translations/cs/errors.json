{"common": {"notFound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unauthorized": "Neoprávněný přístup", "internalServerError": "Interní ch<PERSON>", "unknownErr": "Neznámá chyba"}, "audits": {"auditNotFound": "Audit nebyl nalezen", "auditEvaluate": {"saveFailed": "Nepodařilo se uložit odpověď"}, "auditResults": {"noAnswersFound": "Nebyly nalezeny žádné od<PERSON>vědi", "unknownQuestion": "Neznámý kontrolní bod"}, "auditSetup": {"auditNotFound": "Audit nebyl nalezen", "failedUpdate": "Nepodařilo se aktualizovat nastavení auditu", "failedAddQ": "Nepodařilo se přidat kontrolní bod", "failedEditQ": "Nepodařilo se upravit kontrolní bod", "failedDeleteQ": "Nepodařilo se smazat kontrolní bod", "failedCreatingInstance": "Nepodařilo se vytvořit instanci auditu", "noAuditSelected": "Není vybrán žádný audit", "auditDeleted": "Audit byl úsp<PERSON>š<PERSON><PERSON> s<PERSON>", "failedDeleteInst": "Nepodařilo se smazat instanci auditu", "auditCreated": "Audit byl úspěšně vytvořen", "auditUpdated": "Audit byl úspěšně aktualizován"}, "services": {"auditNotFound": "Audit nebyl nalezen", "quesitonNotFound": "Otázka neby<PERSON> na<PERSON>zena", "answerRequired": "Odpověď je pro tuto otázku povinná", "auditTypeNotFound": "Typ <PERSON>u nebyl na<PERSON>", "failedToCreateInst": "Nepodařilo se vytvořit instanci auditu", "failedToFetchAudit": "Nepodařilo se načíst audit", "failedToFetchAnswers": "Nepodařilo se načíst odpovědi", "failedToEvaluate": "Nepodařilo se vyhodnotit audit", "failedToSaveAnswer": "Nepodařilo se uložit odpověď", "failedToComplete": "Nepodařilo se uzavřít audit", "requiredQuestionsNotAnswered": "Povinné o<PERSON>ázky musí být zodpovězeny"}}, "questions": {"failedDeleting": "Nepodařilo se smazat kontrolní bod"}, "form": {"required": "<PERSON><PERSON><PERSON><PERSON>", "missingFields": "<PERSON><PERSON><PERSON><PERSON> pov<PERSON> pole", "auditTypeRequired": "<PERSON>p <PERSON>u je povinný", "auditTypeNameRequired": "Název typu auditu je povinný", "auditTypeCodeRequired": "<PERSON><PERSON><PERSON> t<PERSON>pu <PERSON>u je povinný", "auditTypeCodeMax": "K<PERSON>d t<PERSON>pu auditu může mít maximálně 10 znaků", "workplaceRequired": "Pracovišt<PERSON> je povinné", "questionRequired": "Otáz<PERSON> je povinná", "responsiblePersonRequired": "Odpovědná osoba je povinná", "plannedDateRequired": "<PERSON><PERSON><PERSON>ova<PERSON><PERSON> datum je povinné", "questionTextRequired": "Text ot<PERSON><PERSON>ky je povinný", "messageRequired": "Text zpr<PERSON>vy je povinný", "categoryAbbreviationRequired": "Zkratka sekce je povinná", "categoryAbbreviationMax": "Zkratka sekce může mít maximálně 10 znaků"}, "login": {"loginFailed": "Přihlášení se nezdařilo", "logoutFailed": "Odhlášení se nezdařilo", "invalidToken": "Neplatný token", "validEmailRequired": "Prosím, zadejte platnou e-mailovou adresu", "passwordDontMatch": "<PERSON><PERSON> se neshoduj<PERSON>", "passwordRequired": "<PERSON><PERSON><PERSON> je povinné", "passwordTooShort": "Heslo musí mít alespoň 8 znaků"}, "workplaces": {"workplaceNotFound": "Pracoviště nebylo <PERSON>lezeno", "workplaceFailedCreate": "Nepodařilo se vytvořit pracoviště", "workplaceFailedUpdate": "Nepodařilo se aktualizovat pracoviště", "workplaceFailedDelete": "Nepodařilo se smazat pracoviště", "services": {"workplaceNotUpdated": "Ž<PERSON><PERSON><PERSON> p<PERSON>oviště nebylo aktualizováno"}, "workplaceNameRequired": "Název pracoviště je povinný", "workplaceCodeRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> je povinný", "workplaceCodeMax": "Kód pracoviště může mít maximálně 10 znaků", "responsiblePersonRequired": "Odpovědná osoba je povinná", "eKaizenWorkstationRequired": "E-Kaizen pracoviště je povinné"}, "auditTypes": {"auditTypeFailedDelete": "Nepodařilo se smazat typ auditu"}, "auth": {"firstNameRequired": "Jméno je povinné", "lastNameRequired": "Příjmení je povinné", "emailRequired": "E-mail je povinný", "phoneRequired": "Telefon je povinný", "passwordRequired": "<PERSON><PERSON><PERSON> je povinné", "passwordTooShort": "Heslo musí mít alespoň 8 znaků", "passwordConfirmRequired": "<PERSON><PERSON><PERSON> je povinné", "passwordConfirmMatch": "<PERSON><PERSON> se neshoduj<PERSON>", "notAuthenticated": "Uživatel není <PERSON>", "invalidCredentials": "Neplatné <PERSON>lašovací údaje"}, "validation": {"required": "Toto pole je povinné", "timeFormat": "Musí být ve formátu HH:mm", "invalidDate": "Neplat<PERSON><PERSON> datum", "minLength": "Musí mít ale<PERSON> {min} znaků", "maxLength": "Musí mít maxim<PERSON>lně {max} znak<PERSON>", "email": "Neplatná e-mailová adresa", "password": "Heslo musí obsahovat alespoň 8 znaků, jedno velk<PERSON> písmeno, jedno malé písmeno a jedno číslo", "passwordMatch": "<PERSON><PERSON> se neshoduj<PERSON>", "number": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "min": "<PERSON><PERSON><PERSON> b<PERSON> {min}", "max": "<PERSON><PERSON><PERSON> b<PERSON>t maxi<PERSON> {max}", "oldPasswordRequired": "<PERSON><PERSON> he<PERSON>lo je povinné"}, "evaluationConfig": {"failedToSave": "Nepodařilo se uložit nastavení vyhodnocování", "error": "Chyba při vyhodnocování"}}