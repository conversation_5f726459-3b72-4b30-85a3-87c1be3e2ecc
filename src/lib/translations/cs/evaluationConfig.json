{"saved": "Nastavení vyhodnocování bylo úsp<PERSON>š<PERSON>ě ul<PERSON>", "saveFailed": "Nepodařilo se uložit nastavení vyhodnocování", "errors": {"required": "Toto pole je povinné", "numberRequired": "Zadejte plat<PERSON>", "minValue": "Hodnota musí být ale<PERSON> {min}", "maxValue": "Hodnota nesmí překročit {max}", "invalidRange": "Neplatný rozsah hodnot", "percentage": {"min": "Procenta musí být alespoň 1", "max": "Procenta nesmí překročit 100"}, "points": {"minMax": "Maximální počet bodů musí být alespoň 1"}, "weight": {"min": "Váha musí být alespoň 0", "max": "Váha nesmí překročit 1"}, "rules": {"min": "Hodnota musí být alespoň 0"}, "auditThreshold": {"min": "Práh musí být alespoň 0", "max": "<PERSON><PERSON><PERSON><PERSON> nesmí přek<PERSON> 100"}, "validation": {"successHigherThanAverage": "Práh pro úspěšný audit musí být vyšší než práh pro neúspěšný", "maxHigherThanMin": "Maximální hodnota musí být vyšší než minimální", "pointsThresholdInRange": "Práh musí být v rozsahu minimálních a maximálních bodů", "badAuditHigherThanAverage": "Práh pro neúspěšný audit musí být vyšší nebo roven prahu pro průměrný", "unsuccessfulHigherThanAverage": "Práh pro neúspěšný audit musí být vyšší než práh pro průměrný", "unsuccessfulBadHigherThanAverageBad": "Počet negativních odpovědí pro neúspěšný audit musí být vyšší než pro průměrný audit", "auditSuccessHigherThanAverage": "Práh pro úspěšný audit musí být vyšší než práh pro neúspěšný"}}, "thresholds": {"title": "Procenta a body", "percentages": {"title": "Hraniční hodnoty pro procenta", "average": "Práh pro průměrný výsledek (%)", "averageDescription": "Kontrolní body s procentuálním výsledkem pod touto hranicí budou vyhodnoceny jako <PERSON>", "averagePlaceholder": "např. 40", "success": "Práh pro úspěšný výsledek (%)", "successDescription": "Kontrolní body s procentuálním výsledkem nad touto hranicí budou oz<PERSON> jako <PERSON>", "successPlaceholder": "např. 70", "preview": "<PERSON><PERSON><PERSON><PERSON> prahov<PERSON><PERSON> hodnot"}, "points": {"title": "<PERSON><PERSON><PERSON><PERSON> bod<PERSON>", "min": "Minimální počet bodů", "max": "Maximáln<PERSON> poč<PERSON> bodů", "minDescription": "Nejni<PERSON><PERSON><PERSON> po<PERSON> bod<PERSON>, kter<PERSON> lze otázce přiřadit", "maxDescription": "Nej<PERSON><PERSON><PERSON><PERSON> po<PERSON> bod<PERSON>, kter<PERSON> lze otázce přiřadit", "minPlaceholder": "např. 1", "maxPlaceholder": "např. 100"}, "pointsThresholds": {"title": "Hraniční hodnoty pro body", "description": "<PERSON><PERSON>, od kolika bodů bude otázka hodnocena jako průměrná nebo úspěšná", "average": "Práh pro průměrný v<PERSON>dek (body)", "averageDescription": "Kontrolní body s bodov<PERSON><PERSON> výsledkem pod touto hranicí budou vyhodnoceny jako <PERSON>", "averagePlaceholder": "např. 3", "success": "Práh pro úspěšný vý<PERSON>dek (body)", "successDescription": "Kontrolní body s bodovým výsledkem nad touto hranicí budou oz<PERSON> jako <PERSON>", "successPlaceholder": "např. 7"}}, "weights": {"title": "<PERSON><PERSON><PERSON>", "currentMode": "Aktuální režim ho<PERSON>: ", "standardScore": "Standardní (skóre)", "byRules": "Podle pravidel", "evaluationType": "Typ hodnocení pro ukázku", "values": "Hodnoty vah", "fixed": "<PERSON><PERSON><PERSON><PERSON>t", "yesNo": {"description": "Ano = 1 bod, Ne = 0 bodů", "values": "Ano: 1, <PERSON><PERSON>: 0"}, "okNokNa": {"description": "OK = 1 bod, NOK = 0 bodů, NA = nezapočítáno", "values": "OK: 1, NOK: 0, NA: neza<PERSON>č<PERSON>táno"}, "withReservations": {"description": "Hodnota pro odpověď 'Částečně'", "placeholder": "např. 0.5"}}, "rules": {"title": "Diskvalifikační pravidla", "description": "Nastavte počty odpovědí, k<PERSON><PERSON> urč<PERSON> výsledek auditu", "validation": {"title": "Pozor", "description": "Práh pro červený audit musí být vyšší nebo roven prahu pro žlutý audit"}, "average": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (žlutý) audit", "count": "Počet průměrných odpovědí", "description": "Při <PERSON>í tohoto počtu bude audit automaticky vyhodnocen jako průměrný", "placeholder": "např. 2", "badCount": "Počet negativních odpovědí", "badDescription": "<PERSON><PERSON><PERSON> tohoto počtu špatných odpovědí bude audit automaticky vyhodnocen jako pr<PERSON>ý", "badPlaceholder": "např. 1"}, "unsuccessful": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (červený) audit", "averageCount": "Počet průměrných odpovědí", "averageDescription": "<PERSON>ři <PERSON>í tohoto počtu průměrných odpovědí bude audit automaticky vyhodnocen jako <PERSON>", "averagePlaceholder": "např. 4", "badCount": "Počet negativních odpovědí", "badDescription": "<PERSON>ř<PERSON> tohoto počtu špatných odpovědí bude audit automaticky vyhodnocen jako <PERSON>", "badPlaceholder": "např. 6"}}, "auditThresholds": {"title": "Hraniční hodnoty auditu", "description": "Nastavte procentní hranice pro klasifikaci výsledku auditu", "average": "Práh pro neúspěšný audit (%)", "averageDescription": "Audity s výsledkem pod touto hranicí budou vyhodnoceny jako <PERSON>", "success": "Práh pro úspěšný audit (%)", "successDescription": "Audity s výsledkem nad touto hranicí budou vyhodnoceny jako <PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "unsuccessful": "Neúspěš<PERSON>ý", "averageLabel": "Pr<PERSON><PERSON>ě<PERSON>ý", "successLabel": "Úspěšný", "averagePlaceholder": "např. 40", "successPlaceholder": "např. 70"}}