{"adminPanel": {"title": "Admin Panel"}, "companies": {"title": "Companies", "table": {"headers": {"name": "Company Name", "code": "Company Code", "status": "Status", "licenses": "Number of Licenses", "url": "URL", "actions": "Actions"}}, "dialog": {"createTitle": "Add New Company", "editTitle": "Edit Company", "deleteTitle": "Delete Company", "deleteDescription": "Are you sure you want to delete this company? This action cannot be undone."}, "form": {"name": "Company Name", "namePlaceholder": "Enter company name...", "code": "Company Code", "codePlaceholder": "Enter company code...", "slug": "Company Slug (URL Routing)", "slugPlaceholder": "Enter company slug...", "active": "Active", "url": "Company URL", "urlPlaceholder": "https://example.com", "numberOfLicenses": "Number of Licenses", "numberOfLicensesPlaceholder": "Enter number of licenses..."}, "validation": {"name": "Company name is required", "code": "Company code is required", "slug": "Company slug is required", "url": "Company URL is required", "numberOfLicenses": "Number of licenses must be greater than 0"}}, "plants": {"title": "Plants", "table": {"headers": {"name": "Plant Name", "code": "Plant Code", "company": "Company", "country": "Country", "licenses": "Number of Licenses", "gpsLocation": "GPS Location", "url": "URL", "actions": "Actions"}, "notAssigned": "Not Assigned"}, "dialog": {"createTitle": "Add New Plant", "editTitle": "Edit Plant", "deleteTitle": "Delete Plant", "deleteDescription": "Are you sure you want to delete this plant? This action cannot be undone."}, "form": {"name": "Plant Name", "namePlaceholder": "Enter plant name...", "code": "Plant Code", "codePlaceholder": "Enter plant code...", "slug": "Plant Slug (URL Routing)", "slugPlaceholder": "Enter plant slug...", "active": "Active", "status": "Status", "statusDescription": "Active plants will be able to use the application.\n Inactive plants will not be able to access the application.", "company": "Company", "selectCompany": "Select Company", "searchCompany": "Search Company...", "noCompanyFound": "No company found", "countryCode": "Country Code", "countryCodePlaceholder": "Enter country code...", "gpsLocation": "GPS Location", "gpsLocationPlaceholder": "Enter GPS location...", "url": "Plant URL", "urlPlaceholder": "https://example.com", "numberOfLicenses": "Number of Licenses", "numberOfLicensesPlaceholder": "Enter number of licenses...", "eKaizenFormURL": "eKaizen Form URL", "eKaizenFormURLPlaceholder": "Enter eKaizen Form URL...", "tasksEnabled": "Task Usage", "tasksDescription": "Enabling tasks allows you to create and manage tasks related to audits within the application. Otherwise, you can use eKaizen or other task management applications.", "createdAt": "Created", "updatedAt": "Updated"}, "validation": {"name": "Plant name is required", "code": "Plant code is required", "slug": "Plant slug is required", "countryCode": "Country code is required", "companyId": "Company is required", "numberOfLicenses": "Number of licenses must be greater than or equal to 0"}}, "users": {"title": "Users", "table": {"headers": {"name": "Name", "email": "Email", "phone": "Phone", "company": "Company", "mainPlant": "Main Plant", "actions": "Actions"}, "noName": "No Name", "notProvided": "Not Provided", "notAssigned": "Not Assigned"}, "editUser": {"successUpdate": "User has been successfully updated"}, "dialog": {"createTitle": "Add New User", "editTitle": "Edit User", "deleteTitle": "Delete User", "deleteDescription": "Are you sure you want to delete this user? This action cannot be undone."}, "form": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone Number", "firstNamePlaceholder": "Enter first name...", "lastNamePlaceholder": "Enter last name...", "emailPlaceholder": "Enter email...", "phonePlaceholder": "Enter phone number...", "company": "Company", "selectCompany": "Select Company", "searchCompany": "Search Company...", "noCompanyFound": "No company found", "mainPlant": "Main Plant", "selectPlant": "Select Plant", "searchPlant": "Search Plant...", "noPlantFound": "No plant found", "selectCompanyFirst": "Select company first...", "passwordPlaceholder": "Enter password...", "passwordConfirmPlaceholder": "Confirm password...", "cardNumber": "Card Number (optional)", "cardNumberPlaceholder": "Enter card number (optional)"}, "toasts": {"created": "User has been successfully created", "saved": "User has been successfully saved", "saveFailed": "User saving failed"}, "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailInvalid": "Invalid email", "phoneRequired": "Phone number is required", "passwordTooShort": "Password must be at least 8 characters long", "passwordConfirmRequired": "Password confirmation is required"}, "actionMessages": {"deleted": "User has been successfully deleted"}, "info": {"name": "Name", "email": "Email", "phone": "Phone Number", "company": "Company", "mainPlant": "Main Plant", "cardNumber": "Card Number"}}}