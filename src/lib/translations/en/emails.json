{"forgottenPassword": {"title": "Password Reset", "subject": "Password Reset for Your LeanAudit Account", "text": "You received this email because you requested a password reset for your LeanAudit account. Click the button below to set a new password.", "expiration": "This link will expire in 30 minutes for security reasons. If you didn't request this password reset, please ignore this email.", "buttonText": "Reset Password", "securityNoticeTitle": "Security Notice:", "fallbackText": "If the button doesn't work, you can copy and paste this link into your browser:", "regards": "Best regards,", "teamName": "The LeanAudit Team", "footerInfo": "This email was sent to you because a password reset was requested for your account.", "supportText": "If you have any questions, please contact our support team."}, "contactEmail": {"title": "Contact from audit", "subject": "Message from audit evaluation", "greeting": "Hello,", "intro": "you have received a message from audit:", "questionLabel": "Checklist item:", "messageLabel": "Message:", "senderLabel": "Sender:", "footer": "This email was automatically generated from the Lean Audit system.", "errors": {"questionNotFound": "Checklist item not found", "questionLoadError": "Error loading checklist item", "noQuestionSelected": "No checklist item selected"}}, "negativeCheckpoints": {"title": "Negative Checkpoints", "subject": "Negative Checkpoints - {auditTitle}", "greeting": "Hello", "intro": "an audit has been completed and negative checkpoints have been identified that require your attention.", "negativeFound": "During the audit, {count} negative checkpoints were identified:", "checkpointsHeader": "Negative Checkpoints:", "noteLabel": "Note", "viewAuditButton": "View Complete Audit", "footer": "This email was automatically generated by the LeanAudit system.", "regards": "Best regards,", "auditTeam": "LeanAudit Team", "auditInfoTitle": "Audit Information", "auditLabel": "Audit", "workplaceLabel": "Workplace", "auditorLabel": "Auditor", "dateLabel": "Date", "emailSent": "<PERSON><PERSON> was successfully sent to the responsible person", "emailFailed": "Failed to send email"}, "resultsPdf": {"title": "Audit Results", "auditInfo": {"title": "Basic Information", "code": "Audit Code", "workplace": "Workplace", "auditType": "Audit Type", "auditor": "Auditor", "plannedDate": "Planned Date", "completionDate": "Completion Date", "expectedDuration": "Expected Duration", "realDuration": "Actual Duration", "status": "Audit Status", "successRate": "Success Rate", "generated": "Generated"}, "questions": {"title": "Checkpoints", "category": "Section", "text": "Text", "answer": "Answer", "note": "Note", "attachments": "Attachments", "noAttachments": "No attachments", "notEvaluated": "Not evaluated", "noQuestions": "No checklist items to display"}, "attachments": {"title": "Attachments", "total": "Total attachments", "noAttachments": "No attachments for this audit"}, "status": {"planned": "Planned", "inProgress": "In Progress", "closed": "Closed", "late": "Late", "completed": "Completed"}, "evaluationTypes": {"oknok": "OK / NOK / N/A", "yesno": "Yes/No", "yesno_inverse": "No/Yes", "points": "Points", "percentage": "Percentage", "values": {"ok": "OK", "nok": "NOK", "na": "N/A", "yes": "Yes", "no": "No"}}, "footer": "All rights reserved"}}