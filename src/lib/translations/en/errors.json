{"auth": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "phoneRequired": "Phone number is required", "passwordRequired": "Password is required", "passwordConfirmRequired": "Password confirmation is required", "passwordConfirmMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters long", "notAuthenticated": "User is not authenticated", "invalidCredentials": "Invalid login credentials"}, "common": {"notFound": "Not found", "unauthorized": "Unauthorized access", "internalServerError": "Internal server error", "unknownErr": "Unknown error"}, "audits": {"auditNotFound": "<PERSON><PERSON> not found", "auditEvaluate": {"saveFailed": "Failed to save answer"}, "auditResults": {"noAnswersFound": "No answers found", "unknownQuestion": "Unknown checklist item"}, "auditSetup": {"auditNotFound": "<PERSON><PERSON> not found", "failedUpdate": "Failed to update audit settings", "failedAddQ": "Failed to add checklist item", "failedEditQ": "Failed to edit checklist item", "failedDeleteQ": "Failed to delete checklist item", "failedCreatingInstance": "Failed to create audit instance", "noAuditSelected": "No audit selected", "auditDeleted": "<PERSON><PERSON> was successfully deleted", "failedDeleteInst": "Failed to delete audit instance", "auditCreated": "Audit was successfully created", "auditUpdated": "Audit was successfully updated"}, "services": {"auditNotFound": "<PERSON><PERSON> not found", "quesitonNotFound": "Checklist item not found", "answerRequired": "Answer is required for this item", "auditTypeNotFound": "Audit type not found", "failedToCreateInst": "Failed to create audit instance", "failedToFetchAudit": "Failed to fetch audit", "failedToFetchAnswers": "Failed to fetch answers", "failedToEvaluate": "Failed to evaluate audit", "failedToSaveAnswer": "Failed to save answer", "failedToComplete": "Failed to close audit", "requiredQuestionsNotAnswered": "All required checklist items must be answered"}}, "questions": {"failedDeleting": "Failed to delete checklist item"}, "form": {"required": "Required", "missingFields": "Required fields are missing", "auditTypeRequired": "Audit type is required", "auditTypeNameRequired": "Audit type name is required", "auditTypeCodeRequired": "Audit type code is required", "auditTypeCodeMax": "Audit type code must be at most 10 characters long", "workplaceRequired": "Location is required", "questionRequired": "Checklist item is required", "responsiblePersonRequired": "Responsible person is required", "plannedDateRequired": "Scheduled date is required", "questionTextRequired": "Checklist item text is required", "messageRequired": "Message is required", "categoryAbbreviationRequired": "Category abbreviation is required", "categoryAbbreviationMax": "Category abbreviation must be at most 10 characters long"}, "login": {"loginFailed": "<PERSON><PERSON> failed", "logoutFailed": "Logout failed", "invalidToken": "Invalid token", "validEmailRequired": "Please enter a valid email address", "passwordDontMatch": "Passwords do not match", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters long"}, "workplaces": {"workplaceNotFound": "Location not found", "workplaceFailedCreate": "Failed to create location", "workplaceFailedUpdate": "Failed to update location", "workplaceFailedDelete": "Failed to delete location", "services": {"workplaceNotUpdated": "No location was updated"}, "workplaceNameRequired": "Location name is required", "workplaceCodeRequired": "Location code is required", "workplaceCodeMax": "Location code must be at most 10 characters long", "responsiblePersonRequired": "Responsible person is required", "eKaizenWorkstationRequired": "eKaizen workstation is required"}, "auditTypes": {"auditTypeFailedDelete": "Failed to delete audit type"}, "validation": {"required": "This field is required", "timeFormat": "Must be in format HH:mm", "invalidDate": "Invalid date", "minLength": "Must be at least {min} characters", "maxLength": "Must be at most {max} characters", "email": "Invalid email address", "password": "Password must contain at least 8 characters, one uppercase letter, one lowercase letter and one number", "passwordMatch": "Passwords do not match", "number": "Must be a number", "min": "Must be at least {min}", "max": "Must be at most {max}", "oldPasswordRequired": "Old password is required"}, "evaluationConfig": {"failedToSave": "Failed to save evaluation settings", "unknownError": "Unknown error occurred while saving evaluation settings"}}