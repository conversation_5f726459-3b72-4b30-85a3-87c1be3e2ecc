{"saved": "Evaluation settings saved successfully", "saveFailed": "Failed to save evaluation settings", "errors": {"required": "This field is required", "numberRequired": "Please enter a valid number", "minValue": "Value must be at least {min}", "maxValue": "Value must not exceed {max}", "invalidRange": "Invalid value range", "percentage": {"min": "Percentage must be at least 1", "max": "Percentage must not exceed 100"}, "points": {"minMax": "Maximum points must be at least 1"}, "weight": {"min": "Weight must be at least 0", "max": "Weight must not exceed 1"}, "rules": {"min": "Value must be at least 0"}, "auditThreshold": {"min": "Threshold must be at least 0", "max": "Thresh<PERSON> must not exceed 100"}, "validation": {"successHigherThanAverage": "Success threshold must be higher than average threshold", "maxHigherThanMin": "Maximum value must be higher than minimum", "pointsThresholdInRange": "Threshold must be within the minimum and maximum points range", "badAuditHigherThanAverage": "Unsuccessful audit threshold must be higher than or equal to average audit threshold", "unsuccessfulHigherThanAverage": "Unsuccessful audit threshold must be higher than average audit threshold", "unsuccessfulBadHigherThanAverageBad": "Number of negative answers for unsuccessful audit must be higher than for average audit", "auditSuccessHigherThanAverage": "Successful audit threshold must be higher than average audit threshold"}}, "thresholds": {"title": "Percentage and Points", "percentages": {"title": "Percentage Thresholds", "average": "Average Result Threshold (%)", "averageDescription": "Checkpoints with percentage below this threshold will be evaluated as not successful", "averagePlaceholder": "e.g. 40", "success": "Success Result Threshold (%)", "successDescription": "Checkpoints with percentage above this threshold will be marked as successful", "successPlaceholder": "e.g. 70", "preview": "Threshold Preview"}, "points": {"title": "Points Range", "min": "Minimum Points", "max": "Maximum Points", "minDescription": "Lowest number of points that can be assigned to a question", "maxDescription": "Highest number of points that can be assigned to a question", "minPlaceholder": "e.g. 1", "maxPlaceholder": "e.g. 100"}, "pointsThresholds": {"title": "Points Thresholds", "description": "These thresholds determine from how many points a question will be evaluated as average or successful", "average": "Average Result <PERSON><PERSON><PERSON><PERSON> (points)", "averageDescription": "Checkpoints with points below this threshold will be vyhodnoceny as not successful", "averagePlaceholder": "e.g. 3", "success": "Success Result <PERSON><PERSON><PERSON><PERSON> (points)", "successDescription": "Checkpoints with points above this threshold will be marked as successful", "successPlaceholder": "e.g. 7"}}, "weights": {"title": "Evaluation Weights", "currentMode": "Current Evaluation Mode:", "standardScore": "Standard (score)", "byRules": "By Rules", "evaluationType": "Evaluation Type for Preview", "values": "Weight Values", "fixed": "Cannot be adjusted", "yesNo": {"description": "Yes = 1 point, No = 0 points", "values": "Yes: 1, No: 0"}, "okNokNa": {"description": "OK = 1 point, NOK = 0 points, NA = not counted", "values": "OK: 1, NOK: 0, NA: not counted"}, "withReservations": {"description": "Value for 'Partly' answer", "placeholder": "e.g. 0.5"}}, "rules": {"title": "Disqualifying Rules", "description": "Set answer counts that determine audit result", "validation": {"title": "Warning", "description": "Red audit threshold must be higher than or equal to yellow audit threshold"}, "average": {"title": "Average (Yellow) Audit", "count": "Number of Average Answers", "description": "Audit will be automatically marked as average when this count is reached", "placeholder": "e.g. 2", "badCount": "Number of Negative Answers", "badDescription": "<PERSON><PERSON> will be automatically marked as average when this count of bad answers is reached", "badPlaceholder": "e.g. 1"}, "unsuccessful": {"title": "Unsuccessful (Red) Audit", "averageCount": "Number of Average Answers", "averageDescription": "<PERSON><PERSON> will be automatically vyhodnoceny as unsuccessful when this count of average answers is reached", "averagePlaceholder": "e.g. 4", "badCount": "Number of Negative Answers", "badDescription": "<PERSON><PERSON> will be automatically marked as unsuccessful when this count of bad answers is reached", "badPlaceholder": "e.g. 6"}}, "auditThresholds": {"title": "<PERSON><PERSON>s", "description": "Set percentage boundaries for overall audit result classification", "average": "Average Audit Threshold (%)", "averageDescription": "Audits with results below this threshold will be vyhodnoceny as not successful", "success": "Successful <PERSON><PERSON> (%)", "successDescription": "Audits with results above this threshold will be vyhodnoceny as successful", "preview": "Thresholds Preview", "unsuccessful": "Unsuccessful", "averageLabel": "Average", "successLabel": "Successful", "averagePlaceholder": "e.g. 40", "successPlaceholder": "e.g. 70"}}