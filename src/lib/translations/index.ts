import i18n, { type Config } from 'sveltekit-i18n';
import { dev } from '$app/environment';

type Params = {
	company?: string;
};

const config: Config<Params> = {
	log: {
		level: dev ? 'error' : 'error'
	},
	loaders: [
		{
			locale: 'cs',
			key: 'auth',
			loader: async () => (await import('./cs/auth.json')).default
		},
		{
			locale: 'cs',
			key: 'common',
			loader: async () => (await import('./cs/common.json')).default
		},
		{
			locale: 'cs',
			key: 'dashboard',
			loader: async () => (await import('./cs/dashboard.json')).default
		},
		{
			locale: 'cs',
			key: 'errors',
			loader: async () => (await import('./cs/errors.json')).default
		},
		{
			locale: 'cs',
			key: 'audits',
			loader: async () => (await import('./cs/audits.json')).default
		},
		{
			locale: 'cs',
			key: 'workplaces',
			loader: async () => (await import('./cs/workplaces.json')).default
		},
		{
			locale: 'cs',
			key: 'auditTypes',
			loader: async () => (await import('./cs/auditTypes.json')).default
		},
		{
			locale: 'cs',
			key: 'questions',
			loader: async () => (await import('./cs/questions.json')).default
		},
		{
			locale: 'cs',
			key: 'auditors',
			loader: async () => (await import('./cs/auditors.json')).default
		},
		{
			locale: 'cs',
			key: 'emails',
			loader: async () => (await import('./cs/emails.json')).default
		},
		{
			locale: 'cs',
			key: 'admin',
			loader: async () => (await import('./cs/admin.json')).default
		},
		{
			locale: 'cs',
			key: 'permissions',
			loader: async () => (await import('./cs/permissions.json')).default
		},
		{
			locale: 'cs',
			key: 'evaluationConfig',
			loader: async () => (await import('./cs/evaluationConfig.json')).default
		},
		{
			locale: 'cs',
			key: 'plantAdmin',
			loader: async () => (await import('./cs/plantAdmin.json')).default
		},

		//English
		{
			locale: 'en',
			key: 'auth',
			loader: async () => (await import('./en/auth.json')).default
		},
		{
			locale: 'en',
			key: 'common',
			loader: async () => (await import('./en/common.json')).default
		},
		{
			locale: 'en',
			key: 'dashboard',
			loader: async () => (await import('./en/dashboard.json')).default
		},
		{
			locale: 'en',
			key: 'errors',
			loader: async () => (await import('./en/errors.json')).default
		},
		{
			locale: 'en',
			key: 'audits',
			loader: async () => (await import('./en/audits.json')).default
		},
		{
			locale: 'en',
			key: 'workplaces',
			loader: async () => (await import('./en/workplaces.json')).default
		},
		{
			locale: 'en',
			key: 'auditTypes',
			loader: async () => (await import('./en/auditTypes.json')).default
		},
		{
			locale: 'en',
			key: 'questions',
			loader: async () => (await import('./en/questions.json')).default
		},
		{
			locale: 'en',
			key: 'auditors',
			loader: async () => (await import('./en/auditors.json')).default
		},
		{
			locale: 'en',
			key: 'emails',
			loader: async () => (await import('./en/emails.json')).default
		},
		{
			locale: 'en',
			key: 'admin',
			loader: async () => (await import('./en/admin.json')).default
		},
		{
			locale: 'en',
			key: 'permissions',
			loader: async () => (await import('./en/permissions.json')).default
		},
		{
			locale: 'en',
			key: 'evaluationConfig',
			loader: async () => (await import('./en/evaluationConfig.json')).default
		},
		{
			locale: 'en',
			key: 'plantAdmin',
			loader: async () => (await import('./en/plantAdmin.json')).default
		}
	]
};

export const { t, locale, locales, loading, loadTranslations } = new i18n(config);
