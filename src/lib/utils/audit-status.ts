import type { AuditListDTO } from '$lib/DTO/audits/audits';

/**
 * Get audit status based on progress and completion date
 */
export function getAuditStatus(audit: AuditListDTO): string {
	if (audit.progress === -1) {
		return 'late';
	} else if (audit.completionDate && audit.successRate !== undefined) {
		return 'closed';
	} else if (audit.progress === 100) {
		return 'completed';
	} else if (audit.progress > 0) {
		return 'inProgress';
	} else {
		return 'planned';
	}
}

/**
 * Get numeric status priority for sorting (lower number = higher priority)
 */
export function getStatusSortPriority(audit: AuditListDTO): number {
	const status = getAuditStatus(audit);

	// Priority order: late (highest priority) -> planned -> inProgress -> completed -> closed (lowest priority)
	switch (status) {
		case 'late':
			return 1;
		case 'planned':
			return 2;
		case 'inProgress':
			return 3;
		case 'completed':
			return 4;
		case 'closed':
			return 5;
		default:
			return 6;
	}
}
