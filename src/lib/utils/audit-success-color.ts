/**
 * Get color based on audit success rate
 * Green: 85%+, Orange: 70-84%, Red: <70%
 */
export function getAuditSuccessColor(successRate: number): {
	fill: string;
	track: string;
	stopColor1: string;
	stopColor2: string;
} {
	if (successRate >= 85) {
		// Green
		return {
			fill: 'url(#successGradientGreen)',
			track: 'hsl(var(--muted))',
			stopColor1: '#90DAB4',
			stopColor2: '#2E5D3E'
		};
	} else if (successRate >= 70) {
		// Orange
		return {
			fill: 'url(#successGradientOrange)',
			track: 'hsl(var(--muted))',
			stopColor1: '#F4D03F',
			stopColor2: '#8B6914'
		};
	} else {
		// Red
		return {
			fill: 'url(#successGradientRed)',
			track: 'hsl(var(--muted))',
			stopColor1: '#D18385',
			stopColor2: '#7A2E32'
		};
	}
}
