import type { PlantEvaluationConfig } from '$lib/db/schema/company';
import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';

type EvaluationConfig = {
	evaluationMode: string;
	average_averageAuditAnswers?: number | null;
	average_badAuditAnswers?: number | null;
	notSuccessful_averageAnswers?: number | null;
	notSuccessful_badAnswers?: number | null;
};

type AuditInstanceConfig = {
	auditThreshold_average?: number | null;
	auditThreshold_success?: number | null;
};

export class AuditScoreService {
	static classifyAnswerColor(
		answer: AuditAnswerResultsDTO,
		plantConfig: PlantEvaluationConfig
	): 'success' | 'average' | 'bad' | 'gray' {
		const value = answer.evaluationValue;
		if (!value) {
			return 'gray';
		}

		const evaluationType = answer.evaluationType;

		// Use plant config thresholds or defaults
		const avgThreshold = plantConfig.auditThreshold_average ?? 70;
		const successThreshold = plantConfig.auditThreshold_success ?? 80;
		const pointsMin = plantConfig.pointsRangeMin ?? 0;
		const pointsMax = plantConfig.pointsRangeMax ?? 100;

		if (evaluationType === 'percentage') {
			const percentValue = parseFloat(value);
			if (isNaN(percentValue)) {
				return 'gray';
			}

			if (percentValue >= successThreshold) {
				return 'success';
			} else if (percentValue >= avgThreshold) {
				return 'average';
			} else {
				return 'bad';
			}
		}

		if (evaluationType === 'points') {
			const pointValue = parseFloat(value);
			if (isNaN(pointValue)) {
				return 'gray';
			}

			// Calculate percentage based on plant config range
			const range = pointsMax - pointsMin;
			const normalizedValue = pointValue - pointsMin;
			const percentage = range > 0 ? (normalizedValue / range) * 100 : 0;

			if (percentage >= successThreshold) {
				return 'success';
			} else if (percentage >= avgThreshold) {
				return 'average';
			} else {
				return 'bad';
			}
		}

		const lowerValue = value.toLowerCase();
		switch (lowerValue) {
			case 'ok':
			case 'yes':
			case 'no_inverse':
			case 'meets':
				return 'success';
			case 'nok':
			case 'no':
			case 'yes_inverse':
			case 'doesntmeet':
				return 'bad';
			case 'wreservations':
				return 'average';
			case 'na':
				return 'gray';
			default:
				return 'gray';
		}
	}

	static getAuditResultColor(
		successRate: number,
		config: PlantEvaluationConfig
	): 'success' | 'average' | 'bad' {
		if (successRate >= config.auditThreshold_success!) return 'success';
		if (config.auditThreshold_average && successRate >= config.auditThreshold_average)
			return 'average';
		return 'bad';
	}

	static getAuditResultColorByRules(
		answers: AuditAnswerResultsDTO[],
		evaluationConfig: EvaluationConfig,
		plantConfig: PlantEvaluationConfig
	): 'success' | 'average' | 'bad' {
		if (!evaluationConfig || evaluationConfig.evaluationMode !== 'rules') {
			return 'average';
		}

		const classifiedAnswers = answers.map((answer) =>
			this.classifyAnswerColor(answer, plantConfig)
		);

		const averageAnswers = classifiedAnswers.filter((color) => color === 'average').length;

		const badAnswers = classifiedAnswers.filter((color) => color === 'bad').length;

		// Check for "bad" result first (most restrictive)
		const notSuccessfulAverageThreshold = evaluationConfig.notSuccessful_averageAnswers;
		const notSuccessfulBadThreshold = evaluationConfig.notSuccessful_badAnswers;

		if (notSuccessfulBadThreshold && badAnswers >= notSuccessfulBadThreshold) {
			return 'bad';
		}
		if (notSuccessfulAverageThreshold && averageAnswers >= notSuccessfulAverageThreshold) {
			return 'bad';
		}

		// Check for "average" result
		const averageThreshold_average = evaluationConfig.average_averageAuditAnswers;
		const averageThreshold_bad = evaluationConfig.average_badAuditAnswers;

		if (averageThreshold_bad && averageThreshold_bad > 0 && badAnswers >= averageThreshold_bad) {
			return 'average';
		}
		if (averageThreshold_average && averageThreshold_average > 0 && averageAnswers >= averageThreshold_average) {
			return 'average';
		}

		return 'success';
	}

	static getAuditResultColorCombined(
		successRate: number,
		answers: AuditAnswerResultsDTO[],
		plantConfig: PlantEvaluationConfig,
		auditEvaluationConfig?: EvaluationConfig | null
	): 'success' | 'average' | 'bad' {
		if (auditEvaluationConfig && auditEvaluationConfig.evaluationMode === 'rules') {
			return this.getAuditResultColorByRules(answers, auditEvaluationConfig, plantConfig);
		}

		return this.getAuditResultColor(successRate, plantConfig);
	}

	static getAuditBadgeColor(
		rulesResult: string | null | undefined,
		successRate: number,
		plantConfig: PlantEvaluationConfig,
		auditInstanceConfig?: AuditInstanceConfig | null
	): 'success' | 'average' | 'bad' {
		if (rulesResult) {
			return rulesResult as 'success' | 'average' | 'bad';
		}

		const avgThreshold =
			auditInstanceConfig?.auditThreshold_average ?? plantConfig.auditThreshold_average ?? 70;
		const successThreshold =
			auditInstanceConfig?.auditThreshold_success ?? plantConfig.auditThreshold_success ?? 80;

		if (successRate >= successThreshold) return 'success';
		if (successRate >= avgThreshold) return 'average';
		return 'bad';
	}
}

export function classifyAnswerColor(
	answer: AuditAnswerResultsDTO,
	plantConfig: PlantEvaluationConfig
): 'success' | 'average' | 'bad' | 'gray' {
	const value = answer.evaluationValue;
	if (!value) {
		return 'gray';
	}

	const evaluationType = answer.evaluationType;

	// Use plant config thresholds or defaults
	const avgThreshold = plantConfig.auditThreshold_average ?? 70;
	const successThreshold = plantConfig.auditThreshold_success ?? 80;
	const pointsMin = plantConfig.pointsRangeMin ?? 0;
	const pointsMax = plantConfig.pointsRangeMax ?? 100;

	if (evaluationType === 'percentage') {
		const percentValue = parseFloat(value);
		if (isNaN(percentValue)) {
			return 'gray';
		}

		if (percentValue >= successThreshold) {
			return 'success';
		} else if (percentValue >= avgThreshold) {
			return 'average';
		} else {
			return 'bad';
		}
	}

	if (evaluationType === 'points') {
		const pointValue = parseFloat(value);
		if (isNaN(pointValue)) {
			return 'gray';
		}

		const range = pointsMax - pointsMin;
		const normalizedValue = pointValue - pointsMin;
		const percentage = range > 0 ? (normalizedValue / range) * 100 : 0;

		if (percentage >= successThreshold) {
			return 'success';
		} else if (percentage >= avgThreshold) {
			return 'average';
		} else {
			return 'bad';
		}
	}

	const lowerValue = value.toLowerCase();
	switch (lowerValue) {
		case 'ok':
		case 'yes':
		case 'no_inverse':
		case 'meets':
			return 'success';
		case 'nok':
		case 'no':
		case 'yes_inverse':
		case 'doesntmeet':
			return 'bad';
		case 'wreservations':
			return 'average';
		case 'na':
			return 'gray';
		default:
			return 'gray';
	}
}

export function getAuditResultColor(successRate: number, config: PlantEvaluationConfig) {
	if (successRate >= config.auditThreshold_success!) return 'success';
	if (config.auditThreshold_average && successRate >= config.auditThreshold_average)
		return 'average';
	return 'bad';
}

export function getAuditResultColorByRules(
	answers: AuditAnswerResultsDTO[],
	evaluationConfig: EvaluationConfig,
	plantConfig: PlantEvaluationConfig
): 'success' | 'average' | 'bad' {
	if (!evaluationConfig || evaluationConfig.evaluationMode !== 'rules') {
		return 'average';
	}

	const classifiedAnswers = answers.map((answer) => classifyAnswerColor(answer, plantConfig));

	const averageAnswers = classifiedAnswers.filter((color) => color === 'average').length;

	const badAnswers = classifiedAnswers.filter((color) => color === 'bad').length;

	// Check for "bad" result first (most restrictive)
	const notSuccessfulAverageThreshold = evaluationConfig.notSuccessful_averageAnswers;
	const notSuccessfulBadThreshold = evaluationConfig.notSuccessful_badAnswers;

	if (notSuccessfulBadThreshold && badAnswers >= notSuccessfulBadThreshold) {
		return 'bad';
	}
	if (notSuccessfulAverageThreshold && averageAnswers >= notSuccessfulAverageThreshold) {
		return 'bad';
	}

	// Check for "average" result
	const averageThreshold_average = evaluationConfig.average_averageAuditAnswers;
	const averageThreshold_bad = evaluationConfig.average_badAuditAnswers;

	if (averageThreshold_bad && averageThreshold_bad > 0 && badAnswers >= averageThreshold_bad) {
		return 'average';
	}
	if (averageThreshold_average && averageThreshold_average > 0 && averageAnswers >= averageThreshold_average) {
		return 'average';
	}

	return 'success';
}

export function getAuditResultColorCombined(
	successRate: number,
	answers: AuditAnswerResultsDTO[],
	plantConfig: PlantEvaluationConfig,
	auditEvaluationConfig?: EvaluationConfig | null
): 'success' | 'average' | 'bad' {
	if (auditEvaluationConfig && auditEvaluationConfig.evaluationMode === 'rules') {
		return getAuditResultColorByRules(answers, auditEvaluationConfig, plantConfig);
	}

	return getAuditResultColor(successRate, plantConfig);
}
