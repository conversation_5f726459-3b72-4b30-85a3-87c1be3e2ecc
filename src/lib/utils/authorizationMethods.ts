import type { PermissionCode } from '$lib/models/authModel';

//Can method for checking permissions on frontend
function can(
	permissions: Record<string, string[]>,
	permissionCode: PermissionCode,
	action: string
): boolean {
	return (
		permissions?.['*']?.includes('*') || //all permissions in all areas - superadmin
		permissions?.[permissionCode]?.includes('*') || //all permissions in this code/area
		permissions?.[permissionCode]?.includes(action)
	);
}

function isSuperAdmin(permissions: Record<string, string[]>, roleName: string): boolean {
	return permissions?.['*']?.includes('*') && roleName === 'superadmin';
}

export const AuthMethods = {
	can,
	isSuperAdmin
};
