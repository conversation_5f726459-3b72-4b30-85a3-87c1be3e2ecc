import type { AttachmentFile } from '$lib/DTO/audits/auditEvaluation';

export interface AutoSaveData {
	evaluationValue: string | null;
	note: string;
	attachments: AttachmentFile[];
	filesToRemove?: string[]; // URLs of files to remove
	realDuration?: number; // Optional duration tracking
}

export interface AutoSaveResult {
	success: boolean;
	files?: AttachmentFile[];
	error?: string;
}

export type SaveFunction = (data: AutoSaveData) => Promise<AutoSaveResult>;

/**
 * AutoSaver handles auto-saving with race condition protection and upload tracking
 */
export class AutoSaver {
	private version = 0;
	private activeUploads = new Set<string>();
	private lastSavedState: string | null = null;
	private lastSavedData: AutoSaveData | null = null;

	/**
	 * Track an active upload
	 */
	trackUpload(uploadId: string): void {
		this.activeUploads.add(uploadId);
	}

	/**
	 * Untrack an upload when completed
	 */
	untrackUpload(uploadId: string): void {
		this.activeUploads.delete(uploadId);
	}

	/**
	 * Check if there are any active uploads
	 */
	get hasActiveUploads(): boolean {
		return this.activeUploads.size > 0;
	}

	/**
	 * Check if data has changed since last save
	 * Note: realDuration is excluded from comparison as it changes frequently
	 */
	hasChanges(data: AutoSaveData): boolean {
		const currentState = JSON.stringify({
			evaluationValue: data.evaluationValue,
			note: data.note,
			attachments: data.attachments.map((f) => ({
				url: f.url,
				filename: f.filename,
				type: f.type
			})),
			filesToRemove: data.filesToRemove || []
			// realDuration excluded from comparison
		});

		return this.lastSavedState !== currentState;
	}

	/**
	 * Save data with race condition protection
	 */
	async save(data: AutoSaveData, saveFn: SaveFunction): Promise<AutoSaveResult | null> {
		// Check if data has changed
		if (!this.hasChanges(data)) {
			return null; // No changes, skip save
		}

		const currentVersion = ++this.version;

		// Wait for active uploads to complete
		while (this.activeUploads.size > 0) {
			await new Promise((resolve) => setTimeout(resolve, 100));
		}

		try {
			const result = await saveFn(data);

			// Only process result if this is still the latest version
			if (currentVersion === this.version) {
				if (result.success) {
					// Update last saved state (exclude realDuration from state comparison)
					this.lastSavedState = JSON.stringify({
						evaluationValue: data.evaluationValue,
						note: data.note,
						attachments: data.attachments.map((f) => ({
							url: f.url,
							filename: f.filename,
							type: f.type
						})),
						filesToRemove: data.filesToRemove || []
						// realDuration excluded from state tracking
					});
				}
				return result;
			}

			// Outdated version, ignore result
			return null;
		} catch (error) {
			// Only throw error for latest version
			if (currentVersion === this.version) {
				throw error;
			}
			return null;
		}
	}

	/**
	 * Reset the saved state (e.g., when switching questions)
	 */
	reset(): void {
		this.lastSavedState = null;
		this.version = 0;
		this.activeUploads.clear();
	}

	/**
	 * Initialize with existing data (mark as already saved)
	 * Useful when switching to a question that already has saved data
	 */
	initializeWithData(data: AutoSaveData): void {
		this.lastSavedState = JSON.stringify({
			evaluationValue: data.evaluationValue,
			note: data.note,
			attachments: data.attachments.map((f) => ({
				url: f.url,
				filename: f.filename,
				type: f.type
			})),
			filesToRemove: data.filesToRemove || []
			// realDuration excluded from comparison
		});
	}

	/**
	 * Get the last saved data for comparison
	 */
	getLastSavedData(): AutoSaveData | null {
		if (!this.lastSavedState) {
			return null;
		}

		try {
			return JSON.parse(this.lastSavedState);
		} catch {
			return null;
		}
	}
}
