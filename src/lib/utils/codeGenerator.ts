import { db } from '$lib/db/db.server';
import { auditInstancesTable } from '$lib/db/schema/audits';
import { sql } from 'drizzle-orm';

//Generator for correctly formatting audit codes

async function getLastNumberForYear(prefix: string, year: string): Promise<number> {
	const upperCasePrefix = prefix.toUpperCase();
	const result = await db
		.select({
			code: auditInstancesTable.code
		})
		.from(auditInstancesTable)
		.where(sql`${auditInstancesTable.code} LIKE ${upperCasePrefix + '-' + year + '-%'}`)
		.orderBy(sql`${auditInstancesTable.code} DESC`)
		.limit(1);

	if (result.length === 0) return 0;

	const lastCode = result[0].code;
	const number = parseInt(lastCode.split('-')[2]);
	return isNaN(number) ? 0 : number;
}

export async function generateAuditCode(prefix: string): Promise<string> {
	const upperCasePrefix = prefix.toUpperCase();
	const year = new Date().getFullYear().toString();
	const number = await getLastNumberForYear(upperCasePrefix, year);
	const newNumber = (number + 1).toString().padStart(3, '0');
	return `${upperCasePrefix}-${year}-${newNumber}`;
}
