import type { AuditListDTO } from '$lib/DTO/audits/audits';
import { formatDate } from './date';
import { t } from '$lib/translations';

export interface AuditCSVData {
	code: string;
	auditType: string;
	workplace: string;
	auditor: string;
	plannedDate: string;
	completionDate: string;
	progress: string;
	createdBy: string;
	createdAt: string;
}

export function convertAuditsToCSV(audits: AuditListDTO[], locale: string = 'en'): string {
	const headers = [
		t.get('audits.csvExport.headers.code'),
		t.get('audits.csvExport.headers.auditType'),
		t.get('audits.csvExport.headers.workplace'),
		t.get('audits.csvExport.headers.auditor'),
		t.get('audits.csvExport.headers.plannedDate'),
		t.get('audits.csvExport.headers.completionDate'),
		t.get('audits.csvExport.headers.progress'),
		t.get('audits.csvExport.headers.createdBy'),
		t.get('audits.csvExport.headers.createdAt')
	];

	const csvData: AuditCSVData[] = audits.map((audit) => ({
		code: audit.code,
		auditType: audit.auditType?.name || '',
		workplace: audit.workplace?.name || '',
		auditor: audit.auditor
			? `${audit.auditor.firstName || ''} ${audit.auditor.lastName || ''}`.trim()
			: '',
		plannedDate: formatDate(audit.plannedDate, locale),
		completionDate: audit.completionDate ? formatDate(audit.completionDate, locale) : '',
		progress: getProgressText(audit),
		createdBy: audit.createdBy
			? `${audit.createdBy.firstName || ''} ${audit.createdBy.lastName || ''}`.trim()
			: '',
		createdAt: audit.createdAt ? formatDate(new Date(audit.createdAt).toISOString(), locale) : ''
	}));

	// Convert to CSV format
	const csvRows = [
		headers.join(','),
		...csvData.map((row) =>
			Object.values(row)
				.map((value) => `"${value.toString().replace(/"/g, '""')}"`)
				.join(',')
		)
	];

	return csvRows.join('\n');
}

function getProgressText(audit: AuditListDTO): string {
	if (audit.progress === -1) {
		return t.get('audits.csvExport.progress.late');
	} else if (audit.progress === 100 && audit.completionDate) {
		return t.get('audits.csvExport.progress.closed');
	} else if (audit.progress === 100) {
		return t.get('audits.csvExport.progress.completed');
	} else if (audit.progress > 0) {
		return `${t.get('audits.csvExport.progress.inProgress')} (${audit.progress}%)`;
	} else {
		return t.get('audits.csvExport.progress.planned');
	}
}

export function downloadCSV(data: string, filename: string) {
	const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
	const link = document.createElement('a');
	const url = URL.createObjectURL(blob);
	link.setAttribute('href', url);
	link.setAttribute('download', filename);
	link.style.visibility = 'hidden';
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}
