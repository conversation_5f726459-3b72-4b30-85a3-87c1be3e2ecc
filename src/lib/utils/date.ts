import { format, parseISO } from 'date-fns';
import { cs, enUS } from 'date-fns/locale';

/**
 * Formats a date string to a localized date format using date-fns
 * @param dateString Date string to format
 * @param locale Current locale ('cs' or 'en')
 * @param formatStyle Date format style ('full', 'long', 'medium', 'short')
 * @returns Formatted date string
 */
export function formatDate(
	dateString: string | null | undefined,
	locale: string,
	formatStyle: 'full' | 'long' | 'medium' | 'short' = 'medium'
): string {
	if (!dateString) return 'N/A';

	try {
		const date = parseISO(dateString);
		const localeObj = locale === 'cs' ? cs : enUS;

		let formatString: string;
		switch (formatStyle) {
			case 'full':
				formatString = 'PPPP'; // "Monday, April 29th, 2023"
				break;
			case 'long':
				formatString = 'PPP'; // "April 29th, 2023"
				break;
			case 'medium':
				formatString = 'PP'; // "Apr 29, 2023"
				break;
			case 'short':
				formatString = 'P'; // "04/29/2023"
				break;
		}

		return format(date, formatString, { locale: localeObj });
	} catch {
		return dateString || 'N/A';
	}
}
