/**
 * Debounce function that delays execution until after a specified delay
 * @param func - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: never[]) => unknown>(
	func: T,
	delay: number
): (...args: Parameters<T>) => void {
	let timeoutId: ReturnType<typeof setTimeout>;

	return (...args: Parameters<T>) => {
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => func(...args), delay);
	};
}

/**
 * Debounce function that can be cancelled
 * @param func - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Object with debounced function and cancel method
 */
export function debounceCancellable<T extends (...args: never[]) => unknown>(
	func: T,
	delay: number
): {
	debounced: (...args: Parameters<T>) => void;
	cancel: () => void;
} {
	let timeoutId: ReturnType<typeof setTimeout>;

	const debounced = (...args: Parameters<T>) => {
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => func(...args), delay);
	};

	const cancel = () => {
		clearTimeout(timeoutId);
	};

	return { debounced, cancel };
}
