import { t } from '$lib/translations';

export function getProgressInfo(
	progress: number,
	completionDate: string | null | undefined,
	successRate?: number
) {
	let text: string;
	let variant: 'default' | 'filled' | 'warning' | 'success' | 'destructive' = 'default';

	if (progress === -1) {
		// Late
		text = t.get('audits.progress.late');
		variant = 'destructive';
	} else if (completionDate && successRate !== undefined) {
		// Closed (has completionDate and successRate)
		text = `${t.get('audits.progress.closed')} (${successRate}%)`;
		variant = 'success';
	} else if (progress === 100) {
		// Completed (100% progress but no completionDate)
		text = t.get('audits.progress.completed');
		variant = 'filled';
	} else if (progress > 0) {
		// In progress - show only text, no percentage
		text = t.get('audits.progress.inProgress');
		variant = 'default';
	} else {
		text = t.get('audits.progress.planned');
		variant = 'warning';
	}

	return { text, variant };
}
