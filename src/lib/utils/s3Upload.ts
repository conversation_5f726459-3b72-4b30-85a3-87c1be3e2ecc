import type { AttachmentFile } from '$lib/DTO/audits/auditEvaluation';

export interface PresignedUploadResponse {
	uploadUrl: string;
	s3Key: string;
	fileName: string;
	expiresIn: number;
}

export interface UploadProgress {
	loaded: number;
	total: number;
	percentage: number;
}

export interface UploadResult {
	success: boolean;
	s3Key?: string;
	fileName?: string;
	error?: string;
}

/**
 * Get presigned URL for file upload
 */
export async function getPresignedUploadUrl(
	file: File,
	companySlug: string,
	plantSlug?: string,
	context: string = 'audits_attachments'
): Promise<PresignedUploadResponse> {
	const response = await fetch('/api/upload/presigned', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			fileName: file.name,
			fileSize: file.size,
			contentType: file.type,
			companySlug,
			plantSlug,
			context
		})
	});

	if (!response.ok) {
		const error = await response.text();
		throw new Error(`Failed to get upload URL: ${error}`);
	}

	return response.json();
}

/**
 * Upload file directly to S3 using presigned URL
 */
export async function uploadFileToS3(
	file: File,
	uploadUrl: string,
	onProgress?: (progress: UploadProgress) => void
): Promise<void> {
	return new Promise((resolve, reject) => {
		const xhr = new XMLHttpRequest();

		// Track upload progress
		if (onProgress) {
			xhr.upload.addEventListener('progress', (event) => {
				if (event.lengthComputable) {
					const progress: UploadProgress = {
						loaded: event.loaded,
						total: event.total,
						percentage: Math.round((event.loaded / event.total) * 100)
					};
					onProgress(progress);
				}
			});
		}

		xhr.addEventListener('load', () => {
			if (xhr.status === 200) {
				resolve();
			} else {
				reject(new Error(`Upload failed with status: ${xhr.status}`));
			}
		});

		xhr.addEventListener('error', (event) => {
			console.error('XHR Error event:', event);
			console.error('XHR status:', xhr.status);
			console.error('XHR statusText:', xhr.statusText);
			console.error('XHR responseText:', xhr.responseText);
			reject(new Error(`Upload failed due to network error. Status: ${xhr.status}`));
		});

		xhr.addEventListener('abort', () => {
			reject(new Error('Upload was aborted'));
		});

		xhr.open('PUT', uploadUrl);
		xhr.setRequestHeader('Content-Type', file.type);
		xhr.send(file);
	});
}

/**
 * Complete upload flow: get presigned URL + upload to S3
 */
export async function uploadFileComplete(
	file: File,
	companySlug: string,
	plantSlug: string,
	context: string = 'audits_attachments',
	onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
	try {
		// Step 1: Get presigned URL
		const presignedData = await getPresignedUploadUrl(file, companySlug, plantSlug, context);

		// Step 2: Upload to S3
		await uploadFileToS3(file, presignedData.uploadUrl, onProgress);

		// Step 3: Return success result
		return {
			success: true,
			s3Key: presignedData.s3Key,
			fileName: presignedData.fileName
		};
	} catch (error) {
		console.error('Upload failed:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown upload error'
		};
	}
}

/**
 * Complete upload flow with auto-saver tracking
 */
export async function uploadFileCompleteWithTracking(
	file: File,
	companySlug: string,
	plantSlug: string,
	context: string = 'audits_attachments',
	onProgress?: (progress: UploadProgress) => void,
	trackUpload?: (uploadId: string) => void,
	untrackUpload?: (uploadId: string) => void
): Promise<UploadResult> {
	const uploadId = globalThis.crypto.randomUUID();

	// Track upload if callback provided
	trackUpload?.(uploadId);

	try {
		const result = await uploadFileComplete(file, companySlug, plantSlug, context, onProgress);
		return result;
	} finally {
		// Always untrack upload
		untrackUpload?.(uploadId);
	}
}

/**
 * Convert upload result to AttachmentFile format
 */
export function uploadResultToAttachment(
	uploadResult: UploadResult,
	originalFile: File
): AttachmentFile | null {
	if (!uploadResult.success || !uploadResult.s3Key) {
		return null;
	}

	return {
		url: uploadResult.s3Key, // S3 key for backend processing
		filename: originalFile.name,
		type: originalFile.type,
		// Add preview URL for immediate display (will be cleaned up after submit)
		previewUrl: URL.createObjectURL(originalFile)
	};
}

/**
 * Create object URL for file preview (for files being uploaded)
 */
export function createFilePreview(file: File): string {
	return URL.createObjectURL(file);
}

/**
 * Cleanup object URL when no longer needed
 */
export function revokeFilePreview(url: string): void {
	URL.revokeObjectURL(url);
}
