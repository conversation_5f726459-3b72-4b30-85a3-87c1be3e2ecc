/**
 * Converts database slug to URL slug (lowercase)
 */
export function toUrlSlug(dbSlug: string): string {
	return dbSlug.toLowerCase();
}

/**
 * Converts URL slug to database slug (uppercase for company codes)
 */
export function fromUrlSlug(urlSlug: string, type: 'company' | 'plant'): string {
	if (type === 'company') {
		// Company codes are always uppercase
		return urlSlug.toUpperCase();
	}
	// Plants keep their original case (mixed case)
	// We'll need to look them up in database by case-insensitive search
	return urlSlug;
}

/**
 * Normalizes slug for database comparison (case-insensitive)
 */
export function normalizeSlugForSearch(slug: string): string {
	return slug.toLowerCase();
}
