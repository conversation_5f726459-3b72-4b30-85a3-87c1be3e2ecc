import { t } from '$lib/translations';
import { formatDuration as formatDurationFns, intervalToDuration } from 'date-fns';
import { cs, enUS } from 'date-fns/locale';

/**
 * Formats minutes into HH:MM format
 * @param minutes Total minutes
 * @returns Formatted string in HH:MM format
 */
export function minutesToHHMM(minutes: number): string {
	const hours = Math.floor(minutes / 60);
	const mins = minutes % 60;
	return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

/**
 * Formats seconds into HH:MM:SS format
 * @param seconds Total seconds
 * @returns Formatted string in HH:MM:SS format
 */
export function secondsToHHMMSS(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = seconds % 60;
	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Formats seconds into a localized duration string using date-fns
 * @param seconds Total seconds
 * @param locale Locale string ('cs' or 'en')
 * @returns Formatted duration string
 */
export function formatSecondsDuration(seconds: number, locale: string = 'cs'): string {
	if (seconds === 0) return `0 ${t.get('common.duration.minutes.many')}`;

	const duration = intervalToDuration({
		start: 0,
		end: seconds * 1000
	});

	return formatDurationFns(duration, {
		format: ['hours', 'minutes', 'seconds'],
		locale: locale === 'cs' ? cs : enUS,
		zero: false,
		delimiter: ' '
	});
}

/**
 * Formats minutes into a localized duration string using date-fns
 * @param minutes Total minutes
 * @param locale Locale string ('cs' or 'en')
 * @returns Formatted duration string
 */
export function formatMinutesDuration(minutes: number, locale: string = 'cs'): string {
	if (minutes === 0) return `0 ${t.get('common.duration.minutes.many')}`;

	const duration = intervalToDuration({
		start: 0,
		end: minutes * 60 * 1000
	});

	return formatDurationFns(duration, {
		format: ['hours', 'minutes'],
		locale: locale === 'cs' ? cs : enUS,
		zero: false,
		delimiter: ' '
	});
}

/**
 * Formats HH:MM string into a human-readable duration
 * @param duration Duration in HH:MM format
 * @returns Formatted duration string
 */
export function formatDuration(duration: string): string {
	const [hours, minutes] = duration.split(':').map(Number);

	if (hours === 0 && minutes === 0) return `0 ${t.get('common.duration.minutes.many')}`;

	const parts = [];

	if (hours > 0) {
		const hoursKey = hours === 1 ? 'one' : hours >= 2 && hours <= 4 ? 'few' : 'many';
		parts.push(`${hours} ${t.get(`common.duration.hours.${hoursKey}`)}`);
	}

	if (minutes > 0) {
		const minutesKey = minutes === 1 ? 'one' : minutes >= 2 && minutes <= 4 ? 'few' : 'many';
		parts.push(`${minutes} ${t.get(`common.duration.minutes.${minutesKey}`)}`);
	}

	return parts.join(' ');
}

/**
 * Converts HH:MM format to total minutes
 * @param timeString Time in HH:MM format
 * @returns Total minutes
 */
export function hhmmToMinutes(timeString: string): number {
	if (!timeString) return 0;

	try {
		if (!timeString.includes(':')) {
			const minutes = parseInt(timeString, 10);
			if (!isNaN(minutes)) return minutes;
			return 0;
		}

		const [hours, minutes] = timeString.split(':').map((part) => {
			const num = parseInt(part, 10);
			return isNaN(num) ? 0 : num;
		});

		return hours * 60 + minutes;
	} catch (error) {
		console.error('Error converting time to minutes:', error);
		return 0;
	}
}

/**
 * Converts HH:MM:SS format to total seconds
 * @param timeString Time in HH:MM:SS format
 * @returns Total seconds
 */
export function hhmmssToSeconds(timeString: string): number {
	if (!timeString) return 0;

	try {
		const parts = timeString.split(':').map((part) => parseInt(part, 10) || 0);

		if (parts.length === 3) {
			// HH:MM:SS format
			return parts[0] * 3600 + parts[1] * 60 + parts[2];
		} else if (parts.length === 2) {
			// HH:MM format (assume 0 seconds)
			return parts[0] * 3600 + parts[1] * 60;
		} else if (parts.length === 1) {
			// Just seconds
			return parts[0];
		}

		return 0;
	} catch (error) {
		console.error('Error converting time to seconds:', error);
		return 0;
	}
}

/**
 * Formats and validates time input in HH:MM format
 * @param value Current input value
 * @param isBlur Whether the function is called on blur event
 * @returns Formatted time string
 */
export function formatTimeInput(value: string, isBlur: boolean = false): string {
	let formatted = value.replace(/[^\d:]/g, '');

	if (formatted.includes(':')) {
		const [hours, minutes] = formatted.split(':');

		let hoursNum = parseInt(hours, 10);
		if (!isNaN(hoursNum) && hoursNum > 23) hoursNum = 23;

		let minutesNum = parseInt(minutes, 10);
		if (!isNaN(minutesNum) && minutesNum > 59) minutesNum = 59;

		if (!isNaN(hoursNum) && !isNaN(minutesNum)) {
			formatted = `${hoursNum}:${minutesNum < 10 ? '0' + minutesNum : minutesNum}`;
		}
	} else if (formatted.length > 2) {
		formatted = formatted.slice(0, 2) + ':' + formatted.slice(2);
	}

	if (isBlur) {
		if (formatted && !formatted.includes(':')) {
			let hoursNum = parseInt(formatted, 10);
			if (!isNaN(hoursNum)) {
				if (hoursNum > 23) hoursNum = 23;
				formatted = `${hoursNum}:00`;
			}
		} else if (formatted && formatted.includes(':')) {
			const [hours, minutes] = formatted.split(':');
			let hoursNum = parseInt(hours, 10);
			let minutesNum = parseInt(minutes, 10);

			if (!isNaN(hoursNum) && !isNaN(minutesNum)) {
				if (hoursNum > 23) hoursNum = 23;
				if (minutesNum > 59) minutesNum = 59;
				formatted = `${hoursNum.toString().padStart(2, '0')}:${minutesNum.toString().padStart(2, '0')}`;
			}
		}
	}

	return formatted;
}
