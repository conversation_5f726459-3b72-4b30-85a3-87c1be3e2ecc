<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import { Languages } from '@lucide/svelte';
	import { locale, locales, loadTranslations, t } from '$lib/translations';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';

	let { children } = $props();

	const switchLanguage = async (newLocale: string) => {
		if (browser) {
			// Set cookie for server-side detection
			document.cookie = `language=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}`;
			// Keep localStorage for backward compatibility
			localStorage.setItem('language', newLocale);
			await goto(page.url.pathname, {
				invalidateAll: true,
				replaceState: true
			});
		}
	};
</script>

<div
	class="relative flex min-h-screen w-full items-center justify-center overflow-hidden bg-[#EAEDF5]"
>
	<!-- Decorative elements - responsive sizes -->
	<div
		class="absolute top-0 left-0 h-32 w-32 rounded-full bg-blue-200/20 blur-3xl sm:h-48 sm:w-48 lg:h-64 lg:w-64"
	></div>
	<div
		class="absolute right-0 bottom-0 h-48 w-48 rounded-full bg-purple-200/20 blur-3xl sm:h-72 sm:w-72 lg:h-96 lg:w-96"
	></div>

	<!-- Language Selector -->
	<div class="absolute top-3 right-3 z-10 sm:top-4 sm:right-4">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				<Button
					variant="ghost"
					class="size-14 rounded-full bg-white/90 shadow-md transition-all duration-200 hover:bg-white/100"
				>
					<Languages class="size-6 text-[#B1B7C3]" />
				</Button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content align="end" class="min-w-[120px]">
				{#each $locales as lang}
					<DropdownMenu.Item
						class="cursor-pointer py-2 text-sm sm:text-base"
						onclick={() => switchLanguage(lang)}
					>
						<span class:font-bold={$locale === lang}>
							{lang === 'cs' ? $t('common.languages.cs') : $t('common.languages.en')}
						</span>
						{#if $locale === lang}
							<DropdownMenu.Shortcut>✓</DropdownMenu.Shortcut>
						{/if}
					</DropdownMenu.Item>
				{/each}
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>

	<!-- Content -->
	<div class="relative w-full px-4 sm:px-6 lg:px-8">
		{@render children()}
	</div>
</div>
