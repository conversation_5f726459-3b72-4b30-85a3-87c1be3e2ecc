import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { forgotPasswordSchema } from '$lib/schemas/auth';
import { AuthService } from '$lib/server/services/auth';

export const load = async () => {
	const form = await superValidate(zod(forgotPasswordSchema));
	return { form };
};

export const actions = {
	default: async ({ request, url }) => {
		const form = await superValidate(request, zod(forgotPasswordSchema));

		if (!form.valid) {
			return fail(400, { form });
		}
		const currentLocale = form.data.locale || 'en';
		await AuthService.createPasswordReset(form.data.email, currentLocale, url.origin);

		return {
			form,
			success: true
		};
	}
};
