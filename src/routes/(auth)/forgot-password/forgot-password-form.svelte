<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import { forgotPasswordSchema, type ForgotPasswordForm } from '$lib/schemas/auth';
	import * as Form from '$lib/components/ui/form/index';
	import { elasticOut, quintOut } from 'svelte/easing';
	import { fade, fly } from 'svelte/transition';
	import { t, locale } from '$lib/translations';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Loader2 } from '@lucide/svelte';

	let { data }: { data: { form: SuperValidated<ForgotPasswordForm> } } = $props();
	let success = $state(false);
	let isSubmitting = $state(false);

	const form = superForm(data.form, {
		validators: zodClient(forgotPasswordSchema),
		validationMethod: 'oninput',
		taintedMessage: null,
		resetForm: true,
		onResult: ({ result }) => {
			if (result.type === 'success' && result.data?.success) {
				success = true;
			}
			isSubmitting = false;
		},
		onSubmit: () => {
			isSubmitting = true;
		}
	});

	const { form: formData, enhance, errors } = form;
</script>

<form
	method="POST"
	use:enhance
	in:fade={{ duration: 400, easing: quintOut }}
	class="flex flex-col gap-6"
>
	<input type="hidden" name="locale" value={$locale} />

	{#if success}
		<div class="rounded-md bg-green-50 p-4" in:fly={{ y: -20, duration: 400, easing: elasticOut }}>
			<div class="flex">
				<div class="ml-3">
					<h3 class="text-sm font-medium text-green-800">
						{$t('auth.forgotPassword.alertEmailSent')}
					</h3>
				</div>
			</div>
		</div>
	{/if}

	<div class="flex flex-col gap-4">
		<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">Email</Form.Label>
						<Input
							{...props}
							type="email"
							bind:value={$formData.email}
							class={`h-10 bg-white ${$errors.email ? 'border-red-500' : ''}`}
							disabled={isSubmitting}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<div in:fly={{ y: 20, duration: 300, delay: 200 }}>
		<Form.Button type="submit" class="h-10 w-full" disabled={isSubmitting}>
			{#if isSubmitting}
				<Loader2 class="mr-2 h-4 w-4 animate-spin" />
			{:else}
				{$t('auth.forgotPassword.send')}
			{/if}
		</Form.Button>
	</div>
</form>
