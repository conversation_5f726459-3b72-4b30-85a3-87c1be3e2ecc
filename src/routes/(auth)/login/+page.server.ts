import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { loginSchema } from '$lib/schemas/auth';
import { AuthService } from '$lib/server/services/auth';
import { generateSessionToken } from '$lib/server/services/auth/authAPI';
import { setSessionCookies } from '$lib/utils/session';
import { CompanyService, PlantsService } from '$lib/server/services/tenants/index.js';

export const load = async () => {
	const form = await superValidate(zod(loginSchema));
	return { form };
};

export const actions = {
	default: async ({ request, cookies }) => {
		const form = await superValidate(request, zod(loginSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const result = await AuthService.login(form.data);

		if (!result) {
			return fail(400, {
				form,
				status: 'error',
				message: 'Invalid email or password'
			});
		}

		if (typeof result === 'object' && result.error === 'accDeactivated') {
			return fail(403, {
				form,
				status: 'deactivated'
			});
		}

		if (typeof result === 'object' && result.error === 'noPlantRole') {
			return fail(403, {
				form,
				status: 'noPlantRole'
			});
		}

		if (typeof result === 'string') {
			const token = generateSessionToken();
			const session = await AuthService.createSession(token, result);
			setSessionCookies(cookies, token, session.expiresAt);

			// Get main plant slug
			const mainPlantSlug = await PlantsService.getUserMainPlantSlug(result);
			const companySlug = await CompanyService.getUserCompanySlug(result);

			if (mainPlantSlug) {
				return {
					status: 'success',
					companySlug,
					plantSlug: mainPlantSlug,
					form
				};
			} else {
				return fail(400, {
					form,
					status: 'error',
					message: 'No plant access found for this user'
				});
			}
		}
	}
};
