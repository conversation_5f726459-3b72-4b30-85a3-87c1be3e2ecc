<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { fade } from 'svelte/transition';
	import type { PageData } from './$types';
	import LoginForm from './login-form.svelte';
	import { quintOut } from 'svelte/easing';
	import { t } from '$lib/translations';

	let { data }: { data: PageData } = $props();
</script>

<svelte:head>
	<title>LeanAudit - {$t('auth.login.title')}</title>
</svelte:head>

<div
	class="flex min-h-screen items-center justify-center p-4 sm:p-6"
	in:fade={{ duration: 300, delay: 200, easing: quintOut }}
>
	<div
		class="mx-auto w-full max-w-md overflow-hidden rounded-xl bg-white/90 shadow-lg backdrop-blur-sm sm:rounded-2xl sm:shadow-xl lg:max-w-lg"
	>
		<div class="px-4 pt-4 sm:px-6 sm:pt-6 lg:px-10 lg:pt-10">
			<div class="flex items-center gap-2">
				<!-- Logo? -->
				<div class="text-xl font-extrabold tracking-tight sm:text-2xl">
					{$t('auth.login.welcomeBack')}
				</div>
			</div>
			<p class="text-muted-foreground mt-2 text-sm sm:text-base">{$t('auth.login.title')}</p>
		</div>

		<div class="px-4 py-4 sm:px-6 sm:py-6 lg:px-10 lg:py-8">
			<LoginForm {data} />
		</div>

		<div class="border-t bg-slate-50/50 px-4 py-3 sm:px-6 sm:py-4 lg:px-10">
			<Button variant="ghost" class="h-12 w-full text-sm " href="/register"
				>{$t('auth.login.dontHaveAccount')}</Button
			>
		</div>
	</div>
</div>
