<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import { loginSchema, type LoginForm } from '$lib/schemas/auth';
	import * as Form from '$lib/components/ui/form/index';
	import { elasticOut, quintOut } from 'svelte/easing';
	import { fade, fly, scale } from 'svelte/transition';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { Loader2, OctagonX, TriangleAlert } from '@lucide/svelte';
	import { is } from 'drizzle-orm';

	let { data }: { data: { form: SuperValidated<LoginForm> } } = $props();

	let errorMessage = $state('');
	let errorType = $state<'error' | 'warning'>('error');
	let isLoggingIn = $state(false);

	const form = superForm(data.form, {
		validators: zodClient(loginSchema),
		taintedMessage: null,
		resetForm: false,
		onSubmit: () => {
			isLoggingIn = true;
		},
		onResult: ({ result }) => {
			if (result.type === 'failure') {
				$formData.password = '';
				if (result.data?.status === 'deactivated') {
					errorMessage = $t('auth.login.errors.accountDeactivated');
					errorType = 'warning';
				} else if (result.data?.status === 'noPlantRole') {
					errorMessage = $t('auth.login.errors.noPlantRole');
					errorType = 'warning';
				} else {
					errorMessage = $t('auth.login.errors.invalidCredentials');
					errorType = 'error';
				}
			} else if (result.type === 'success') {
				errorMessage = '';
				errorType = 'error';
				toast.success($t('auth.login.success'));
				if (result.data?.companySlug && result.data?.plantSlug) {
					goto(`/${result.data.companySlug}/${result.data.plantSlug}/dashboard`);
				}
			}
			isLoggingIn = false;
		},
		applyAction: true
	});

	const { form: formData, enhance, errors } = form;
</script>

<form
	method="POST"
	use:enhance
	in:fade={{ duration: 400, easing: quintOut }}
	class="flex flex-col gap-4 sm:gap-6"
>
	{#if errorMessage}
		<div
			class={`rounded-lg p-3 sm:p-4 ${errorType === 'warning' ? 'bg-yellow-50' : 'bg-red-50'}`}
			in:fly={{ y: -20, duration: 400, easing: elasticOut }}
			out:scale={{ duration: 200 }}
		>
			<div class="flex">
				<div class="ml-3 flex flex-row items-center justify-center gap-4 whitespace-pre-line">
					{#if errorType === 'warning'}
						<TriangleAlert class="size-4 flex-shrink-0 text-yellow-900/50 sm:size-5" />
					{:else if errorType === 'error'}
						<OctagonX class="size-4 flex-shrink-0 text-red-800/50 sm:size-5" />
					{/if}
					<h3
						class={`text-sm leading-tight font-medium sm:text-base ${errorType === 'warning' ? 'text-yellow-900' : 'text-red-800'}`}
					>
						{errorMessage}
					</h3>
				</div>
			</div>
		</div>
	{/if}

	<div class="flex flex-col gap-4 sm:gap-5">
		<div class="min-h-[80px] sm:min-h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="mb-2 block text-sm font-medium sm:text-base"
							>{$t('auth.login.email')}</Form.Label
						>
						<Input
							{...props}
							type="email"
							bind:value={$formData.email}
							class={`h-12 rounded-lg bg-white px-4 text-base sm:h-11 sm:text-sm ${$errors.email ? 'border-red-500' : 'border-gray-300'}`}
							placeholder="<EMAIL>"
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600 sm:text-sm" />
			</Form.Field>
		</div>

		<div class="min-h-[80px] sm:min-h-[85px]" in:fly={{ y: 20, duration: 300, delay: 200 }}>
			<Form.Field {form} name="password">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="mb-2 block text-sm font-medium sm:text-base"
							>{$t('auth.login.password')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.password}
							class={`h-12 rounded-lg bg-white px-4 text-base sm:h-11 sm:text-sm ${$errors.password ? 'border-red-500' : 'border-gray-300'}`}
							placeholder="••••••••"
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600 sm:text-sm" />
			</Form.Field>
		</div>
	</div>

	<div class="text-right">
		<a href="/forgot-password" class="text-sm text-blue-600 hover:underline active:text-blue-800"
			>{$t('auth.login.forgotPassword')}</a
		>
	</div>

	<div in:fly={{ y: 20, duration: 300, delay: 250 }}>
		<Form.Button
			type="submit"
			class="flex h-12 w-full items-center justify-center gap-2 rounded-lg text-base font-medium sm:h-11 sm:text-sm"
			disabled={isLoggingIn}
		>
			{#if isLoggingIn}
				<Loader2 class="size-4 animate-spin" />
			{:else}
				{$t('auth.login.login')}
			{/if}
		</Form.Button>
	</div>
</form>
