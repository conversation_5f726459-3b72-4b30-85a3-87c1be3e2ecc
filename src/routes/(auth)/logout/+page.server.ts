import { invalidateSession } from '$lib/server/services/auth/authAPI';
import { deleteSessionCookies } from '$lib/utils/session';
import { fail, redirect, type Actions } from '@sveltejs/kit';

export const actions: Actions = {
	default: async ({ locals, cookies }) => {
		if (locals.session === null) {
			return fail(401, { message: 'You are not logged in' });
		}
		invalidateSession(locals.session.id);
		deleteSessionCookies(cookies);
		return redirect(302, '/login');
	}
};
