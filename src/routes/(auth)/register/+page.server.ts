import { AuthService } from '$lib/server/services/auth';
import { fail, superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { registrationSchema } from '$lib/schemas/auth';
import { redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types.js';

export const load: PageServerLoad = async () => {
	return {
		form: await superValidate(zod(registrationSchema))
	};
};

export const actions: Actions = {
	register: async (request) => {
		const form = await superValidate(request, zod(registrationSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}
		const result = await AuthService.register(form.data);

		return result && result.token ? redirect(302, '/login') : fail(400, { form });
	}
};
