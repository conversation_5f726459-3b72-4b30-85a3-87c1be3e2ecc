<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { fade } from 'svelte/transition';
	import type { PageData } from './$types';
	import RegisterForm from './register-form.svelte';
	import { quintOut } from 'svelte/easing';
	import { t } from '$lib/translations';

	let { data }: { data: PageData } = $props();
</script>

<svelte:head>
	<title>LeanAudit - {$t('auth.register.title')}</title>
</svelte:head>

<div
	class="flex min-h-screen items-center justify-center p-4 sm:p-6"
	in:fade={{ duration: 300, delay: 200, easing: quintOut }}
>
	<div
		class="w-full max-w-md overflow-hidden rounded-2xl bg-white/80 shadow-xl backdrop-blur-sm lg:max-w-2xl"
	>
		<div class="px-6 pt-6 sm:px-10 sm:pt-10">
			<div class="flex items-center gap-2">
				<!-- Logo? -->
				<div class="text-2xl font-extrabold tracking-tight">{$t('auth.register.title')}</div>
			</div>
			<p class="text-muted-foreground mt-2 text-sm">{$t('auth.register.createAccount')}</p>
		</div>

		<div class="px-6 py-6 sm:px-10 sm:py-8">
			<RegisterForm {data} />
		</div>

		<div class="border-t bg-slate-50/50 px-6 py-4 sm:px-10">
			<Button variant="ghost" class="w-full" href="/login"
				>{$t('auth.register.alreadyHaveAccount')}</Button
			>
		</div>
	</div>
</div>
