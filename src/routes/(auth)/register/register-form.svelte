<script lang="ts">
	import { registrationSchema, type RegistrationForm } from '$lib/schemas/auth';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index';
	import { fly } from 'svelte/transition';
	import { t } from '$lib/translations';

	let { data }: { data: { form: SuperValidated<RegistrationForm> } } = $props();

	const form = superForm(data.form, {
		validators: zodClient(registrationSchema),
		validationMethod: 'oninput'
	});

	const { form: formData, enhance, errors } = form;
</script>

<form method="POST" use:enhance action="?/register" class="flex flex-col gap-6">
	<!-- Name row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="firstName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.firstName')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.firstName}
							class={`h-10 bg-white ${$errors.firstName ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="lastName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.lastName')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.lastName}
							class={`h-10 bg-white ${$errors.lastName ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Contact row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.email')}</Form.Label>
						<Input
							{...props}
							type="email"
							bind:value={$formData.email}
							class={`h-10 bg-white ${$errors.email ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="phone">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.phone')}</Form.Label>
						<Input
							{...props}
							type="tel"
							bind:value={$formData.phone}
							class={`h-10 bg-white ${$errors.phone ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Password column -->
	<div class="flex flex-col gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="password">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.password')}</Form.Label>
						<Input
							{...props}
							type="password"
							bind:value={$formData.password}
							class={`h-10 bg-white ${$errors.password ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="passwordConfirm">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.register.confirmPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.passwordConfirm}
							class={`h-10 bg-white ${$errors.passwordConfirm ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<div in:fly={{ y: 20, duration: 300, delay: 200 }}>
		<Form.Button type="submit" class="mt-2 h-10 w-full">{$t('auth.register.register')}</Form.Button>
	</div>
</form>
