import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { resetPasswordSchema } from '$lib/schemas/auth';
import { AuthService } from '$lib/server/services/auth';

export const load = async () => {
  const form = await superValidate(zod(resetPasswordSchema));
  return { form };
};

export const actions = {
  default: async ({ request, params }) => {
    const form = await superValidate(request, zod(resetPasswordSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    const success = await AuthService.resetPassword(params.token, form.data.password);

    if (!success) {
      return fail(400, {
        form,
        message: 'Neplatný nebo expirovaný token pro reset hesla.'
      });
    }

    return redirect(302, '/login?reset=success');
  }
};