<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { fade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import ResetPasswordForm from './reset-password-form.svelte';
	import { t } from '$lib/translations';

	let { data } = $props();
</script>

<svelte:head>
	<title>LeanAudit - {$t('auth.resetPassword.title')}</title>
</svelte:head>

<div
	class="flex min-h-screen items-center justify-center p-4 sm:p-6"
	in:fade={{ duration: 300, delay: 200, easing: quintOut }}
>
	<div
		class="w-full max-w-md overflow-hidden rounded-2xl bg-white/80 shadow-xl backdrop-blur-sm lg:max-w-lg"
	>
		<div class="px-6 pt-6 sm:px-10 sm:pt-10">
			<div class="flex items-center gap-2">
				<div class="text-2xl font-extrabold tracking-tight">{$t('auth.resetPassword.title')}</div>
			</div>
			<p class="text-muted-foreground mt-2 text-sm">{$t('auth.resetPassword.newPassword')}</p>
		</div>

		<div class="px-6 py-6 sm:px-10 sm:py-8">
			<ResetPasswordForm {data} />
		</div>
	</div>
</div>
