<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import { resetPasswordSchema, type ResetPasswordForm } from '$lib/schemas/auth';
	import * as Form from '$lib/components/ui/form/index';
	import { elasticOut, quintOut } from 'svelte/easing';
	import { fade, fly } from 'svelte/transition';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { t } from '$lib/translations';

	let { data }: { data: { form: SuperValidated<ResetPasswordForm>; message?: string } } = $props();

	const form = superForm(data.form, {
		validators: zodClient(resetPasswordSchema),
		validationMethod: 'oninput',
		taintedMessage: null
	});

	const { form: formData, enhance, errors } = form;
</script>

<form
	method="POST"
	use:enhance
	in:fade={{ duration: 400, easing: quintOut }}
	class="flex flex-col gap-6"
>
	{#if data.message}
		<div class="rounded-md bg-red-50 p-4" in:fly={{ y: -20, duration: 400, easing: elasticOut }}>
			<div class="flex">
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">
						{data.message}
					</h3>
				</div>
			</div>
		</div>
	{/if}

	<div class="flex flex-col gap-4">
		<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="password">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.resetPassword.newPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.password}
							class={`h-10 bg-white ${$errors.password ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="passwordConfirm">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.resetPassword.confirmNewPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.passwordConfirm}
							class={`h-10 bg-white ${$errors.passwordConfirm ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<div in:fly={{ y: 20, duration: 300, delay: 200 }}>
		<Form.Button type="submit" class="h-10 w-full"
			>{$t('auth.resetPassword.setNewPassword')}</Form.Button
		>
	</div>
</form>
