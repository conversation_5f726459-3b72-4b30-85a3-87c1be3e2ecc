export const load = async ({ cookies, request }) => {
	let language = cookies.get('language');

	if (!language) {
		language = cookies.get('localStorage-language');
	}

	if (!language) {
		const acceptLanguage = request.headers.get('accept-language');
		language = `${acceptLanguage?.match(/[a-zA-Z]+?(?=-|_|,|;)/)}`.toLowerCase();
	}

	if (!['cs', 'en'].includes(language)) {
		language = 'en';
	}

	return { language };
};
