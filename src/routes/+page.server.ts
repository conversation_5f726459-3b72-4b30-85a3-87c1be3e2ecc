import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { CompanyService, PlantsService } from '$lib/server/services/tenants';

export const load: PageServerLoad = async ({ locals }) => {
	console.log('🟡 ROOT PAGE - User:', locals.user ? 'logged in' : 'not logged in');

	if (!locals.user || locals.user.plants.length === 0) {
		console.log('🔴 ROOT PAGE - No user/plants, redirecting to login');
		throw redirect(302, '/login');
	}

	const [company, mainPlant] = await Promise.all([
		CompanyService.getCompanyById(locals.user.companyId),
		PlantsService.getPlantById(locals.user.plants[0])
	]);

	console.log('🟢 Company slug:', company?.slug);
	console.log('🟢 MainPlant slug:', mainPlant?.slug);
	console.log('🟢 Redirecting to:', `/${company?.slug}/${mainPlant?.slug}/dashboard`);

	if (company && mainPlant) {
		const redirectUrl = `/${company.slug}/${mainPlant.slug}/dashboard`;
		console.log('🟢 ROOT PAGE - Redirecting to:', redirectUrl);
		return redirect(302, redirectUrl);
	}

	console.log('🔴 ROOT PAGE - No company/plant found, redirecting to login');
	throw redirect(302, '/login');
};
