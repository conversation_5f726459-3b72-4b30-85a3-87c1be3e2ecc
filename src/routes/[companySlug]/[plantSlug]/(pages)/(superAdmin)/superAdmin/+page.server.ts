import { createUserSchema, updateUserSchema } from '$lib/schemas/admin.js';
import { AuthService } from '$lib/server/services/auth';
import {
	SuperAdminCompanyService,
	SuperAdminPlantService,
	SuperAdminService
} from '$lib/server/services/superadmin';
import { CompanyService, PlantsService } from '$lib/server/services/tenants';
import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { createAdminBreadcrumbs } from './_utils/createBreadcrumbs';
import { companySchema, plantSchema } from '$lib/schemas/company';

export const load = async ({ locals, params }) => {
	if (!locals.user) {
		throw redirect(307, `/${params.companySlug}/${params.plantSlug}/dashboard`);
	}
	//TEMPORARY

	const [companies, plants, users, newUserForm, editUserForm, companyForm, plantForm] =
		await Promise.all([
			SuperAdminCompanyService.getAllCompanies(),
			SuperAdminService.getAllPlants(),
			SuperAdminService.getAllUsers(),
			superValidate(zod(createUserSchema)),
			superValidate(zod(updateUserSchema)),
			superValidate(zod(companySchema)),
			superValidate(zod(plantSchema))
		]);

	return {
		companies,
		plants,
		users,
		newUserForm,
		editUserForm,
		companyForm,
		plantForm,
		breadcrumbs: createAdminBreadcrumbs()
	};
};

export const actions = {
	createUser: async (request) => {
		const form = await superValidate(request, zod(createUserSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await AuthService.register(form.data);

			if (result && result.token) {
				if (form.data.plantId) {
					await SuperAdminService.assignUserToPlantAndCompany(
						result.user.id,
						form.data.plantId,
						form.data.companyId!,
						form.data.cardNumber
					);
				}

				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error creating user:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	},

	updateUser: async ({ request, locals }) => {
		const form = await superValidate(request, zod(updateUserSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminService.updateUser(form.data);

			if (result) {
				// Check if user is updating themselves
				if (locals.user && locals.user.id === form.data.id) {
					//Update session
					const [company, plant] = await Promise.all([
						CompanyService.getCompanyById(form.data.companyId!),
						PlantsService.getPlantById(form.data.plantId!)
					]);

					if (company && plant) {
						return {
							form,
							success: true,
							redirectTo: `/${company.slug}/${plant.slug}/dashboard`
						};
					}
				}

				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error updating user:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	},

	deleteUser: async ({ request }) => {
		const formData = await request.formData();
		const userId = formData.get('id') as string;

		if (!userId) {
			return fail(400, {
				error: 'User ID is required'
			});
		}

		try {
			const result = await SuperAdminService.deleteUser(userId);

			if (result.success) {
				return {
					success: true
				};
			} else {
				return fail(400, {
					success: false
				});
			}
		} catch (error) {
			console.error('Error deleting user:', error);
			return fail(500, {
				error: 'Internal server error'
			});
		}
	},

	createCompany: async (request) => {
		const form = await superValidate(request, zod(companySchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminCompanyService.createCompany(form.data);

			if (result) {
				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error creating company:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	},

	updateCompany: async (request) => {
		const form = await superValidate(request, zod(companySchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminCompanyService.updateCompany(form.data.id!, form.data);

			if (result) {
				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error updating company:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	},

	deleteCompany: async ({ request }) => {
		const formData = await request.formData();
		const companyId = formData.get('id') as string;

		if (!companyId) {
			return fail(400, {
				error: 'Company ID is required'
			});
		}

		try {
			const result = await SuperAdminCompanyService.deleteCompany(companyId);

			if (result.success) {
				return {
					success: true
				};
			} else {
				return fail(400, {
					error: result.error || 'Failed to delete company'
				});
			}
		} catch (error) {
			console.error('Error deleting company:', error);
			return fail(500, {
				error: 'Internal server error'
			});
		}
	},

	createPlant: async (request) => {
		const form = await superValidate(request, zod(plantSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminPlantService.createPlant(form.data);

			if (result) {
				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error creating plant:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	},

	updatePlant: async (request) => {
		const form = await superValidate(request, zod(plantSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminPlantService.updatePlant(form.data.id!, form.data);

			if (result) {
				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error updating plant:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	},

	deletePlant: async ({ request }) => {
		const formData = await request.formData();
		const plantId = formData.get('id') as string;

		if (!plantId) {
			return fail(400, {
				error: 'Plant ID is required'
			});
		}

		try {
			const result = await SuperAdminPlantService.deletePlant(plantId);

			if (result.success) {
				return {
					success: true
				};
			} else {
				return fail(400, {
					error: result.error || 'Failed to delete plant'
				});
			}
		} catch (error) {
			console.error('Error deleting plant:', error);
			return fail(500, {
				error: 'Internal server error'
			});
		}
	}
};
