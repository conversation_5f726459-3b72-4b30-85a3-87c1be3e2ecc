<script lang="ts">
	import { fade } from 'svelte/transition';
	import { Users, Building, Factory, Plus } from '@lucide/svelte';
	import { t } from '$lib/translations';
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import { createCompaniesColumns } from './_components/columns-companies';
	import { createPlantsColumns } from './_components/columns-plants';
	import { createUsersColumns } from './_components/columns-users';
	import FormNewUser from './_components/form-new-user.svelte';
	import FormEditUser from './_components/form-edit-user.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import type { UserListDTO } from '$lib/DTO/admin/users/UsersListDTO';
	import type { CompaniesListDTO } from '$lib/DTO/admin/companies/CompaniesListDTO';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import FormCompany from './_components/form-company.svelte';
	import FormPlant from './_components/form-plant.svelte';
	import type { PlantsListDTO } from '$lib/DTO/admin/plants/PlantsListDTO';

	let { data } = $props();

	let activeTab = $state('companies');
	let showNewUserDialog = $state(false);
	let showEditUserDialog = $state(false);
	let showCompanyDialog = $state(false);
	let showPlantDialog = $state(false);
	let selectedUser = $state<UserListDTO | null>(null);
	let selectedCompany = $state<CompaniesListDTO | null>(null);
	let selectedPlant = $state<PlantsListDTO | null>(null);

	let isCompanyEdit = $state(false);
	let isPlantEdit = $state(false);

	function handleEditUser(user: UserListDTO) {
		selectedUser = user;
		showEditUserDialog = true;
	}

	function handleCloseEditDialog() {
		showEditUserDialog = false;
		selectedUser = null;
	}

	function handleEditCompany(company: CompaniesListDTO) {
		isCompanyEdit = true;
		selectedCompany = company;
		showCompanyDialog = true;
	}

	function handleCloseCompanyDialog() {
		setTimeout(() => {
			showCompanyDialog = false;
			selectedCompany = null;
			isCompanyEdit = false;
		}, 200);
	}

	function handleEditPlant(plant: PlantsListDTO) {
		isPlantEdit = true;
		selectedPlant = plant;
		showPlantDialog = true;
	}

	function handleClosePlantDialog() {
		setTimeout(() => {
			showPlantDialog = false;
			selectedPlant = null;
			isPlantEdit = false;
		}, 200);
	}

	const companiesColumns = createCompaniesColumns({
		onEdit: handleEditCompany
	});

	const plantsColumns = createPlantsColumns({
		onEdit: handleEditPlant
	});

	const usersColumns = createUsersColumns({
		onEdit: handleEditUser
	});
</script>

<svelte:head>
	<title>LeanAudit - {$t('admin.adminPanel.title')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-5 h-[calc(100vh-8rem)] w-full overflow-hidden rounded-2xl bg-white px-4 py-6 md:h-[calc(100vh-8rem)] md:overflow-auto md:px-8 md:py-9"
>
	<!-- Navigation Tabs -->
	<div class="mb-8 flex gap-4 border-b border-gray-200">
		<button
			class="flex cursor-pointer items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors
                 {activeTab === 'companies'
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			onclick={() => (activeTab = 'companies')}
		>
			<Building class="h-4 w-4" />
			{$t('admin.companies.title')}
		</button>

		<button
			class="flex cursor-pointer items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors
                 {activeTab === 'workplaces'
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			onclick={() => (activeTab = 'workplaces')}
		>
			<Factory class="h-4 w-4" />
			{$t('admin.plants.title')}
		</button>

		<button
			class="flex cursor-pointer items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors
				{activeTab === 'users'
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			onclick={() => (activeTab = 'users')}
		>
			<Users class="h-4 w-4" />
			{$t('admin.users.title')}
		</button>
	</div>

	<!-- Content Area -->
	<div class="flex h-[calc(100%-8rem)] flex-col">
		{#if activeTab === 'companies'}
			<div class="flex h-full flex-col space-y-4">
				<div class="flex items-center justify-between">
					<h2 class="text-xl font-semibold">{$t('admin.companies.title')}</h2>
					<ButtonIcon Icon={Plus} onClickAction={() => (showCompanyDialog = true)} />
				</div>
				<div class="flex-1 overflow-hidden pt-4">
					<DataTable data={data.companies} columns={companiesColumns} pageSize={6} />
				</div>
			</div>
		{:else if activeTab === 'workplaces'}
			<div class="flex h-full flex-col space-y-4">
				<div class="flex items-center justify-between">
					<h2 class="text-xl font-semibold">{$t('admin.plants.title')}</h2>
					<ButtonIcon Icon={Plus} onClickAction={() => (showPlantDialog = true)} />
				</div>
				<div class="flex-1 overflow-hidden">
					<DataTable data={data.plants} columns={plantsColumns} pageSize={6} />
				</div>
			</div>
		{:else if activeTab === 'users'}
			<div class="flex h-full flex-col space-y-4">
				<div class="flex items-center justify-between">
					<h2 class="text-xl font-semibold">{$t('admin.users.title')}</h2>
					<ButtonIcon Icon={Plus} onClickAction={() => (showNewUserDialog = true)} />
				</div>
				<div class="flex-1 overflow-hidden">
					<DataTable data={data.users} columns={usersColumns} pageSize={6} />
				</div>
			</div>
		{/if}
	</div>
</div>

{#if showNewUserDialog}
	<CustomDialog
		title={$t('admin.users.dialog.createTitle')}
		open={showNewUserDialog}
		onClose={() => (showNewUserDialog = false)}
		width="max-w-2xl"
	>
		<FormNewUser
			newUserForm={data.newUserForm}
			companies={data.companies}
			plants={data.plants}
			onClose={() => (showNewUserDialog = false)}
		/>
	</CustomDialog>
{/if}

{#if showEditUserDialog && selectedUser}
	<CustomDialog
		title={$t('admin.users.dialog.editTitle')}
		subtitle={selectedUser.firstName + ' ' + selectedUser.lastName}
		open={showEditUserDialog}
		onClose={handleCloseEditDialog}
		width="max-w-2xl"
	>
		<FormEditUser
			editUserForm={data.editUserForm}
			companies={data.companies}
			plants={data.plants}
			user={selectedUser}
			onClose={handleCloseEditDialog}
		/>
	</CustomDialog>
{/if}

<!-- Companies -->
<CustomDialog
	open={showCompanyDialog}
	title={isCompanyEdit
		? $t('admin.companies.dialog.editTitle')
		: $t('admin.companies.dialog.createTitle')}
	subtitle={isCompanyEdit ? selectedCompany?.name : undefined}
	onClose={() => (isCompanyEdit ? handleCloseCompanyDialog() : (showCompanyDialog = false))}
	width="max-w-2xl"
>
	<FormCompany
		companyForm={data.companyForm}
		onClose={() => (isCompanyEdit ? handleCloseCompanyDialog() : (showCompanyDialog = false))}
		isEdit={isCompanyEdit}
		company={isCompanyEdit ? selectedCompany! : undefined}
	/>
</CustomDialog>

<!-- Plants -->
<CustomDialog
	open={showPlantDialog}
	title={isPlantEdit ? $t('admin.plants.dialog.editTitle') : $t('admin.plants.dialog.createTitle')}
	subtitle={isPlantEdit ? selectedPlant?.name : undefined}
	onClose={() => (isPlantEdit ? handleClosePlantDialog() : (showPlantDialog = false))}
	width="max-w-2xl"
>
	<FormPlant
		plantForm={data.plantForm}
		onClose={() => (isPlantEdit ? handleClosePlantDialog() : (showPlantDialog = false))}
		isEdit={isPlantEdit}
		plant={isPlantEdit ? selectedPlant! : undefined}
		companies={data.companies}
	/>
</CustomDialog>
