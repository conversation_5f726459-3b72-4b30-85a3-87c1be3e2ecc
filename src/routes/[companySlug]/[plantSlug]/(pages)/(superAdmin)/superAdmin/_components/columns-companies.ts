import { renderComponent } from '$lib/components/ui/data-table';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import type { ColumnDef } from '@tanstack/table-core';
import DataTableActionsCompany from './data-table-actions-company.svelte';
import type { CompaniesListDTO } from '$lib/DTO/admin/companies/CompaniesListDTO';
import { t } from '$lib/translations';
import { get } from 'svelte/store';

export function createCompaniesColumns(callbacks: {
	onEdit: (company: CompaniesListDTO) => void;
}): ColumnDef<CompaniesListDTO>[] {
	const $t = get(t);

	return [
		{
			accessorKey: 'name',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.companies.table.headers.name'),
					column
				}),
			cell: ({ row }) => {
				const name = row.getValue('name') as string;
				return renderComponent(Cell, {
					content: name
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'code',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.companies.table.headers.code'),
					column
				}),
			cell: ({ row }) => {
				const code = row.getValue('code') as string;
				return renderComponent(Cell, {
					content: code
				});
			},
			enableSorting: true
		},
		{
			id: 'active',
			accessorKey: 'active',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.active'),
					column
				}),
			cell: ({ row }) => {
				const active = row.getValue('active') as boolean;
				return renderComponent(Cell, {
					content: active
						? t.get('auditTypes.list.activeStates.true')
						: t.get('auditTypes.list.activeStates.false')
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'numberOfLicenses',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.companies.table.headers.licenses'),
					column
				}),
			cell: ({ row }) => {
				const licenses = row.getValue('numberOfLicenses') as number;
				return renderComponent(Cell, {
					content: licenses.toString()
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'url',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.companies.table.headers.url'),
					column
				}),
			cell: ({ row }) => {
				const url = row.getValue('url') as string;
				return renderComponent(Cell, {
					content: url || '-'
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',
			cell: ({ row }) => {
				return renderComponent(DataTableActionsCompany, {
					row: row.original,
					onEdit: () => callbacks.onEdit(row.original)
				});
			},
			enableSorting: false
		}
	];
}
