import { renderComponent } from '$lib/components/ui/data-table';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import type { ColumnDef } from '@tanstack/table-core';
import DataTableActionsPlant from './data-table-actions-plant.svelte';
import { t } from '$lib/translations';
import { get } from 'svelte/store';
import type { PlantsListDTO } from '$lib/DTO/admin/plants/PlantsListDTO';

export function createPlantsColumns(callbacks: {
	onEdit: (plant: PlantsListDTO) => void;
}): ColumnDef<PlantsListDTO>[] {
	const $t = get(t);

	return [
		{
			accessorKey: 'name',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.name'),
					column
				}),
			cell: ({ row }) => {
				const name = row.getValue('name') as string;
				return renderComponent(Cell, {
					content: name
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'code',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.code'),
					column
				}),
			cell: ({ row }) => {
				const code = row.getValue('code') as string;
				return renderComponent(Cell, {
					content: code
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'slug',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: 'Slug',
					column
				}),
			cell: ({ row }) => {
				const slug = row.getValue('slug') as string;
				return renderComponent(Cell, {
					content: slug
				});
			},
			enableSorting: true
		},
		{
			id: 'active',
			accessorKey: 'active',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.active'),
					column
				}),
			cell: ({ row }) => {
				const active = row.getValue('active') as boolean;
				return renderComponent(Cell, {
					content: active
						? t.get('auditTypes.list.activeStates.true')
						: t.get('auditTypes.list.activeStates.false')
				});
			},
			enableSorting: true
		},
		{
			id: 'company',
			accessorFn: (row) => row.company?.name || '',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.company'),
					column
				}),
			cell: ({ row }) => {
				const companyName = row.original.company?.name || $t('admin.plants.table.notAssigned');
				return renderComponent(Cell, {
					content: companyName
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'countryCode',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.country'),
					column
				}),
			cell: ({ row }) => {
				const countryCode = row.getValue('countryCode') as string;
				return renderComponent(Cell, {
					content: countryCode
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'numberOfLicenses',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.licenses'),
					column
				}),
			cell: ({ row }) => {
				const licenses = row.getValue('numberOfLicenses') as number;
				return renderComponent(Cell, {
					content: licenses.toString()
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'gpsLocation',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.gpsLocation'),
					column
				}),
			cell: ({ row }) => {
				const gps = row.getValue('gpsLocation') as string;
				return renderComponent(Cell, {
					content: gps || '-'
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'url',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.plants.table.headers.url'),
					column
				}),
			cell: ({ row }) => {
				const url = row.getValue('url') as string;
				return renderComponent(Cell, {
					content: url || '-'
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',
			cell: ({ row }) => {
				return renderComponent(DataTableActionsPlant, {
					row: row.original,
					onEdit: () => callbacks.onEdit(row.original)
				});
			},
			enableSorting: false
		}
	];
}
