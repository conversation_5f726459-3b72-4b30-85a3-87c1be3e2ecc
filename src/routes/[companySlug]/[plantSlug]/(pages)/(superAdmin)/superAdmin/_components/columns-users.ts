import { renderComponent } from '$lib/components/ui/data-table';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import DataTableActionsUsers from './data-table-actions-users.svelte';
import type { UserListDTO } from '$lib/DTO/admin/users/UsersListDTO';
import type { ColumnDef } from '@tanstack/table-core';
import { t } from '$lib/translations';
import { get } from 'svelte/store';

export function createUsersColumns(callbacks: {
	onEdit: (user: UserListDTO) => void;
}): ColumnDef<UserListDTO>[] {
	const $t = get(t);

	return [
		{
			id: 'fullName',
			accessorFn: (row) => {
				const firstName = row.firstName || '';
				const lastName = row.lastName || '';
				return `${firstName} ${lastName}`.trim() || $t('admin.users.table.noName');
			},
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.users.table.headers.name'),
					column
				}),
			cell: ({ row }) => {
				const firstName = row.original.firstName || '';
				const lastName = row.original.lastName || '';
				const fullName = `${firstName} ${lastName}`.trim() || $t('admin.users.table.noName');
				return renderComponent(Cell, {
					content: fullName
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'email',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.users.table.headers.email'),
					column
				}),
			cell: ({ row }) => {
				const email = row.getValue('email') as string;
				return renderComponent(Cell, {
					content: email
				});
			},
			enableSorting: true
		},
		{
			id: 'active',
			accessorKey: 'active',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.active'),
					column
				}),
			cell: ({ row }) => {
				const active = row.getValue('active') as boolean;
				return renderComponent(Cell, {
					content: active
						? t.get('auditTypes.list.activeStates.true')
						: t.get('auditTypes.list.activeStates.false')
				});
			},
			enableSorting: true
		},
		{
			accessorKey: 'phone',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.users.table.headers.phone'),
					column
				}),
			cell: ({ row }) => {
				const phone = row.getValue('phone') as string | null;
				return renderComponent(Cell, {
					content: phone || $t('admin.users.table.notProvided')
				});
			},
			enableSorting: true
		},
		{
			id: 'company',
			accessorFn: (row) => row.company?.name || '',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.users.table.headers.company'),
					column
				}),
			cell: ({ row }) => {
				const companyName = row.original.company?.name || $t('admin.users.table.notAssigned');
				return renderComponent(Cell, {
					content: companyName
				});
			},
			enableSorting: true
		},
		{
			id: 'mainPlant',
			accessorFn: (row) => row.mainPlant?.name || '',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: $t('admin.users.table.headers.mainPlant'),
					column
				}),
			cell: ({ row }) => {
				const plantName = row.original.mainPlant?.name || $t('admin.users.table.notAssigned');
				return renderComponent(Cell, {
					content: plantName
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',

			cell: ({ row }) => {
				return renderComponent(DataTableActionsUsers, {
					row: row.original,
					onEdit: () => callbacks.onEdit(row.original)
				});
			},
			enableSorting: false
		}
	];
}
