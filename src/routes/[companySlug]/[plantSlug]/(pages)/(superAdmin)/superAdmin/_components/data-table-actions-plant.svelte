<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Ellipsis, Eye, Trash, Wrench } from '@lucide/svelte';
	import type { PlantsListDTO } from '$lib/DTO/admin/plants/PlantsListDTO';
	import { t } from '$lib/translations';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';

	let {
		row,
		onEdit
	}: {
		row: PlantsListDTO;
		onEdit: (plant: PlantsListDTO) => void;
	} = $props();

	let open = $state(false);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase">
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() =>
					goto(`/${page.params.companySlug}/${page.params.plantSlug}/superAdmin/plant/${row.id}`)}
			>
				<Eye class="text-white" />
				<span>{$t('common.buttons.details')}</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => onEdit(row)}
			>
				<Wrench class="text-white" />
				<span>{$t('common.buttons.edit')}</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => (open = true)}
			>
				<Trash class="text-white" />
				<span>{$t('common.buttons.delete')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	id={row.id}
	title={$t('admin.plants.dialog.deleteTitle')}
	description={$t('admin.plants.dialog.deleteDescription')}
	code={row.code}
	formAction="?/deletePlant"
	onDelete={() => toast.success($t('admin.plants.actionMessages.deleted'))}
	onClose={() => (open = false)}
/>
