<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { MoreHorizontal, Edit, Trash2, Trash, Ellipsis, Wrench } from '@lucide/svelte';
	import type { UserListDTO } from '$lib/DTO/admin/users/UsersListDTO';
	import { t } from '$lib/translations';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';

	let {
		row,
		onEdit
	}: {
		row: UserListDTO;
		onEdit: (user: UserListDTO) => void;
	} = $props();

	let open = $state(false);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase">
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => onEdit(row)}
			>
				<Wrench class="text-white" />
				<span>{$t('common.buttons.edit')}</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => (open = true)}
			>
				<Trash class="text-white" />
				<span>{$t('common.buttons.delete')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	id={row.id}
	title={$t('admin.users.dialog.deleteTitle')}
	description={$t('admin.users.dialog.deleteDescription')}
	code={row.firstName + ' ' + row.lastName}
	formAction="?/deleteUser"
	onDelete={() => toast.success($t('admin.users.actionMessages.deleted'))}
	onClose={() => (open = false)}
/>
