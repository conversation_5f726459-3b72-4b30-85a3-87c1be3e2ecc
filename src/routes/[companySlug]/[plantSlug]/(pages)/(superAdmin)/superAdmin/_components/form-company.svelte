<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index';
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';
	import { fly } from 'svelte/transition';
	import { t } from '$lib/translations';
	import type { CompaniesListDTO } from '$lib/DTO/admin/companies/CompaniesListDTO';
	import { companySchema } from '$lib/schemas/company';
	import { cn } from '$lib/utils';
	import Switch from '$lib/components/ui/switch/switch.svelte';

	let {
		companyForm,
		company,
		onClose,
		isEdit = false
	}: {
		companyForm: any;
		company?: CompaniesListDTO;
		onClose: () => void;
		isEdit?: boolean;
	} = $props();

	const form = superForm(companyForm, {
		validators: zodClient(companySchema),
		validationMethod: 'oninput',
		onResult: ({ result }) => {
			if (result.type === 'success') {
				toast.success(
					isEdit
						? $t('admin.companies.actions.edit.success')
						: $t('admin.companies.actions.create.success')
				);
				onClose();
			}
		}
	});

	const { form: formData, enhance, errors } = form;

	// Initialize form with company data if editing
	if (isEdit && company) {
		$formData.id = company.id;
		$formData.name = company.name;
		$formData.code = company.code;
		$formData.slug = company.slug;
		$formData.active = company.active;
		$formData.url = company.url || undefined;
		$formData.numberOfLicenses = company.numberOfLicenses;
	}
</script>

<form
	method="POST"
	action={isEdit ? '?/updateCompany' : '?/createCompany'}
	use:enhance
	class="flex flex-col gap-6"
>
	{#if isEdit}
		<input type="hidden" name="id" value={company?.id} />
	{/if}

	<!-- Name and Code row -->
	<div class="h-[85px]">
		<Form.Field {form} name="name">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium">{$t('admin.companies.form.name')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.name}
						placeholder={$t('admin.companies.form.namePlaceholder')}
						class={`h-10 bg-white ${$errors.name ? 'border-red-500' : ''}`}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
		</Form.Field>
	</div>
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="code">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.companies.form.code')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.code}
							placeholder={$t('admin.companies.form.codePlaceholder')}
							class={`h-10 bg-white ${$errors.code ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<!-- Slug row -->
		<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="slug">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.companies.form.slug')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.slug}
							placeholder={$t('admin.companies.form.slugPlaceholder')}
							class={`h-10 bg-white ${$errors.slug ? 'border-red-500' : ''}`}
							oninput={(e) => {
								const target = e.target as HTMLInputElement | null;
								if (target) {
									$formData.slug = target.value.replace(/\s+/g, '');
								}
							}}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Status and Licenses row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="relative flex h-[85px] pb-6">
			<Form.Field {form} name="active" class="w-full">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="mt-4 flex flex-1 flex-row items-center justify-center gap-4">
							<Switch
								bind:checked={$formData.active}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out ',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.active ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.active
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.active
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="numberOfLicenses">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('admin.companies.form.numberOfLicenses')}</Form.Label
						>
						<Input
							{...props}
							type="number"
							bind:value={$formData.numberOfLicenses}
							placeholder={$t('admin.companies.form.numberOfLicensesPlaceholder')}
							class={`h-10 bg-white ${$errors.numberOfLicenses ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- URL row -->
	<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<Form.Field {form} name="url">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium">{$t('admin.companies.form.url')}</Form.Label>
					<Input
						{...props}
						type="url"
						placeholder={$t('admin.companies.form.urlPlaceholder')}
						bind:value={$formData.url}
						class={`h-10 bg-white ${$errors.url ? 'border-red-500' : ''}`}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
		</Form.Field>
	</div>

	<div class="flex justify-end gap-4">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={() => {
				onClose();
			}}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Form.Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{$t('common.buttons.save')}
		</Form.Button>
	</div>
</form>
