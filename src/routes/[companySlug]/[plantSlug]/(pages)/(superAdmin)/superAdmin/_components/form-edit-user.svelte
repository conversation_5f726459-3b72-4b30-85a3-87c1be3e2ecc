<script lang="ts">
	import { updateUserSchema, type UpdateUserForm } from '$lib/schemas/admin';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { fly } from 'svelte/transition';
	import { t } from '$lib/translations';
	import type { PlantsListDTO } from '$lib/DTO/plants/PlantsListDTO';
	import type { UserListDTO } from '$lib/DTO/admin/users/UsersListDTO';
	import { Button } from '$lib/components/ui/button';
	import { useForm } from '$lib/hooks/superformValidation';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import Switch from '$lib/components/ui/switch/switch.svelte';

	let {
		editUserForm,
		companies,
		plants,
		user,
		onClose
	}: {
		editUserForm: SuperValidated<UpdateUserForm>;
		companies: any[];
		plants: PlantsListDTO[];
		user: UserListDTO;
		onClose: () => void;
	} = $props();

	const form = superForm(editUserForm, {
		validators: zodClient(updateUserSchema),
		validationMethod: 'oninput',
		onResult: ({ result }) => {
			if (result.type === 'success') {
				if (result.data && 'redirectTo' in result.data && result.data.redirectTo) {
					onClose();
					toast.success($t('admin.users.editUser.successUpdate'));
					goto(result.data.redirectTo);
				} else {
					onClose();
					toast.success($t('admin.users.editUser.successUpdate'));
				}
			}
		}
	});

	const { form: formData, enhance, errors } = form;

	// Initialize form with user data
	$formData.id = user.id;
	$formData.firstName = user.firstName || '';
	$formData.lastName = user.lastName || '';
	$formData.email = user.email;
	$formData.active = user.active;
	$formData.phone = user.phone || '';
	$formData.companyId = user.company?.id;
	$formData.plantId = user.mainPlant?.id;
	$formData.cardNumber = user.cardNumber || '';

	let selectedCompany = $state<string | undefined>(user.company?.id);
	let filteredPlants = $derived(
		selectedCompany ? plants.filter((plant) => plant.company?.id === selectedCompany) : []
	);

	let companyOpen = $state(false);
	let plantOpen = $state(false);

	function handleCompanyChange(companyId: string) {
		selectedCompany = companyId;
		$formData.companyId = companyId;
		// Reset plant selection when company changes
		$formData.plantId = undefined;
		companyOpen = false;
	}

	function handlePlantChange(plantId: string) {
		$formData.plantId = plantId;
		plantOpen = false;
	}
</script>

<form method="POST" use:enhance action="?/updateUser" class="flex flex-col gap-6">
	<input type="hidden" name="id" value={user.id} />

	<!-- Name row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="firstName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.firstName')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.firstName}
							class={`h-10 bg-white ${$errors.firstName ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="lastName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.lastName')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.lastName}
							class={`h-10 bg-white ${$errors.lastName ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Contact row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.email')}</Form.Label>
						<Input
							{...props}
							type="email"
							bind:value={$formData.email}
							class={`h-10 bg-white ${$errors.email ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="phone">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.phone')}</Form.Label>
						<Input
							{...props}
							type="tel"
							bind:value={$formData.phone}
							class={`h-10 bg-white ${$errors.phone ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Company and Plant row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="relative pb-6">
			<Form.Field {form} name="companyId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">Společnost</Form.Label>
						<Popover.Root bind:open={companyOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'items-center justify-between',
									!$formData.companyId && 'text-muted-foreground',
									$errors.companyId && 'border-red-500'
								)}
								role="combobox"
								{...props}
							>
								{#if $formData.companyId && companies}
									{companies.find((c) => c.id === $formData.companyId)?.name ||
										'Vyberte společnost'}
								{:else}
									Vyberte společnost
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.companyId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input autofocus placeholder="Hledat společnost..." class="h-9" />
									<Command.List>
										<Command.Empty>Žádná společnost nenalezena</Command.Empty>
										<Command.Group>
											{#each companies as company}
												<Command.Item
													value={company.name}
													onSelect={() => handleCompanyChange(company.id)}
												>
													<span>{company.name}</span>
													{#if company.id === $formData.companyId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="plantId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">Hlavní závod</Form.Label>
						<Popover.Root bind:open={plantOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'items-center justify-between',
									!$formData.plantId && 'text-muted-foreground',
									$errors.plantId && 'border-red-500'
								)}
								role="combobox"
								disabled={!selectedCompany}
								{...props}
							>
								{#if $formData.plantId && filteredPlants}
									{filteredPlants.find((p: { id: string }) => p.id === $formData.plantId)?.name ||
										'Vyberte závod'}
								{:else}
									{selectedCompany ? 'Vyberte závod' : 'Nejdříve vyberte společnost'}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.plantId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input autofocus placeholder="Hledat závod..." class="h-9" />
									<Command.List>
										<Command.Empty>Žádný závod nenalezen</Command.Empty>
										<Command.Group>
											{#each filteredPlants as plant}
												<Command.Item
													value={plant.name}
													onSelect={() => handlePlantChange(plant.id)}
												>
													<span>{plant.name}</span>
													{#if plant.id === $formData.plantId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>
	</div>

	<!-- active and Card Number -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="relative flex h-[85px] pb-6">
			<Form.Field {form} name="active" class="w-full">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="mt-4 flex flex-1 flex-row items-center justify-center gap-4">
							<Switch
								bind:checked={$formData.active}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out ',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.active ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.active
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.active
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="cardNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">Číslo karty (volitelné)</Form.Label>
						<Input
							{...props}
							bind:value={$formData.cardNumber}
							class={`h-10 bg-white ${$errors.cardNumber ? 'border-red-500' : ''}`}
							placeholder="Zadejte číslo karty"
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<div class="flex justify-end gap-4">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={() => {
				onClose();
			}}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Form.Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{$t('common.buttons.save')}
		</Form.Button>
	</div>
</form>
