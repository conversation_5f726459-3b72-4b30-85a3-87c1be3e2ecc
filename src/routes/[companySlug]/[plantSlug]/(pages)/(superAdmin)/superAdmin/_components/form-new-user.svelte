<script lang="ts">
	import { createUserSchema, type CreateUserForm } from '$lib/schemas/admin';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { fly } from 'svelte/transition';
	import { t } from '$lib/translations';
	import { invalidateAll } from '$app/navigation';
	import type { PlantsListDTO } from '$lib/DTO/plants/PlantsListDTO';
	import { Button } from '$lib/components/ui/button';
	import Switch from '$lib/components/ui/switch/switch.svelte';
	import { toast } from 'svelte-sonner';

	let {
		newUserForm,
		companies,
		plants,
		onClose
	}: {
		newUserForm: SuperValidated<CreateUserForm>;
		companies: any[];
		plants: PlantsListDTO[];
		onClose: () => void;
	} = $props();

	const form = superForm(newUserForm, {
		validators: zodClient(createUserSchema),
		validationMethod: 'oninput',
		onResult: ({ result }) => {
			if (result.type === 'success' && result.data?.success) {
				onClose();
				toast.success($t('admin.users.actionMessages.created'));
			}
		}
	});

	const { form: formData, enhance, errors } = form;

	let selectedCompany = $state<string | undefined>();
	let filteredPlants = $derived(
		selectedCompany ? plants.filter((plant) => plant.company?.id === selectedCompany) : []
	);

	let companyOpen = $state(false);
	let plantOpen = $state(false);

	function handleCompanyChange(companyId: string) {
		selectedCompany = companyId;
		$formData.companyId = companyId;
		// Reset plant selection when company changes
		$formData.plantId = undefined;
		companyOpen = false;
	}

	function handlePlantChange(plantId: string) {
		$formData.plantId = plantId;
		plantOpen = false;
	}
</script>

<form method="POST" use:enhance action="?/createUser" class="flex flex-col gap-6">
	<!-- Name row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="firstName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.firstName')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.firstName}
							placeholder={$t('admin.users.form.firstNamePlaceholder')}
							class={`h-10 bg-white ${$errors.firstName ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="lastName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.lastName')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.lastName}
							placeholder={$t('admin.users.form.lastNamePlaceholder')}
							class={`h-10 bg-white ${$errors.lastName ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Contact row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.email')}</Form.Label>
						<Input
							{...props}
							type="email"
							bind:value={$formData.email}
							placeholder={$t('admin.users.form.emailPlaceholder')}
							class={`h-10 bg-white ${$errors.email ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="phone">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.phone')}</Form.Label>
						<Input
							{...props}
							type="tel"
							bind:value={$formData.phone}
							placeholder={$t('admin.users.form.phonePlaceholder')}
							class={`h-10 bg-white ${$errors.phone ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Company and Plant row -->
	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="relative pb-6">
			<Form.Field {form} name="companyId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.users.form.company')}</Form.Label>
						<Popover.Root bind:open={companyOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'items-center justify-between',
									!$formData.companyId && 'text-muted-foreground',
									$errors.companyId && 'border-red-500'
								)}
								role="combobox"
								{...props}
							>
								{#if $formData.companyId && companies}
									{companies.find((c) => c.id === $formData.companyId)?.name ||
										$t('admin.users.form.selectCompany')}
								{:else}
									{$t('admin.users.form.selectCompany')}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.companyId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input
										autofocus
										placeholder={$t('admin.users.form.searchCompany')}
										class="h-9"
									/>
									<Command.List>
										<Command.Empty>{$t('admin.users.form.noCompanyFound')}</Command.Empty>
										<Command.Group>
											{#each companies as company}
												<Command.Item
													value={company.name}
													onSelect={() => handleCompanyChange(company.id)}
												>
													<span>{company.name}</span>
													{#if company.id === $formData.companyId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="plantId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.users.form.mainPlant')}</Form.Label>
						<Popover.Root bind:open={plantOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'items-center justify-between',
									!$formData.plantId && 'text-muted-foreground',
									$errors.plantId && 'border-red-500'
								)}
								role="combobox"
								disabled={!selectedCompany}
								{...props}
							>
								{#if $formData.plantId && filteredPlants}
									{filteredPlants.find((p: { id: string }) => p.id === $formData.plantId)?.name ||
										$t('admin.users.form.selectPlant')}
								{:else}
									{selectedCompany
										? $t('admin.users.form.selectPlant')
										: $t('admin.users.form.selectCompanyFirst')}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.plantId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input
										autofocus
										placeholder={$t('admin.users.form.searchPlant')}
										class="h-9"
									/>
									<Command.List>
										<Command.Empty>{$t('admin.users.form.noPlantFound')}</Command.Empty>
										<Command.Group>
											{#each filteredPlants as plant}
												<Command.Item
													value={plant.name}
													onSelect={() => handlePlantChange(plant.id)}
												>
													<span>{plant.name}</span>
													{#if plant.id === $formData.plantId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>
	</div>
	<!-- Password column -->
	<div class="flex flex-col gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="h-[85px]">
			<Form.Field {form} name="password">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('auth.register.password')}</Form.Label>
						<Input
							{...props}
							type="password"
							bind:value={$formData.password}
							placeholder={$t('admin.users.form.passwordPlaceholder')}
							class={`h-10 bg-white ${$errors.password ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="passwordConfirm">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('auth.register.confirmPassword')}</Form.Label
						>
						<Input
							{...props}
							type="password"
							bind:value={$formData.passwordConfirm}
							class={`h-10 bg-white ${$errors.passwordConfirm ? 'border-red-500' : ''}`}
							placeholder={$t('admin.users.form.passwordPlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Active and Card Number -->

	<div class="grid grid-cols-2 gap-4" in:fly={{ y: 20, duration: 300, delay: 150 }}>
		<div class="relative flex h-[85px] pb-6">
			<Form.Field {form} name="active" class="w-full">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="mt-4 flex flex-1 flex-row items-center justify-center gap-4">
							<Switch
								bind:checked={$formData.active}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out ',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.active ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.active
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.active
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="h-[85px]" in:fly={{ y: 20, duration: 300, delay: 150 }}>
			<Form.Field {form} name="cardNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.users.form.cardNumber')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.cardNumber}
							class={`h-10 bg-white ${$errors.cardNumber ? 'border-red-500' : ''}`}
							placeholder={$t('admin.users.form.cardNumberPlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<div class="flex justify-end gap-4">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={() => {
				onClose();
			}}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Form.Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{$t('common.buttons.create')}
		</Form.Button>
	</div>
</form>
