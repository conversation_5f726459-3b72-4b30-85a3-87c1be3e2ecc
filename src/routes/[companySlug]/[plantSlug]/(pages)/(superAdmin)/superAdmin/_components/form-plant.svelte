<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';
	import { t } from '$lib/translations';
	import type { CompaniesListDTO } from '$lib/DTO/admin/companies/CompaniesListDTO';
	import { plantSchema } from '$lib/schemas/company';
	import type { PlantsListDTO } from '$lib/DTO/admin/plants/PlantsListDTO';
	import Switch from '$lib/components/ui/switch/switch.svelte';

	let {
		plantForm,
		plant,
		companies,
		onClose,
		isEdit = false
	}: {
		plantForm: any;
		plant?: PlantsListDTO;
		companies: CompaniesListDTO[];
		onClose: () => void;
		isEdit?: boolean;
	} = $props();

	const form = superForm(plantForm, {
		validators: zodClient(plantSchema),
		validationMethod: 'oninput',
		onResult: ({ result }) => {
			if (result.type === 'success') {
				toast.success(
					isEdit
						? $t('admin.plants.actions.edit.success')
						: $t('admin.plants.actions.create.success')
				);
				onClose();
			}
		}
	});

	const { form: formData, enhance, errors } = form;

	if (isEdit && plant) {
		$formData.id = plant.id;
		$formData.name = plant.name;
		$formData.code = plant.code;
		$formData.slug = plant.slug;
		$formData.active = plant.active;
		$formData.companyId = plant.company?.id || undefined;
		$formData.countryCode = plant.countryCode;
		$formData.url = plant.url || undefined;
		$formData.gpsLocation = plant.gpsLocation || undefined;
		$formData.numberOfLicenses = plant.numberOfLicenses;
	}

	let selectedCompany = $state<string | undefined>(plant?.company?.id);
	let companyOpen = $state(false);

	function handleCompanyChange(companyId: string) {
		selectedCompany = companyId;
		$formData.companyId = companyId;
		companyOpen = false;
	}
</script>

<form
	method="POST"
	action={isEdit ? '?/updatePlant' : '?/createPlant'}
	use:enhance
	class="flex flex-col gap-6"
>
	{#if isEdit}
		<input type="hidden" name="id" value={plant?.id} />
	{/if}

	<!-- Name and Code row -->
	<div class="h-[85px]">
		<Form.Field {form} name="name">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium">{$t('admin.plants.form.name')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.name}
						placeholder={$t('admin.plants.form.namePlaceholder')}
						class={`h-10 bg-white ${$errors.name ? 'border-red-500' : ''}`}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
		</Form.Field>
	</div>

	<div class="grid grid-cols-2 gap-4">
		<div class="h-[85px]">
			<Form.Field {form} name="code">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.plants.form.code')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.code}
							placeholder={$t('admin.plants.form.codePlaceholder')}
							class={`h-10 bg-white ${$errors.code ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="slug">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.plants.form.slug')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.slug}
							placeholder={$t('admin.plants.form.slugPlaceholder')}
							class={`h-10 bg-white ${$errors.slug ? 'border-red-500' : ''}`}
							oninput={(e) => {
								const target = e.target as HTMLInputElement | null;
								if (target) {
									$formData.slug = target.value.replace(/\s+/g, '');
								}
							}}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- Company -->
	<div class="relative pb-6">
		<Form.Field {form} name="companyId">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium">{$t('admin.plants.form.company')}</Form.Label>
					<Popover.Root bind:open={companyOpen}>
						<Popover.Trigger
							class={cn(
								'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
								'items-center justify-between',
								!$formData.companyId && 'text-muted-foreground',
								$errors.companyId && 'border-red-500'
							)}
							role="combobox"
							{...props}
						>
							{#if $formData.companyId && companies}
								{companies.find((c) => c.id === $formData.companyId)?.name ||
									$t('admin.plants.form.selectCompany')}
							{:else}
								{$t('admin.plants.form.selectCompany')}
							{/if}
							<ChevronsUpDown class="h-4 w-4 opacity-50" />
						</Popover.Trigger>
						<input hidden value={$formData.companyId} name={props.name} />
						<Popover.Content
							class="w-(--bits-popover-anchor-width) p-0"
							align="start"
							sideOffset={5}
						>
							<Command.Root>
								<Command.Input placeholder={$t('admin.plants.form.searchCompany')} />
								<Command.List>
									<Command.Empty>{$t('admin.plants.form.noCompanyFound')}</Command.Empty>
									<Command.Group>
										{#each companies as company}
											<Command.Item
												value={company.id}
												onSelect={() => handleCompanyChange(company.id)}
											>
												<Check
													class={cn(
														'mr-2 h-4 w-4',
														$formData.companyId === company.id ? 'opacity-100' : 'opacity-0'
													)}
												/>
												{company.name}
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.List>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
		</Form.Field>
	</div>

	<div class="grid grid-cols-2 gap-4">
		<div class="h-[85px]">
			<Form.Field {form} name="countryCode">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('admin.plants.form.countryCode')}</Form.Label
						>
						<Input
							{...props}
							bind:value={$formData.countryCode}
							placeholder={$t('admin.plants.form.countryCodePlaceholder')}
							class={`h-10 bg-white ${$errors.countryCode ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="relative flex h-[85px] pb-6">
			<Form.Field {form} name="active" class="w-full">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="mt-4 flex flex-1 flex-row items-center justify-center gap-4">
							<Switch
								bind:checked={$formData.active}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out ',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.active ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.active
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.active
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>
	</div>

	<!-- Licenses and GPS row -->
	<div class="grid grid-cols-2 gap-4">
		<div class="h-[85px]">
			<Form.Field {form} name="numberOfLicenses">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('admin.plants.form.numberOfLicenses')}</Form.Label
						>
						<Input
							{...props}
							type="number"
							bind:value={$formData.numberOfLicenses}
							placeholder={$t('admin.plants.form.numberOfLicensesPlaceholder')}
							class={`h-10 bg-white ${$errors.numberOfLicenses ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="h-[85px]">
			<Form.Field {form} name="gpsLocation">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium"
							>{$t('admin.plants.form.gpsLocation')}</Form.Label
						>
						<Input
							{...props}
							bind:value={$formData.gpsLocation}
							placeholder={$t('admin.plants.form.gpsLocationPlaceholder')}
							class={`h-10 bg-white ${$errors.gpsLocation ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>
	</div>

	<!-- URL row -->
	<div class="h-[85px]">
		<Form.Field {form} name="url">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium">{$t('admin.plants.form.url')}</Form.Label>
					<Input
						{...props}
						type="url"
						bind:value={$formData.url}
						placeholder={$t('admin.plants.form.urlPlaceholder')}
						class={`h-10 bg-white ${$errors.url ? 'border-red-500' : ''}`}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
		</Form.Field>
	</div>

	<div class="flex justify-end gap-4">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={() => {
				onClose();
			}}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Form.Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{$t('common.buttons.save')}
		</Form.Button>
	</div>
</form>
