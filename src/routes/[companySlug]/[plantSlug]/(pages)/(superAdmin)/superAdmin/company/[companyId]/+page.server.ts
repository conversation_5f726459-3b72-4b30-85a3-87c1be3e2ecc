import { SuperAdminCompanyService } from '$lib/server/services/superadmin/index.js';
import { error, fail } from '@sveltejs/kit';
import { createSuperAdminCompanyBreadcrumbs } from './_utils/createBreadcrumbs';
import { companySchema } from '$lib/schemas/company';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';

export const load = async ({ params }) => {
	const companyId = params.companyId;

	if (!companyId) {
		throw error(404, 'Company Not Found ');
	}

	const [companyInfo, companyForm] = await Promise.all([
		SuperAdminCompanyService.getCompanyById(companyId),
		superValidate(zod(companySchema))
	]);

	return {
		companyInfo,
		companyForm,
		breadcrumbs: createSuperAdminCompanyBreadcrumbs(
			params.companySlug,
			params.plantSlug,
			companyInfo?.name
		)
	};
};

export const actions = {
	updateCompany: async (request) => {
		const form = await superValidate(request, zod(companySchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminCompanyService.updateCompany(form.data.id!, form.data);

			if (result) {
				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error updating company:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	}
};
