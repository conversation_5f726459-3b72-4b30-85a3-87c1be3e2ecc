<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import { cn } from '$lib/utils';
	import { Switch } from '$lib/components/ui/switch';
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { useForm } from '$lib/hooks/superformValidation';
	import ConfirmDialog from '$lib/customComponents/confirm-dialog.svelte';

	import { companySchema, type CompanyForm } from '$lib/schemas/company';

	let { companyInfo, companyForm, onCancel, onSave } = $props();

	let originalData = $state<{
		id: string | undefined;
		name: string;
		code: string;
		active: boolean;
		slug: string;
		numberOfLicenses: number | null;
		url: string | undefined;
	}>({
		id: undefined,
		name: '',
		code: '',
		active: true,
		slug: '',
		numberOfLicenses: null,
		url: undefined
	});
	let showConfirmDialog = $state(false);

	let form = useForm<CompanyForm>(
		companyForm,
		// @ts-ignore
		companySchema,
		'form',
		() => {
			toast.success($t('admin.companies.toasts.saved'));
			onSave();
		},
		() => {
			toast.error($t('admin.companies.toasts.saveFailed'));
		}
	);

	const { form: formData, errors, enhance } = form;

	function hasChanges() {
		if (!originalData || !$formData) return false;

		return (
			JSON.stringify({
				id: $formData.id,
				name: $formData.name || '',
				code: $formData.code || '',
				slug: $formData.slug || '',
				numberOfLicenses: $formData.numberOfLicenses || null,
				url: $formData.url || undefined
			}) !==
			JSON.stringify({
				id: originalData.id,
				name: originalData.name || '',
				code: originalData.code || '',
				slug: originalData.slug || '',
				numberOfLicenses: originalData.numberOfLicenses || null,
				url: originalData.url || undefined
			})
		);
	}

	function handleAttemptCancelOrNavigate() {
		if (hasChanges()) {
			showConfirmDialog = true;
		} else {
			onCancel();
		}
	}

	function handleConfirmSave() {
		showConfirmDialog = false;
		const formElement = document.querySelector('form[action="?/updateCompany"]') as HTMLFormElement;
		if (formElement) {
			formElement.requestSubmit();
		}
	}

	export function attemptCancelOrNavigate() {
		handleAttemptCancelOrNavigate();
	}

	onMount(() => {
		// Populate form data from companyInfo
		$formData.id = companyInfo.id;
		$formData.name = companyInfo.name || '';
		$formData.code = companyInfo.code || '';
		$formData.slug = companyInfo.slug || '';
		$formData.numberOfLicenses = companyInfo.numberOfLicenses ?? null;
		$formData.url = companyInfo.url || undefined;
		$formData.active = companyInfo.active ?? true;

		originalData = {
			id: companyInfo.id,
			name: companyInfo.name || '',
			code: companyInfo.code || '',
			active: companyInfo.active ?? true,
			slug: companyInfo.slug || '',
			numberOfLicenses: companyInfo.numberOfLicenses ?? null,
			url: companyInfo.url || undefined
		};
	});
</script>

<div>
	<form method="POST" action="?/updateCompany" use:enhance>
		<input type="hidden" name="id" value={companyInfo.id} />
		<div class="space-y-2">
			<div class="relative pb-6">
				<Form.Field {form} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('admin.companies.info.name')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.name}
								class={$errors.name ? 'border-red-500' : ''}
								placeholder={$t('admin.companies.info.namePlaceholder')}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
				</Form.Field>
			</div>

			<div class="relative pb-6">
				<Form.Field {form} name="code">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('admin.companies.info.code')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.code}
								class={$errors.code ? 'border-red-500' : ''}
								placeholder={$t('admin.companies.info.codePlaceholder')}
								maxlength={10}
								oninput={(e) => {
									const target = e.target as HTMLInputElement;
									$formData.code = target.value.toUpperCase();
								}}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
				</Form.Field>
			</div>

			<div class="relative pb-6">
				<Form.Field {form} name="slug">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('admin.companies.info.slug')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.slug}
								class={$errors.slug ? 'border-red-500' : ''}
								placeholder={$t('admin.companies.info.slugPlaceholder')}
								maxlength={10}
								oninput={(e) => {
									const target = e.target as HTMLInputElement;
									$formData.slug = target.value.toUpperCase();
								}}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
				</Form.Field>
			</div>

			<div class="relative pb-6">
				<Form.Field {form} name="url">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('admin.companies.info.url')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.url}
								class={$errors.url ? 'border-red-500' : ''}
								placeholder={$t('admin.companies.info.urlPlaceholder')}
								maxlength={10}
								oninput={(e) => {
									const target = e.target as HTMLInputElement;
									$formData.url = target.value.toUpperCase();
								}}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
				</Form.Field>
			</div>

			<div class="relative pb-6">
				<Form.Field {form} name="numberOfLicenses">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('admin.companies.info.numberOfLicenses')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.numberOfLicenses}
								class={$errors.numberOfLicenses ? 'border-red-500' : ''}
								placeholder={$t('admin.companies.info.numberOfLicensesPlaceholder')}
								type="number"
								min="0"
								oninput={(e) => {
									const target = e.target as HTMLInputElement;
									$formData.numberOfLicenses = target.valueAsNumber;
								}}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
				</Form.Field>

				<div class="relative py-6">
					<Form.Field {form} name="active">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>{$t('admin.companies.info.active')}</Form.Label>
								<div class="flex items-center gap-4">
									<Switch
										bind:checked={$formData.active}
										{...props}
										class={cn(
											'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out',
											'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
											'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
											'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
											'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
											'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
											'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
											'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
											$errors.active ? 'ring-2 ring-red-500' : ''
										)}
									/>

									<span
										class={cn(
											'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
											'inline-block min-w-[100px]',
											$formData.active
												? 'bg-[#414E6B] text-white shadow-sm'
												: 'bg-gray-100 text-gray-600'
										)}
									>
										{$formData.active
											? $t('auditTypes.list.activeStates.true')
											: $t('auditTypes.list.activeStates.false')}
									</span>

									<span class="text-xs whitespace-pre-line text-gray-600">
										{$t('admin.companies.info.activeDescription')}
									</span>

									<input type="hidden" name={props.name} value={$formData.active.toString()} />
								</div>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
					</Form.Field>
				</div>

				<div class="relative pb-6">
					<div class="mt-4 flex justify-end gap-4">
						<Button
							type="button"
							class="font-titillium min-w-[120px] rounded-xl bg-[#B1B7C3] p-7 text-sm font-semibold text-white uppercase hover:bg-[#B1B7C3]/80"
							onclick={handleAttemptCancelOrNavigate}
						>
							{$t('common.buttons.back')}
						</Button>

						<Form.Button
							type="submit"
							class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
						>
							{$t('common.buttons.save')}
						</Form.Button>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>

<ConfirmDialog
	bind:open={showConfirmDialog}
	title={$t('common.dialogs.confirm.title')}
	description={$t('common.dialogs.confirm.text')}
	confirmButtonText={$t('common.buttons.save')}
	onClose={() => {
		showConfirmDialog = false;
		onCancel();
	}}
	onConfirm={handleConfirmSave}
/>
