import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createSuperAdminCompanyBreadcrumbs(
	currentCompanySlug: string,
	currentPlantSlug: string,
	companyName?: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		},
		{
			kind: 'static',
			label: 'admin.adminPanel.title',
			href: `/${currentCompanySlug}/${currentPlantSlug}/superAdmin`
		}
	];

	if (companyName) {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'admin.companies.title',
				href: `/${currentCompanySlug}/${currentPlantSlug}/superAdmin`
			},
			{
				kind: 'dynamic',
				label: companyName
			}
		);
	}

	return breadcrumbs;
}
