import {
	SuperAdminCompanyService,
	SuperAdminPlantService
} from '$lib/server/services/superadmin/index.js';
import { error, fail } from '@sveltejs/kit';
import { plantSchema } from '$lib/schemas/company';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { createSuperAdminPlantBreadcrumbs } from './_utils/createBreadcrumbs';

export const load = async ({ params }) => {
	const plantId = params.plantId;

	if (!plantId) {
		throw error(404, 'Plant Not Found ');
	}

	const [plantInfo, plantForm, companies] = await Promise.all([
		SuperAdminPlantService.getPlantById(plantId),
		superValidate(zod(plantSchema)),
		SuperAdminCompanyService.getAllCompanies()
	]);

	return {
		plantInfo,
		plantForm,
		companies,
		breadcrumbs: createSuperAdminPlantBreadcrumbs(
			params.companySlug,
			params.plantSlug,
			plantInfo?.name
		)
	};
};

export const actions = {
	updatePlant: async (request) => {
		const form = await superValidate(request, zod(plantSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminPlantService.updatePlant(form.data.id!, form.data);

			if (result) {
				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error updating plant:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	}
};
