<script lang="ts">
	import { fade, fly } from 'svelte/transition';
	import { locale, t } from '$lib/translations';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import { Ellipsis } from '@lucide/svelte';
	import PlantInfo from './_components/plant-info.svelte';
	import PlantForm from './_components/plant-form.svelte';

	let { data } = $props();
	let editInfo = $state(false);
	let plantFormRef = $state<{ attemptCancelOrNavigate?: () => void }>();

	function handleEditInfoToggle() {
		if (editInfo && plantFormRef?.attemptCancelOrNavigate) {
			plantFormRef.attemptCancelOrNavigate();
		} else {
			editInfo = !editInfo;
		}
	}
</script>

<svelte:head>
	<title>LeanAudit - {data.plantInfo!.name}</title>
</svelte:head>

<div in:fade={{ duration: 300 }} class="mt-4 mb-8 flex h-[calc(100vh-10rem)] w-full gap-4">
	<div class="flex h-full w-full flex-col lg:flex-row lg:gap-4">
		<div
			class="flex h-full w-full flex-col rounded-2xl bg-white p-2 lg:flex-row lg:gap-4 lg:rounded-none lg:bg-transparent"
		>
			<!-- Info panel -->
			<div
				class="w-full overflow-y-auto px-4 py-6 sm:px-10 sm:py-8 lg:h-full lg:w-[40%] lg:shrink-0 lg:rounded-2xl lg:bg-white"
			>
				<div class="flex flex-row justify-between">
					{@render plantInfo($t('admin.plants.form.name'), data.plantInfo!.name)}
					<ButtonIcon onClickAction={handleEditInfoToggle} Icon={Ellipsis} />
				</div>

				{#if !editInfo}
					<PlantInfo plantInfo={data.plantInfo} />
				{:else}
					<div class=" mt-4" in:fly={{ duration: 300 }}>
						<PlantForm
							bind:this={plantFormRef}
							plantInfo={data.plantInfo}
							plantForm={data.plantForm}
							companies={data.companies}
							onCancel={() => {
								editInfo = false;
							}}
							onSave={() => {
								editInfo = false;
							}}
						/>
					</div>
				{/if}
			</div>

			<!-- Questions panel  -->
			<div class="flex-1 overflow-auto p-2 sm:p-4 lg:rounded-2xl lg:bg-white lg:p-6">
				<div class="flex h-full flex-col">
					<div class="flex justify-center">topbar</div>
					<div class="flex flex-1 items-center justify-center">content</div>
				</div>
			</div>
		</div>
	</div>
</div>

{#snippet plantInfo(name: string, content: string)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span class="text-sm break-words text-[#4B505A] lg:text-lg">{content}</span>
	</div>
{/snippet}
