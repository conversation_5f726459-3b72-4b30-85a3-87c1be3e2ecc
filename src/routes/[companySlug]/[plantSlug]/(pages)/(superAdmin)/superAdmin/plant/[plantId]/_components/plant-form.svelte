<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as Popover from '$lib/components/ui/popover/index';
	import * as Command from '$lib/components/ui/command/index';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import { cn } from '$lib/utils';
	import { Switch } from '$lib/components/ui/switch';
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { useForm } from '$lib/hooks/superformValidation';
	import ConfirmDialog from '$lib/customComponents/confirm-dialog.svelte';
	import { plantSchema, type PlantForm } from '$lib/schemas/company';
	import { Check, ChevronsUpDown } from '@lucide/svelte';

	let { plantInfo, plantForm, companies, onCancel, onSave } = $props();

	let companyOpen = $state(false);

	let originalData = $state<{
		id: string | undefined;
		name: string;
		code: string;
		active: boolean;
		slug: string;
		numberOfLicenses: number | null;
		url: string | undefined;
		countryCode: string;
		gpsLocation: string | undefined;
		companyId?: string;
		eKaizenFormURL?: string;
		tasksEnabled: boolean;
	}>({
		id: undefined,
		name: '',
		code: '',
		active: true,
		slug: '',
		numberOfLicenses: null,
		url: undefined,
		countryCode: '',
		gpsLocation: undefined,
		companyId: undefined,
		eKaizenFormURL: undefined,
		tasksEnabled: false
	});
	let showConfirmDialog = $state(false);

	let form = useForm<PlantForm>(
		plantForm,
		// @ts-ignore
		plantSchema,
		'form',
		() => {
			toast.success($t('admin.plants.toasts.saved'));
			onSave();
		},
		() => {
			toast.error($t('admin.plants.toasts.saveFailed'));
		}
	);

	const { form: formData, errors, enhance } = form;

	function hasChanges() {
		if (!originalData || !$formData) return false;

		const current = {
			id: $formData.id,
			name: $formData.name || '',
			code: $formData.code || '',
			slug: $formData.slug || '',
			url: $formData.url || undefined,
			active: $formData.active ?? true,
			numberOfLicenses: $formData.numberOfLicenses ?? null,
			countryCode: $formData.countryCode || '',
			gpsLocation: $formData.gpsLocation || '',
			companyId: $formData.companyId || undefined,
			eKaizenFormURL: $formData.eKaizenFormURL || '',
			tasksEnabled: $formData.tasksEnabled ?? false
		};
		const original = {
			id: originalData.id,
			name: originalData.name || '',
			code: originalData.code || '',
			slug: originalData.slug || '',
			url: originalData.url || undefined,
			active: originalData.active ?? true,
			numberOfLicenses: originalData.numberOfLicenses ?? null,
			countryCode: originalData.countryCode || '',
			gpsLocation: originalData.gpsLocation || '',
			companyId: originalData.companyId || undefined,
			eKaizenFormURL: originalData.eKaizenFormURL || '',
			tasksEnabled: originalData.tasksEnabled ?? false
		};

		const result = JSON.stringify(current) !== JSON.stringify(original);
		// Pro ladění:
		// console.log('Changes detected:', { current, original, result });
		return result;
	}

	function handleAttemptCancelOrNavigate() {
		if (hasChanges()) {
			showConfirmDialog = true;
		} else {
			onCancel();
		}
	}

	function handleConfirmSave() {
		showConfirmDialog = false;
		const formElement = document.querySelector('form[action="?/updatePlant"]') as HTMLFormElement;
		if (formElement) {
			formElement.requestSubmit();
		}
	}

	export function attemptCancelOrNavigate() {
		handleAttemptCancelOrNavigate();
	}

	let selectedCompany = $state<string | undefined>(plantInfo.company?.id);

	function handleCompanyChange(companyId: string) {
		selectedCompany = companyId;
		$formData.companyId = companyId;
		companyOpen = false;
	}

	onMount(() => {
		// Populate form data from plantInfo
		$formData.id = plantInfo.id;
		$formData.name = plantInfo.name || '';
		$formData.code = plantInfo.code || '';
		$formData.slug = plantInfo.slug || '';
		$formData.numberOfLicenses = plantInfo.numberOfLicenses ?? null;
		$formData.url = plantInfo.url || undefined;
		$formData.active = plantInfo.active ?? true;
		$formData.countryCode = plantInfo.countryCode || '';
		$formData.gpsLocation = plantInfo.gpsLocation || undefined;
		$formData.companyId = plantInfo.company.id || undefined;
		$formData.eKaizenFormURL = plantInfo.eKaizenFormURL || undefined;
		$formData.tasksEnabled = plantInfo.tasksEnabled ?? false;

		originalData = {
			id: plantInfo.id,
			name: plantInfo.name || '',
			code: plantInfo.code || '',
			active: plantInfo.active ?? true,
			slug: plantInfo.slug || '',
			numberOfLicenses: plantInfo.numberOfLicenses ?? null,
			url: plantInfo.url || undefined,
			countryCode: plantInfo.countryCode || '',
			gpsLocation: plantInfo.gpsLocation || undefined,
			companyId: plantInfo.company.id || undefined,
			eKaizenFormURL: plantInfo.eKaizenFormURL || undefined,
			tasksEnabled: plantInfo.tasksEnabled ?? false
		};
	});
</script>

<div>
	<form method="POST" action="?/updatePlant" use:enhance class="flex flex-col gap-6">
		<input type="hidden" name="id" value={$formData.id} />

		<!-- Name and Code row -->
		<div class="h-[85px]">
			<Form.Field {form} name="name">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.plants.form.name')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.name}
							placeholder={$t('admin.plants.form.namePlaceholder')}
							class={`h-10 bg-white ${$errors.name ? 'border-red-500' : ''}`}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="grid grid-cols-2 gap-4">
			<div class="h-[85px]">
				<Form.Field {form} name="code">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium">{$t('admin.plants.form.code')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.code}
								placeholder={$t('admin.plants.form.codePlaceholder')}
								class={`h-10 bg-white ${$errors.code ? 'border-red-500' : ''}`}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
				</Form.Field>
			</div>

			<div class="h-[85px]">
				<Form.Field {form} name="slug">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium">{$t('admin.plants.form.slug')}</Form.Label>
							<Input
								{...props}
								bind:value={$formData.slug}
								placeholder={$t('admin.plants.form.slugPlaceholder')}
								class={`h-10 bg-white ${$errors.slug ? 'border-red-500' : ''}`}
								oninput={(e) => {
									const target = e.target as HTMLInputElement | null;
									if (target) {
										$formData.slug = target.value.replace(/\s+/g, '');
									}
								}}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
				</Form.Field>
			</div>
		</div>

		<!-- Company -->
		<div class="relative pb-6">
			<Form.Field {form} name="companyId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label class="text-sm font-medium">{$t('admin.plants.form.company')}</Form.Label>
						<Popover.Root bind:open={companyOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'items-center justify-between',
									!$formData.companyId && 'text-muted-foreground',
									$errors.companyId && 'border-red-500'
								)}
								role="combobox"
								{...props}
							>
								{#if $formData.companyId && companies}
									{companies.find((c: { id: string }) => c.id === $formData.companyId)?.name ||
										$t('admin.plants.form.selectCompany')}
								{:else}
									{$t('admin.plants.form.selectCompany')}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.companyId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root>
									<Command.Input placeholder={$t('admin.plants.form.searchCompany')} />
									<Command.List>
										<Command.Empty>{$t('admin.plants.form.noCompanyFound')}</Command.Empty>
										<Command.Group>
											{#each companies as company}
												<Command.Item
													value={company.id}
													onSelect={() => handleCompanyChange(company.id)}
												>
													<Check
														class={cn(
															'mr-2 h-4 w-4',
															$formData.companyId === company.id ? 'opacity-100' : 'opacity-0'
														)}
													/>
													{company.name}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
			</Form.Field>
		</div>

		<div class="grid grid-cols-2 gap-4">
			<div class="h-[85px]">
				<Form.Field {form} name="countryCode">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium"
								>{$t('admin.plants.form.countryCode')}</Form.Label
							>
							<Input
								{...props}
								bind:value={$formData.countryCode}
								placeholder={$t('admin.plants.form.countryCodePlaceholder')}
								class={`h-10 bg-white ${$errors.countryCode ? 'border-red-500' : ''}`}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
				</Form.Field>
			</div>

			<div class="h-[85px]">
				<Form.Field {form} name="gpsLocation">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium"
								>{$t('admin.plants.form.gpsLocation')}</Form.Label
							>
							<Input
								{...props}
								bind:value={$formData.gpsLocation}
								placeholder={$t('admin.plants.form.gpsLocationPlaceholder')}
								class={`h-10 bg-white ${$errors.gpsLocation ? 'border-red-500' : ''}`}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
				</Form.Field>
			</div>
		</div>

		<div class="relative flex h-[85px] pb-6">
			<Form.Field {form} name="active" class="w-full">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="mx-2 mt-4 flex flex-1 flex-row items-center gap-4">
							<Switch
								bind:checked={$formData.active}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out ',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.active ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.active
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.active
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<span class="text-xs whitespace-pre-line text-gray-600">
								{$t('admin.plants.form.statusDescription')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<!-- Licenses and GPS row -->
		<div class="grid grid-cols-2 gap-4">
			<div class="h-[85px]">
				<Form.Field {form} name="numberOfLicenses">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium"
								>{$t('admin.plants.form.numberOfLicenses')}</Form.Label
							>
							<Input
								{...props}
								type="number"
								bind:value={$formData.numberOfLicenses}
								placeholder={$t('admin.plants.form.numberOfLicensesPlaceholder')}
								class={`h-10 bg-white ${$errors.numberOfLicenses ? 'border-red-500' : ''}`}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
				</Form.Field>
			</div>
			<div class="h-[85px]">
				<Form.Field {form} name="url">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium">{$t('admin.plants.form.url')}</Form.Label>
							<Input
								{...props}
								type="url"
								bind:value={$formData.url}
								placeholder={$t('admin.plants.form.urlPlaceholder')}
								class={`h-10 bg-white ${$errors.url ? 'border-red-500' : ''}`}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors class="mt-1 text-right text-xs text-red-600" />
				</Form.Field>
			</div>
		</div>

		<!-- URL row -->

		<div class="relative flex h-[85px] pb-6">
			<Form.Field {form} name="tasksEnabled" class="w-full">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('admin.plants.form.tasksEnabled')}</Form.Label>
						<div class="mx-2 mt-4 flex flex-1 flex-row items-center gap-4">
							<Switch
								bind:checked={$formData.tasksEnabled}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out ',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.tasksEnabled ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.tasksEnabled
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.tasksEnabled
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<span class="text-xs whitespace-pre-line text-gray-600">
								{$t('admin.plants.form.tasksDescription')}
							</span>

							<input type="hidden" name={props.name} value={$formData.tasksEnabled.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<div class="mt-4 flex justify-end gap-4">
				<Button
					type="button"
					class="font-titillium min-w-[120px] rounded-xl bg-[#B1B7C3] p-7 text-sm font-semibold text-white uppercase hover:bg-[#B1B7C3]/80"
					onclick={handleAttemptCancelOrNavigate}
				>
					{$t('common.buttons.back')}
				</Button>

				<Form.Button
					type="submit"
					class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				>
					{$t('common.buttons.save')}
				</Form.Button>
			</div>
		</div>
	</form>
</div>

<ConfirmDialog
	bind:open={showConfirmDialog}
	title={$t('common.dialogs.confirm.title')}
	description={$t('common.dialogs.confirm.text')}
	confirmButtonText={$t('common.buttons.save')}
	onClose={() => {
		showConfirmDialog = false;
		onCancel();
	}}
	onConfirm={handleConfirmSave}
/>
