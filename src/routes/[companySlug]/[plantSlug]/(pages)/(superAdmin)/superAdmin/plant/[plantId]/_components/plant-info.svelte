<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import type { PlantsListDTO } from '$lib/DTO/admin/plants/PlantsListDTO';
	import { locale, t } from '$lib/translations';
	import { formatDate } from '$lib/utils/date';
	import { fly } from 'svelte/transition';

	let { plantInfo } = $props();
</script>

<div class="flex flex-col justify-center" in:fly={{ duration: 300 }}>
	<div class="my-8 grid grid-cols-2 gap-2 gap-y-6 text-base lg:gap-6 lg:gap-y-10">
		{@render plantInfoFormat($t('admin.plants.form.company'), plantInfo.company?.name ?? 'N/A')}

		{@render plantInfoFormat($t('admin.plants.form.code'), plantInfo.code)}
		{@render plantInfoFormat($t('admin.plants.form.slug'), plantInfo.slug)}
		{@render plantInfoFormat($t('admin.plants.form.url'), plantInfo.url ?? 'N/A')}

		<div class="flex flex-col overflow-hidden text-left">
			<span class="-py-0.5 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
				>{$t('admin.plants.form.status')}</span
			>
			<span class="truncate text-sm lg:text-lg">
				<Badge
					class={`font-titillium rounded-md px-2 py-0.5 text-xs font-light text-white uppercase lg:px-6 lg:py-1 lg:text-xs ${
						plantInfo.active ? 'bg-[#414E6B] ' : 'bg-secondary '
					}`}
				>
					{plantInfo.active
						? $t('auditTypes.list.activeStates.true')
						: $t('auditTypes.list.activeStates.false')}
				</Badge>
			</span>
		</div>
		{@render plantInfoFormat(
			$t('admin.plants.form.numberOfLicenses'),
			plantInfo.numberOfLicenses !== undefined && plantInfo.numberOfLicenses !== null
				? String(plantInfo.numberOfLicenses)
				: 'N/A'
		)}

		{@render plantInfoFormat($t('admin.plants.form.countryCode'), plantInfo.countryCode)}
		{@render plantInfoFormat($t('admin.plants.form.gpsLocation'), plantInfo.gpsLocation ?? 'N/A')}

		{@render plantInfoFormat(
			$t('admin.plants.form.eKaizenFormURL'),
			plantInfo.eKaizenFormURL ?? 'N/A'
		)}

		<div class="flex flex-col overflow-hidden text-left">
			<span class="-py-0.5 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
				>{$t('admin.plants.form.tasksEnabled')}</span
			>
			<span class="truncate text-sm lg:text-lg">
				<Badge
					class={`font-titillium rounded-md px-2 py-0.5 text-xs font-light text-white uppercase lg:px-6 lg:py-1 lg:text-xs ${
						plantInfo.tasksEnabled ? 'bg-[#414E6B] ' : 'bg-secondary '
					}`}
				>
					{plantInfo.tasksEnabled
						? $t('auditTypes.list.activeStates.true')
						: $t('auditTypes.list.activeStates.false')}
				</Badge>
			</span>
		</div>

		{@render plantInfoFormat(
			$t('admin.plants.form.createdAt'),
			formatDate(plantInfo.createdAt?.toISOString(), $locale)
		)}
		{@render plantInfoFormat(
			$t('admin.plants.form.updatedAt'),
			formatDate(plantInfo.updatedAt?.toISOString(), $locale)
		)}
	</div>
</div>

{#snippet plantInfoFormat(name: string, content: string, nameClass?: string, contentClass?: string)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class={`truncate text-xs text-[#747C8A] lg:text-sm ${nameClass}`}>{name}</span>
		<span class={`truncate text-sm break-words text-[#4B505A] lg:text-lg ${contentClass}`}
			>{content}</span
		>
	</div>
{/snippet}
