import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createSuperAdminPlantBreadcrumbs(
	currentCompanySlug: string,
	currentPlantSlug: string,
	plantName?: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		},
		{
			kind: 'static',
			label: 'admin.adminPanel.title',
			href: `/${currentCompanySlug}/${currentPlantSlug}/superAdmin`
		}
	];

	if (plantName) {
		breadcrumbs.push({
			kind: 'dynamic',
			label: plantName
		});
	}

	return breadcrumbs;
}
