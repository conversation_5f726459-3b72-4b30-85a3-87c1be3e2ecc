import {
	SuperAdminCompanyService,
	SuperAdminPlantService,
	SuperAdminService
} from '$lib/server/services/superadmin/index.js';
import { error, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { createSuperAdminUserBreadcrumbs } from './_utils/createBreadcrumbs.js';
import { updateUserSchema } from '$lib/schemas/admin.js';
import { CompanyService, PlantsService } from '$lib/server/services/tenants/index.js';

export const load = async ({ params }) => {
	const userId = params.userId;

	if (!userId) {
		throw error(404, 'User Not Found ');
	}

	const [userInfo, companies, plants, userForm] = await Promise.all([
		SuperAdminService.getUserById(userId),
		SuperAdminCompanyService.getAllCompanies(),
		SuperAdminPlantService.getAllPlants(),

		superValidate(zod(updateUserSchema))
	]);

	return {
		userInfo,
		companies,
		plants,
		userForm,
		breadcrumbs: createSuperAdminUserBreadcrumbs(
			params.companySlug,
			params.plantSlug,
			userInfo?.firstName + ' ' + userInfo?.lastName
		)
	};
};

export const actions = {
	updateUser: async ({ request, locals }) => {
		const form = await superValidate(request, zod(updateUserSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			const result = await SuperAdminService.updateUser(form.data);

			if (result) {
				// Check if user is updating themselves
				if (locals.user && locals.user.id === form.data.id) {
					//Update session
					const [company, plant] = await Promise.all([
						CompanyService.getCompanyById(form.data.companyId!),
						PlantsService.getPlantById(form.data.plantId!)
					]);

					if (company && plant) {
						return {
							form,
							success: true,
							redirectTo: `/${company.slug}/${plant.slug}/dashboard`
						};
					}
				}

				return {
					form,
					success: true
				};
			} else {
				return fail(400, {
					form,
					success: false
				});
			}
		} catch (error) {
			console.error('Error updating user:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	}
};
