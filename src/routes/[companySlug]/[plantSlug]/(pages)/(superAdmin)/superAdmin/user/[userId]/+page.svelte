<script lang="ts">
	import { fade, fly } from 'svelte/transition';
	import type { PageData } from './$types';
	import { locale, t } from '$lib/translations';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import { Ellipsis } from '@lucide/svelte';
	import { formatDate } from '$lib/utils/date';
	import { Badge } from '$lib/components/ui/badge';
	import UserInfo from './_components/user-info.svelte';
	import UserForm from './_components/user-form.svelte';

	let { data } = $props();
	let editInfo = $state(false);
	let userFormRef = $state<{ attemptCancelOrNavigate?: () => void }>();

	function handleEditInfoToggle() {
		if (editInfo && userFormRef?.attemptCancelOrNavigate) {
			userFormRef.attemptCancelOrNavigate();
		} else {
			editInfo = !editInfo;
		}
	}
</script>

<svelte:head>
	<title>LeanAudit - {data.userInfo!.firstName + ' ' + data.userInfo!.lastName}</title>
</svelte:head>

<div in:fade={{ duration: 300 }} class="mt-4 mb-8 flex h-[calc(100vh-10rem)] w-full gap-4">
	<div class="flex h-full w-full flex-col lg:flex-row lg:gap-4">
		<div
			class="flex h-full w-full flex-col rounded-2xl bg-white p-2 lg:flex-row lg:gap-4 lg:rounded-none lg:bg-transparent"
		>
			<!-- Info panel -->
			<div
				class="w-full overflow-y-auto px-4 py-6 sm:px-10 sm:py-8 lg:h-full lg:w-[40%] lg:shrink-0 lg:rounded-2xl lg:bg-white"
			>
				<div class="flex flex-row justify-between">
					{@render userInfo(
						$t('admin.users.info.name'),
						data.userInfo!.firstName + ' ' + data.userInfo!.lastName
					)}
					<ButtonIcon onClickAction={handleEditInfoToggle} Icon={Ellipsis} />
				</div>

				{#if !editInfo}
					<UserInfo userInfo={data.userInfo} />
				{:else}
					<div class="pt-2" in:fly={{ duration: 300 }}>
						<UserForm
							bind:this={userFormRef}
							userInfo={data.userInfo}
							userForm={data.userForm}
							companies={data.companies}
							plants={data.plants}
							onCancel={() => {
								editInfo = false;
							}}
							onSave={() => {
								editInfo = false;
							}}
						/>
					</div>
				{/if}
			</div>

			<!-- Questions panel  -->
			<div class="flex-1 overflow-auto p-2 sm:p-4 lg:rounded-2xl lg:bg-white lg:p-6">
				<div class="flex h-full flex-col">
					<div class="flex justify-center">topbar</div>
					<div class="flex flex-1 items-center justify-center">content</div>
				</div>
			</div>
		</div>
	</div>
</div>

{#snippet userInfo(name: string, content: string)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span class="text-sm break-words text-[#4B505A] lg:text-lg">{content}</span>
	</div>
{/snippet}
