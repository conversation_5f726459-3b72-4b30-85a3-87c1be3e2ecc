<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import { locale, t } from '$lib/translations';
	import { formatDate } from '$lib/utils/date';
	import { fly } from 'svelte/transition';

	let { userInfo } = $props();
</script>

<div class="flex flex-col justify-center" in:fly={{ duration: 300 }}>
	<div class="my-8 grid grid-cols-2 gap-2 gap-y-6 text-base lg:gap-6 lg:gap-y-10">
		{@render userInfoFormat($t('admin.users.info.email'), userInfo.email)}
		{@render userInfoFormat($t('admin.users.info.phone'), userInfo.phone)}
		{@render userInfoFormat($t('admin.users.info.company'), userInfo.company.name)}
		{@render userInfoFormat($t('admin.users.info.mainPlant'), userInfo.mainPlant.name)}

		<div class="flex flex-col overflow-hidden text-left">
			<span class="-py-0.5 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
				>{$t('auditTypes.info.status')}</span
			>
			<span class="truncate text-sm lg:text-lg">
				<Badge
					class={`font-titillium rounded-md px-2 py-0.5 text-xs font-light text-white uppercase lg:px-6 lg:py-1 lg:text-xs ${
						userInfo.active ? 'bg-[#414E6B] ' : 'bg-secondary '
					}`}
				>
					{userInfo.active
						? $t('auditTypes.list.activeStates.true')
						: $t('auditTypes.list.activeStates.false')}
				</Badge>
			</span>
		</div>
		{@render userInfoFormat(
			$t('admin.users.info.cardNumber'),
			userInfo.cardNumber ? userInfo.cardNumber : 'N/A'
		)}
	</div>
</div>

{#snippet userInfoFormat(name: string, content: string)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span class="text-sm break-words text-[#4B505A] lg:text-lg">{content}</span>
	</div>
{/snippet}
