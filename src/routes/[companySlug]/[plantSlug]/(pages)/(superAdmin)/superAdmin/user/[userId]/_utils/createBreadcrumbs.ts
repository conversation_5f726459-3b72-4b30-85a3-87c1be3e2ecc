import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createSuperAdminUserBreadcrumbs(
	currentCompanySlug: string,
	currentPlantSlug: string,
	userName?: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		},
		{
			kind: 'static',
			label: 'admin.adminPanel.title',
			href: `/${currentCompanySlug}/${currentPlantSlug}/superAdmin`
		}
	];

	if (userName) {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'admin.users.title',
				href: `/${currentCompanySlug}/${currentPlantSlug}/superAdmin`
			},
			{
				kind: 'dynamic',
				label: userName
			}
		);
	}

	return breadcrumbs;
}
