import { superValidate } from 'sveltekit-superforms';
import type { LayoutServerLoad } from './$types';
import { createAuditSchema } from '$lib/schemas/audits/audits';
import { zod } from 'sveltekit-superforms/adapters';
import { changePasswordSchema } from '$lib/schemas/auth';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { loadLayoutData } from '$lib/server/services/layout/layoutData';
import { PlantLanguageService } from '$lib/server/services/tenants';

export const load: LayoutServerLoad = async ({ locals, params, parent }) => {
	const { role } = await parent();

	const { plantId, companyId } = await validatePlantCompanyAccessLight(
		params.companySlug!,
		params.plantSlug!,
		locals.user!
	);

	const [layoutData, instanceForm, changePasswordForm, language] = await Promise.all([
		loadLayoutData(plantId),
		superValidate(zod(createAuditSchema)),
		superValidate(zod(changePasswordSchema)),
		PlantLanguageService.getPlantDefaultLanguage(plantId)
	]);

	return {
		user: locals.user,
		role,
		context: { plantId, companyId, plantSlug: params.plantSlug!, companySlug: params.companySlug! },
		createAuditData: {
			auditTypes: layoutData.auditTypes,
			workplaces: layoutData.workplaces,
			auditors: layoutData.auditors,
			instanceForm
		},
		changePasswordForm,
		availableLanguages: layoutData.supportedLanguages,
		plantDefaultLanguage: language
	};
};
