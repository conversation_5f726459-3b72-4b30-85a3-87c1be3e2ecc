<script lang="ts">
	import MainContainer from '$lib/customComponents/main-container.svelte';
	import MobileMenu from '$lib/customComponents/mobile-menu.svelte';
	import Sidebar from '$lib/customComponents/side-bar.svelte';
	import TopBar from '$lib/customComponents/top-bar.svelte';

	import '../../../../app.css';
	let { data, children } = $props();
</script>

<div class="flex h-screen bg-[#101826] md:bg-transparent">
	<div class="hidden md:block">
		<Sidebar />
	</div>

	<MainContainer class="flex w-full flex-col">
		<div class="hidden md:block">
			<TopBar
				createAuditData={data.createAuditData}
				changePasswordForm={data.changePasswordForm}
				languages={data.availableLanguages}
			/>
		</div>

		<MobileMenu />

		{@render children()}
	</MainContainer>
</div>
