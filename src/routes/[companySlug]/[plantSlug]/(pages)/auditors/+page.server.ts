import { AuditorService } from '$lib/server/services/auditors';
import type { PageServerLoad } from './$types';
import { createAuditorsBreadcrumbs } from './_utils/createBreadcrumbs';

export const load: PageServerLoad = async ({ parent }) => {
	const { context } = await parent();
	const auditors = await AuditorService.getAuditors(context.plantId);

	return {
		auditors,
		breadcrumbs: createAuditorsBreadcrumbs()
	};
};
