<script lang="ts">
	import { fade } from 'svelte/transition';
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import { columns } from './_components/columns';
	import { t } from '$lib/translations';
	import SearchInput from '$lib/customComponents/search-input.svelte';

	let { data } = $props();
	let globalFilter = $state('');

	function handleSearch(value: string) {
		globalFilter = value;
	}
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.auditors')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9 ring-2 ring-gray-50"
>
	<div class="mb-6 flex items-center justify-between">
		<div class="flex-1">
			<SearchInput value={globalFilter} onSearch={handleSearch} />
		</div>
	</div>

	<div class="flex-1 overflow-hidden">
		<DataTable data={data.auditors} {columns} {globalFilter} />
	</div>
</div>
