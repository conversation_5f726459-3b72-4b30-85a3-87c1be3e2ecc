import { renderComponent } from '$lib/components/ui/data-table';
import type { ColumnDef } from '@tanstack/table-core';
import { t } from '$lib/translations';
import { get } from 'svelte/store';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import AuditorCell from '$lib/customComponents/tableFormat/auditor-cell.svelte';
import type { AuditorDTO } from '$lib/DTO/auditor';

export const columns: ColumnDef<AuditorDTO>[] = [
	{
		id: 'name',
		accessorFn: (row) => {
			return `${row.name.firstName || ''} ${row.name.lastName || ''}`.trim();
		},
		header: ({ column }) =>
			renderComponent(CustomHeader, {
				text: get(t)('auditors.list.name'),
				column
			}),
		cell: ({ row }) => {
			const auditor = row.original;
			return renderComponent(AuditorCell, {
				firstName: auditor.name.firstName,
				lastName: auditor.name.lastName
			});
		},
		enableSorting: true
	},
	{
		id: 'email',
		accessorKey: 'email',
		header: ({ column }) =>
			renderComponent(CustomHeader, {
				text: get(t)('auditors.list.email'),
				column
			}),
		cell: ({ row }) => {
			const auditor = row.original;
			return auditor.email || '-';
		},
		enableSorting: true
	}
];
