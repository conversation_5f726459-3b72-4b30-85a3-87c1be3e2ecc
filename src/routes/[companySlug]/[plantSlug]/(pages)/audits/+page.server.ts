import { fail, type Actions, error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { AuditService, AuditSetupService } from '$lib/server/services/audits';
import { createAuditBreadcrumbs } from './_utils/createBreadcrumbs';
import { AuditTypeService } from '$lib/server/services/auditTypes';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { createAuditSchema } from '$lib/schemas/audits/audits';
import { WorkplaceService } from '$lib/server/services/workplaces';
import { t } from '$lib/translations';
import { mapAuditToListType } from '$lib/DTO/audits/audits';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { getPlantEvaluationConfig } from '$lib/server/services/tenants/plantEvaluationConfig';

export const load: PageServerLoad = async ({ locals, parent }) => {
	if (!locals.user) {
		throw error(401, 'Nepřihlášený uživatel');
	}

	const { context } = await parent();

	try {
		const [audits, auditTypes, workplaces, auditors, instanceForm, plantEvaluationConfig] =
			await Promise.all([
				AuditService.getAuditInstances(locals.user.id, context.plantId),
				AuditTypeService.getAuditTypes(context.plantId, true),
				WorkplaceService.getWorkplaces(context.plantId, true),
				AuditService.getAuditors(context.plantId),
				superValidate(zod(createAuditSchema)),
				getPlantEvaluationConfig(context.plantId)
			]);

		const auditsList = audits.map(mapAuditToListType);

		return {
			audits: auditsList,
			auditTypes,
			workplaces,
			auditors,
			instanceForm,
			plantEvaluationConfig,
			breadcrumbs: createAuditBreadcrumbs(
				undefined,
				undefined,
				undefined,
				context.companySlug,
				context.plantSlug
			)
		};
	} catch (error) {
		console.error('Failed to load audits:', error);
		throw error;
	}
};

export const actions: Actions = {
	createAudit: async ({ request, locals, params }) => {
		if (!locals.user) {
			return fail(401, {
				message: t.get('errors.auth.notAuthenticated')
			});
		}

		const form = await superValidate(request, zod(createAuditSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);

			if (!context.plantId) {
				return fail(404, {
					form,
					message: 'Plant not found'
				});
			}

			const workplace = await WorkplaceService.getWorkplaceById(form.data.workplaceId);
			if (!workplace) {
				return fail(404, {
					form,
					message: 'Workplace not found'
				});
			}

			if (!workplace.plantId || workplace.plantId !== context.plantId) {
				return fail(403, {
					form,
					message: 'Not authorized to this plant'
				});
			}

			const result = await AuditService.createAuditInstance(
				form.data.auditTypeId,
				form.data,
				context.plantId,
				locals.user.id
			);

			if (!result || !result.id) {
				return fail(422, {
					form,
					message: t.get('errors.audits.auditSetup.failedCreatingInstance')
				});
			}

			return { form, success: true };
		} catch (error) {
			console.error('Failed to create audit:', error);
			return fail(422, {
				form,
				message: t.get('errors.audits.auditSetup.failedCreatingInstance')
			});
		}
	},

	updateAudit: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(createAuditSchema));

		if (!form.valid) {
			return fail(400, { form });
		}
		if (!form.data.id) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			// Get plant by slug (case-insensitive)
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);

			if (!context.plantId) {
				return fail(404, {
					form,
					message: 'Plant not found'
				});
			}

			const workplace = await WorkplaceService.getWorkplaceById(form.data.workplaceId);
			if (!workplace || workplace.plantId !== context.plantId) {
				return fail(403, {
					form,
					message: 'Not authorized to this workplace'
				});
			}

			await AuditService.updateAuditInstance(form!.data.id, form.data);

			return {
				form,
				message: t.get('errors.audits.auditSetup.auditUpdated')
			};
		} catch (error) {
			console.error('Failed to update audit:', error);
			return fail(422, {
				form,
				message: t.get('errors.audits.auditSetup.failedUpdate')
			});
		}
	},

	deleteAudit: async ({ request }) => {
		const formData = await request.formData();
		const auditId = formData.get('id') as string;

		try {
			await AuditSetupService.deleteAuditInstance(auditId);
			return { message: t.get('errors.audits.auditSetup.auditDeleted') };
		} catch (error) {
			console.error('Failed to delete audit:', error);
			return fail(422, {
				message: t.get('errors.audits.auditSetup.failedDeleteInst')
			});
		}
	}
};
