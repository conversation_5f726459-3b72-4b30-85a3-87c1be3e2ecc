<script lang="ts">
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import { Plus, Download, List, CalendarFold, Funnel, FunnelX, X } from '@lucide/svelte';
	import { createColumns } from './[id]/_components/columns';
	import { t, locale } from '$lib/translations';
	import { fade, slide } from 'svelte/transition';
	import AuditCalendarView from './[id]/_components/audit-calendar-view.svelte';
	import AuditCardsView from './_components/audit-cards-view.svelte';

	import { createAuditSchema } from '$lib/schemas/audits/audits';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import AuditFilters from '$lib/customComponents/audit-filters.svelte';
	import { useForm } from '$lib/hooks/superformValidation';
	import FormAudit from './_components/form-audit.svelte';
	import { toast } from 'svelte-sonner';
	import type { DateRange } from 'bits-ui';
	import { getLocalTimeZone } from '@internationalized/date';
	import { convertAuditsToCSV, downloadCSV } from '$lib/utils/csvExport';
	import { page } from '$app/state';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';

	let { data } = $props();

	let dialogOpen = $state(false);
	let isEditMode = $state(false);

	let selectedAuditId = $state<string | null>(null);

	let containerWidth = $state();
	let viewMode: 'table' | 'calendar' = $state('table');
	let globalFilter = $state('');
	let filtersOpen = $state(false);
	let activeFilters = $state<{
		auditTypes: string[];
		workplaces: string[];
		auditors: string[];
		statuses: string[];
		plannedDateRange: DateRange | undefined;
		completionDateRange: DateRange | undefined;
	}>({
		auditTypes: [],
		workplaces: [],
		auditors: [],
		statuses: [],
		plannedDateRange: undefined,
		completionDateRange: undefined
	});

	function handleSearch(value: string) {
		globalFilter = value;
	}

	function handleEditAudit(id: string) {
		selectedAuditId = id;
		isEditMode = true;
		instanceForm.reset();
		dialogOpen = true;
	}

	function handleCreateAudit() {
		isEditMode = false;
		selectedAuditId = null;
		instanceForm.reset();
		dialogOpen = true;
	}

	export function openCreateAuditDialog() {
		handleCreateAudit();
	}

	function handleCloseDialog() {
		dialogOpen = false;
		if (!isEditMode) {
			instanceForm.reset();
		}
	}

	const columnsWithCallbacks = createColumns(
		{
			onEdit: handleEditAudit
		},
		page.params.plantSlug,
		page.params.companySlug,
		data.user!.id,
		data.plantEvaluationConfig
	);

	const filterOptions = $derived({
		auditTypes: (() => {
			const auditTypes = new Set<string>();
			data.audits.forEach((audit) => {
				if (audit.auditType?.id && audit.auditType?.name) {
					auditTypes.add(JSON.stringify({ id: audit.auditType.id, name: audit.auditType.name }));
				}
			});
			return Array.from(auditTypes).map((item) => JSON.parse(item as string));
		})(),
		workplaces: (() => {
			const workplaces = new Set<string>();
			data.audits.forEach((audit) => {
				if (audit.workplace?.id && audit.workplace?.name) {
					workplaces.add(JSON.stringify({ id: audit.workplace.id, name: audit.workplace.name }));
				}
			});
			return Array.from(workplaces).map((item) => JSON.parse(item as string));
		})(),
		auditors: (() => {
			const auditors = new Set<string>();
			data.audits.forEach((audit) => {
				if (audit.auditor?.id && (audit.auditor.firstName || audit.auditor.lastName)) {
					const fullName =
						`${audit.auditor.firstName || ''} ${audit.auditor.lastName || ''}`.trim();
					auditors.add(JSON.stringify({ id: audit.auditor.id, name: fullName }));
				}
			});
			return Array.from(auditors).map((item) => JSON.parse(item as string));
		})()
	});
	function getAuditStatus(audit: any): string {
		if (audit.progress === -1) {
			return 'late';
		} else if (audit.completionDate && audit.successRate !== undefined) {
			return 'closed';
		} else if (audit.progress === 100) {
			return 'completed';
		} else if (audit.progress > 0) {
			return 'inProgress';
		} else {
			return 'planned';
		}
	}

	const filteredAudits = $derived.by(() => {
		let filtered = data.audits;
		if (activeFilters.auditTypes.length > 0) {
			filtered = filtered.filter(
				(audit) => audit.auditType?.id && activeFilters.auditTypes.includes(audit.auditType.id)
			);
		}

		if (activeFilters.workplaces.length > 0) {
			filtered = filtered.filter(
				(audit) => audit.workplace?.id && activeFilters.workplaces.includes(audit.workplace.id)
			);
		}

		if (activeFilters.auditors.length > 0) {
			filtered = filtered.filter(
				(audit) => audit.auditor?.id && activeFilters.auditors.includes(audit.auditor.id)
			);
		}

		if (activeFilters.statuses.length > 0) {
			filtered = filtered.filter((audit) => {
				const auditStatus = getAuditStatus(audit);
				return activeFilters.statuses.includes(auditStatus);
			});
		}

		// Planned Date range filtering
		if (activeFilters.plannedDateRange && activeFilters.plannedDateRange.start) {
			filtered = filtered.filter((audit) => {
				if (!audit.plannedDate || !activeFilters.plannedDateRange?.start) return false;

				const startDate = activeFilters.plannedDateRange.start.toDate(getLocalTimeZone());
				const endDate = activeFilters.plannedDateRange.end
					? activeFilters.plannedDateRange.end.toDate(getLocalTimeZone())
					: startDate;

				const plannedDate = new Date(audit.plannedDate);

				return (
					plannedDate >= startDate &&
					plannedDate <= new Date(endDate.getTime() + 24 * 60 * 60 * 1000 - 1)
				);
			});
		}

		// Completion Date range filtering
		if (activeFilters.completionDateRange && activeFilters.completionDateRange.start) {
			filtered = filtered.filter((audit) => {
				if (!audit.completionDate || !activeFilters.completionDateRange?.start) return false;

				const startDate = activeFilters.completionDateRange.start.toDate(getLocalTimeZone());
				const endDate = activeFilters.completionDateRange.end
					? activeFilters.completionDateRange.end.toDate(getLocalTimeZone())
					: startDate;

				const completionDate = new Date(audit.completionDate);

				return (
					completionDate >= startDate &&
					completionDate <= new Date(endDate.getTime() + 24 * 60 * 60 * 1000 - 1)
				);
			});
		}

		return filtered;
	});

	function handleFiltersChange(filters: {
		auditTypes: string[];
		workplaces: string[];
		auditors: string[];
		statuses: string[];
		plannedDateRange?: DateRange;
		completionDateRange?: DateRange;
	}) {
		activeFilters.auditTypes = filters.auditTypes;
		activeFilters.workplaces = filters.workplaces;
		activeFilters.auditors = filters.auditors;
		activeFilters.statuses = filters.statuses;
		activeFilters.plannedDateRange = filters.plannedDateRange;
		activeFilters.completionDateRange = filters.completionDateRange;
	}

	function handleFiltersClear() {
		activeFilters.auditTypes = [];
		activeFilters.workplaces = [];
		activeFilters.auditors = [];
		activeFilters.statuses = [];
		activeFilters.plannedDateRange = undefined;
		activeFilters.completionDateRange = undefined;
	}

	function handleExportCSV() {
		try {
			const csvData = convertAuditsToCSV(filteredAudits, $locale);
			const today = new Date().toISOString().split('T')[0];
			const filename = `audits-export-${today}.csv`;
			downloadCSV(csvData, filename);
			toast.success($t('audits.messages.exportSuccess'));
		} catch (error) {
			console.error('CSV Export Error:', error);
			toast.error($t('audits.messages.exportError'));
		}
	}

	// Derived for active filters count and display
	const activeFiltersCount = $derived(() => {
		return (
			activeFilters.auditTypes.length +
			activeFilters.workplaces.length +
			activeFilters.auditors.length +
			activeFilters.statuses.length +
			(activeFilters.plannedDateRange && activeFilters.plannedDateRange.start ? 1 : 0) +
			(activeFilters.completionDateRange && activeFilters.completionDateRange.start ? 1 : 0)
		);
	});

	// Helper function to format date for display
	function formatDateForDisplay(dateRange: DateRange | undefined): string {
		if (!dateRange || !dateRange.start) return '';

		try {
			const startDate = dateRange.start.toDate(getLocalTimeZone());
			const endDate = dateRange.end?.toDate(getLocalTimeZone());

			const formatter = new Intl.DateTimeFormat($locale, {
				day: '2-digit',
				month: '2-digit',
				year: 'numeric'
			});

			if (endDate && startDate.getTime() !== endDate.getTime()) {
				return `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
			} else {
				return formatter.format(startDate);
			}
		} catch (error) {
			return '';
		}
	}

	// Helper function to get readable filter names
	function getFilterDisplayText(filterType: string, values: string[]): string {
		const options = filterOptions[filterType as keyof typeof filterOptions];
		return values
			.map((value) => {
				const option = options.find((opt) => opt.id === value);
				return option?.name || value;
			})
			.join(', ');
	}

	// Clear individual filter functions
	function clearAuditTypesFilter() {
		activeFilters.auditTypes = [];
		handleFiltersChange(activeFilters);
	}

	function clearWorkplacesFilter() {
		activeFilters.workplaces = [];
		handleFiltersChange(activeFilters);
	}

	function clearAuditorsFilter() {
		activeFilters.auditors = [];
		handleFiltersChange(activeFilters);
	}

	function clearStatusesFilter() {
		activeFilters.statuses = [];
		handleFiltersChange(activeFilters);
	}

	function clearPlannedDateFilter() {
		activeFilters.plannedDateRange = undefined;
		handleFiltersChange(activeFilters);
	}

	function clearCompletionDateFilter() {
		activeFilters.completionDateRange = undefined;
		handleFiltersChange(activeFilters);
	}

	let instanceForm = useForm(data.instanceForm, createAuditSchema, 'form', () => {
		dialogOpen = false;
		toast.success($t('audits.actionMessages.auditCreated'));
	});
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.overview')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-5 w-full rounded-2xl bg-white px-4 py-6 md:h-[calc(100vh-8rem)] md:overflow-hidden md:px-8 md:py-9 {filtersOpen
		? 'min-h-[calc(100vh-8rem)] overflow-y-auto'
		: 'h-[calc(100vh-8rem)] overflow-hidden'}"
>
	<div
		class="flex flex-col p-1 md:h-full md:overflow-hidden md:p-2 {filtersOpen
			? 'min-h-full'
			: 'h-full overflow-hidden'}"
	>
		<div class="flex shrink-0 items-center justify-between gap-2 pb-4">
			<div class="max-w-md flex-1">
				<SearchInput value={globalFilter} onSearch={handleSearch} />
			</div>

			<!-- Active Filters Display -->
			{#if activeFiltersCount() > 0}
				<div class="hidden max-w-lg flex-1 px-4 md:block">
					<div class="grid grid-cols-1 gap-x-3 gap-y-1 text-xs sm:grid-cols-2 lg:grid-cols-3">
						{#if activeFilters.auditTypes.length > 0}
							<div class="flex min-w-0 items-center gap-1">
								<span class="flex-shrink-0 text-gray-600">{$t('audits.auditList.auditType')}:</span>
								<span class="truncate font-medium text-gray-800">
									{(() => {
										const text = getFilterDisplayText('auditTypes', activeFilters.auditTypes);
										return text.length > 12 ? text.slice(0, 12) + '...' : text;
									})()}
								</span>
								<button
									type="button"
									onclick={clearAuditTypesFilter}
									class="flex-shrink-0 text-red-500 hover:text-red-700"
									aria-label="Clear audit types filter"
								>
									<X class=" h-3 w-3 cursor-pointer" />
								</button>
							</div>
						{/if}

						{#if activeFilters.workplaces.length > 0}
							<div class="flex min-w-0 items-center gap-1">
								<span class="flex-shrink-0 text-gray-600">{$t('audits.auditList.workplace')}:</span>
								<span class="truncate font-medium text-gray-800">
									{(() => {
										const text = getFilterDisplayText('workplaces', activeFilters.workplaces);
										return text.length > 12 ? text.slice(0, 12) + '...' : text;
									})()}
								</span>
								<button
									type="button"
									onclick={clearWorkplacesFilter}
									class="flex-shrink-0 text-red-500 hover:text-red-700"
									aria-label="Clear workplaces filter"
								>
									<X class="h-3 w-3 cursor-pointer" />
								</button>
							</div>
						{/if}

						{#if activeFilters.auditors.length > 0}
							<div class="flex min-w-0 items-center gap-1">
								<span class="flex-shrink-0 text-gray-600">{$t('audits.auditList.auditor')}:</span>
								<span class="truncate font-medium text-gray-800">
									{(() => {
										const text = getFilterDisplayText('auditors', activeFilters.auditors);
										return text.length > 12 ? text.slice(0, 12) + '...' : text;
									})()}
								</span>
								<button
									type="button"
									onclick={clearAuditorsFilter}
									class="flex-shrink-0 text-red-500 hover:text-red-700"
									aria-label="Clear auditors filter"
								>
									<X class="h-3 w-3 cursor-pointer" />
								</button>
							</div>
						{/if}

						{#if activeFilters.statuses.length > 0}
							<div class="flex min-w-0 items-center gap-1">
								<span class="flex-shrink-0 text-gray-600">{$t('audits.auditList.progress')}:</span>
								<span class="truncate font-medium text-gray-800">
									{(() => {
										const text = activeFilters.statuses
											.map((status) => $t(`audits.progress.${status}`))
											.join(', ');
										return text.length > 12 ? text.slice(0, 12) + '...' : text;
									})()}
								</span>
								<button
									type="button"
									onclick={clearStatusesFilter}
									class="flex-shrink-0 text-red-500 hover:text-red-700"
									aria-label="Clear statuses filter"
								>
									<X class="h-3 w-3 cursor-pointer" />
								</button>
							</div>
						{/if}

						{#if activeFilters.plannedDateRange && activeFilters.plannedDateRange.start}
							<div class="flex min-w-0 items-center gap-1">
								<span class="flex-shrink-0 text-gray-600"
									>{$t('audits.auditList.plannedDateRange')}:</span
								>
								<span class="truncate font-medium text-gray-800">
									{formatDateForDisplay(activeFilters.plannedDateRange)}
								</span>
								<button
									type="button"
									onclick={clearPlannedDateFilter}
									class="flex-shrink-0 text-red-500 hover:text-red-700"
									aria-label="Clear planned date filter"
								>
									<X class="h-3 w-3 cursor-pointer" />
								</button>
							</div>
						{/if}

						{#if activeFilters.completionDateRange && activeFilters.completionDateRange.start}
							<div class="flex min-w-0 items-center gap-1">
								<span class="flex-shrink-0 text-gray-600"
									>{$t('audits.auditList.completionDateRange')}:</span
								>
								<span class="truncate font-medium text-gray-800">
									{formatDateForDisplay(activeFilters.completionDateRange)}
								</span>
								<button
									type="button"
									onclick={clearCompletionDateFilter}
									class="flex-shrink-0 text-red-500 hover:text-red-700"
									aria-label="Clear completion date filter"
								>
									<X class="h-3 w-3 cursor-pointer" />
								</button>
							</div>
						{/if}
					</div>
				</div>
			{/if}

			<div class="flex items-center gap-2">
				<ButtonIcon
					Icon={Funnel}
					onClickAction={() => (filtersOpen = !filtersOpen)}
					backgroundColor="#2E384F"
					hoverBackgroundColor="#2E384F"
					hoverOpacity="80"
				/>

				<ButtonIcon
					class="hidden md:block"
					Icon={FunnelX}
					onClickAction={handleFiltersClear}
					backgroundColor="#D18385"
					hoverBackgroundColor="#D18385"
					hoverOpacity="80"
				/>

				<div class="bg-secondary hidden rounded-xl md:flex">
					<ButtonIcon
						Icon={CalendarFold}
						onClickAction={() => (viewMode = 'calendar')}
						backgroundColor={viewMode === 'calendar' ? '#2E384F' : '#B1B7C3'}
						hoverBackgroundColor="#2E384F"
					/>

					<ButtonIcon
						Icon={List}
						onClickAction={() => (viewMode = 'table')}
						backgroundColor={viewMode === 'table' ? '#2E384F' : '#B1B7C3'}
						hoverBackgroundColor="#2E384F"
					/>
				</div>

				<ButtonIcon
					Icon={Download}
					onClickAction={handleExportCSV}
					backgroundColor="#B1B7C3"
					hoverBackgroundColor="#B1B7C3"
					hoverOpacity="80"
				/>

				<ButtonIcon Icon={Plus} onClickAction={handleCreateAudit} />
			</div>
		</div>

		<!-- Filters Section -->
		{#if filtersOpen}
			<div
				class="mb-4 max-h-[60vh] shrink-0 overflow-y-auto md:max-h-none md:overflow-hidden"
				transition:slide={{ duration: 300 }}
			>
				<AuditFilters
					auditTypes={filterOptions.auditTypes}
					workplaces={filterOptions.workplaces}
					auditors={filterOptions.auditors}
					audits={data.audits}
					bind:filters={activeFilters}
					onFiltersChange={handleFiltersChange}
				/>
			</div>
		{/if}

		<div class="relative flex-1 overflow-hidden" bind:clientWidth={containerWidth}>
			<div class="hidden md:block">
				<div
					class="absolute flex h-full w-[200%] transition-transform duration-300 ease-in-out"
					style="transform: translateX({viewMode === 'table' ? '0' : `-${containerWidth}px`})"
				>
					<div class="flex h-full w-1/2 flex-col justify-between">
						<div class="flex-1 overflow-auto">
							<DataTable data={filteredAudits} columns={columnsWithCallbacks} {globalFilter} />
						</div>
					</div>
					<div class="h-full w-1/2">
						<AuditCalendarView audits={filteredAudits} {globalFilter} />
					</div>
				</div>
			</div>

			<div class="flex h-full flex-col md:hidden">
				<div class="flex-1 overflow-auto">
					<AuditCardsView
						audits={filteredAudits}
						{globalFilter}
						plantEvaluationConfig={data.plantEvaluationConfig}
					/>
				</div>
			</div>
		</div>
	</div>
</div>

<CustomDialog
	open={dialogOpen}
	title={isEditMode ? $t('audits.dialogs.editTitle') : $t('audits.newInstance.title')}
	onClose={handleCloseDialog}
>
	<FormAudit
		form={instanceForm}
		audits={data.audits}
		auditors={data.auditors}
		auditTypes={data.auditTypes}
		workplaces={data.workplaces}
		onClose={handleCloseDialog}
		{isEditMode}
		{selectedAuditId}
	/>
</CustomDialog>
