<script lang="ts">
	import type { TemplateQuestion } from '$lib/schemas/audits/auditQuestions';

	let { categories, activeCategory, onCategorySelect, questions, answers, categoryIds } = $props<{
		categories: string[];
		activeCategory: number;
		onCategorySelect: (index: number) => void;
		questions: Record<string, TemplateQuestion[]>;
		answers: Record<string, any>;
		categoryIds?: string[];
	}>();

	function getCategoryProgress(categoryIndex: number) {
		const categoryKey = categoryIds ? categoryIds[categoryIndex] : categories[categoryIndex];
		const categoryQuestions = questions[categoryKey] || [];
		if (categoryQuestions.length === 0) return 0;

		const answeredCount = categoryQuestions.filter((q: { questionId: string }) => {
			const answer = answers[q.questionId];
			return (
				answer &&
				answer.evaluationValue !== null &&
				answer.evaluationValue !== '' &&
				answer.evaluationValue !== 'NotVisited'
			);
		}).length;
		return (answeredCount / categoryQuestions.length) * 100;
	}

	function getCategoryAbbreviation(category: string) {
		const colonIndex = category.indexOf(':');
		if (colonIndex !== -1) {
			return category.substring(0, colonIndex).trim();
		}

		return category;
	}

	let progressWidth = $derived(
		activeCategory === categories.length - 1
			? 100
			: (activeCategory / (categories.length - 1)) * 100
	);
</script>

<div
	class="relative flex h-16 w-full items-center {categories.length === 1
		? 'justify-center'
		: 'justify-between'} px-2 sm:px-8"
>
	{#if categories.length > 1}
		<!-- Base line -->
		<div
			class="absolute inset-x-2 top-1/2 h-[2px] -translate-y-1/2 bg-[#EAEDF5] sm:inset-x-8"
		></div>

		<!--- Progress line -->
		<div class="absolute top-1/2 right-2 left-2 h-[2px] -translate-y-1/2 sm:right-8 sm:left-8">
			<div
				class="h-full bg-[#697691] transition-all duration-500 ease-in-out"
				style="width: {activeCategory === 0 ? 0 : progressWidth}%"
			></div>
		</div>
	{/if}

	{#each categories as category, index}
		<button
			class="relative z-10 flex h-8 cursor-pointer items-center justify-center overflow-hidden rounded-xl transition-all duration-300 ease-in-out sm:h-12
                {index === activeCategory ? 'w-24 sm:w-32' : 'w-8 sm:w-12'}"
			onclick={() => onCategorySelect(index)}
			title={category}
		>
			<!-- Active background -->
			{#if index === activeCategory}
				<div class="absolute inset-0 -z-8 bg-[#697691]"></div>
			{/if}

			<!-- Base background -->
			<div class="absolute inset-0 -z-10 bg-[#EAEDF5]"></div>

			<!-- Progress background -->
			<div
				class="absolute inset-0 -z-5 bg-[#414E6B] transition-all duration-300"
				style="width: {getCategoryProgress(index)}%"
			></div>

			<!-- Text -->
			<div class="flex h-full w-full items-center justify-center">
				<span
					class="z-20 text-xs font-medium whitespace-nowrap sm:text-sm
                    {getCategoryProgress(index) > 60 || index === activeCategory
						? 'text-white'
						: 'text-[#697691]'}"
				>
					{getCategoryAbbreviation(category)}
				</span>
			</div>
		</button>
	{/each}
</div>
