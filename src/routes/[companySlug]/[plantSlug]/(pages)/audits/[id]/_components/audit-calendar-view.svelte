<script lang="ts">
	import { locale, t } from '$lib/translations';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { ChevronLeft, ChevronRight } from '@lucide/svelte';
	import * as Popover from '$lib/components/ui/popover';
	import type { AuditListDTO } from '$lib/DTO/audits/audits';
	import Button from '$lib/components/ui/button/button.svelte';
	import { page } from '$app/state';

	// Props
	let { audits, globalFilter = '' } = $props<{ audits: AuditListDTO[]; globalFilter?: string }>();

	// Filtered audits based on global search
	const searchFilteredAudits = $derived(() => {
		if (!globalFilter.trim()) return audits;

		const searchLower = globalFilter.toLowerCase();
		return audits.filter((audit: AuditListDTO) => {
			return (
				audit.code.toLowerCase().includes(searchLower) ||
				getAuditTypeName(audit).toLowerCase().includes(searchLower) ||
				audit.workplace?.name?.toLowerCase().includes(searchLower) ||
				audit.auditor?.firstName?.toLowerCase().includes(searchLower) ||
				audit.auditor?.lastName?.toLowerCase().includes(searchLower)
			);
		});
	});

	// Calendar state
	const today = new Date();
	let currentMonth = $state(today.getMonth());
	let currentYear = $state(today.getFullYear());
	let calendarDays = $state<Array<{ date: Date; isCurrentMonth: boolean }>>([]);

	// Get days of week based on locale
	function getDaysOfWeek(): string[] {
		const weekdays = [];
		for (let i = 1; i <= 7; i++) {
			const date = new Date(2024, 0, i); // Using a known Monday
			weekdays.push(date.toLocaleDateString($locale, { weekday: 'short' }));
		}
		return weekdays;
	}

	function isToday(date: Date): boolean {
		return (
			date.getDate() === today.getDate() &&
			date.getMonth() === today.getMonth() &&
			date.getFullYear() === today.getFullYear()
		);
	}

	// Handle audit click
	function handleAuditClick(auditId: string, progress: number, completionDate: string | null) {
		if (completionDate) {
			goto(`/${page.params.companySlug}/${page.params.plantSlug}/audits/${auditId}/results`);
		} else {
			goto(`/${page.params.companySlug}/${page.params.plantSlug}/audits/${auditId}/evaluate`);
		}
	}

	// Get audits for a specific date
	function getAuditsForDate(date: Date) {
		return searchFilteredAudits().filter((audit: AuditListDTO) => {
			if (!audit.plannedDate) return false;

			const auditDate = new Date(audit.plannedDate);
			return (
				auditDate.getDate() === date.getDate() &&
				auditDate.getMonth() === date.getMonth() &&
				auditDate.getFullYear() === date.getFullYear()
			);
		});
	}

	// Generate calendar days
	function generateCalendar(year: number, month: number) {
		const firstDayOfMonth = new Date(year, month, 1);
		const lastDayOfMonth = new Date(year, month + 1, 0);

		let firstDayOfWeek = firstDayOfMonth.getDay();
		firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

		const daysArray = [];

		// Add days from previous month
		const prevMonth = month === 0 ? 11 : month - 1;
		const prevYear = month === 0 ? year - 1 : year;
		const prevMonthLastDay = new Date(prevYear, prevMonth + 1, 0).getDate();

		for (let i = firstDayOfWeek - 1; i >= 0; i--) {
			daysArray.push({
				date: new Date(prevYear, prevMonth, prevMonthLastDay - i),
				isCurrentMonth: false
			});
		}

		// Add days from current month
		for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
			daysArray.push({
				date: new Date(year, month, day),
				isCurrentMonth: true
			});
		}

		// Add days from next month
		const remainingDays = 42 - daysArray.length;
		const nextMonth = month === 11 ? 0 : month + 1;
		const nextYear = month === 11 ? year + 1 : year;

		for (let day = 1; day <= remainingDays; day++) {
			daysArray.push({
				date: new Date(nextYear, nextMonth, day),
				isCurrentMonth: false
			});
		}

		return daysArray;
	}

	// Navigation functions
	function prevMonth() {
		if (currentMonth === 0) {
			currentMonth = 11;
			currentYear -= 1;
		} else {
			currentMonth -= 1;
		}
		calendarDays = generateCalendar(currentYear, currentMonth);
	}

	function nextMonth() {
		if (currentMonth === 11) {
			currentMonth = 0;
			currentYear += 1;
		} else {
			currentMonth += 1;
		}
		calendarDays = generateCalendar(currentYear, currentMonth);
	}

	// Format date for display
	function formatDate(date: Date) {
		return date.toLocaleDateString($locale, { month: 'long', year: 'numeric' });
	}

	// Maximum number of events to show before displaying "+X more"
	const MAX_VISIBLE_EVENTS = 1;

	// Initialize calendar
	onMount(() => {
		calendarDays = generateCalendar(currentYear, currentMonth);
	});

	// Helper function to safely get audit type name
	function getAuditTypeName(audit: AuditListDTO): string {
		if (typeof audit.auditType === 'string') {
			return audit.auditType;
		} else if (
			audit.auditType &&
			typeof audit.auditType === 'object' &&
			'name' in audit.auditType
		) {
			return audit.auditType.name;
		}
		return '';
	}

	function getAuditStatusClass(progress: number, completionDate: string | null): string {
		if (progress === -1) {
			return 'bg-[#D18385]/15 hover:bg-[#D18385]/25';
		} else if (progress === 100 && completionDate) {
			return 'bg-[#90DAB4]/15 hover:bg-[#90DAB4]/25';
		} else if (progress === 100) {
			return 'bg-[#7D9AD3]/15 hover:bg-[#7D9AD3]/25';
		} else if (progress > 0) {
			return 'bg-[#697691]/15 hover:bg-[#697691]/25';
		}
		return 'bg-[#dfd26d]/15 hover:bg-[#dfd26d]/25';
	}

	function getAuditStatusDotClass(progress: number, completionDate: string | null): string {
		if (progress === -1) {
			return 'bg-[#D18385]';
		} else if (progress === 100 && completionDate) {
			return 'bg-[#90DAB4]';
		} else if (progress === 100) {
			return 'bg-[#7D9AD3]';
		} else if (progress > 0) {
			return 'bg-[#697691]';
		}
		return 'bg-[#dfd26d]';
	}

	function getAuditStatusText(progress: number, completionDate: string | null): string {
		if (progress === -1) {
			return $t('audits.progress.late');
		} else if (progress === 100 && completionDate) {
			return $t('audits.progress.closed');
		} else if (progress === 100) {
			return $t('audits.progress.completed');
		} else if (progress > 0) {
			return $t('audits.progress.inProgress').replace('{progress}', progress.toString());
		}
		return $t('audits.progress.planned');
	}

	// Get week number for a date
	function getWeekNumber(date: Date): number {
		const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
		const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
		return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
	}

	// Group calendar days by weeks
	function getCalendarWeeks() {
		const weeks = [];
		for (let i = 0; i < calendarDays.length; i += 7) {
			const week = calendarDays.slice(i, i + 7);
			const weekNumber = getWeekNumber(week[0].date);
			weeks.push({ weekNumber, days: week });
		}
		return weeks;
	}

	// Navigate to current month
	function goToCurrentMonth() {
		currentMonth = today.getMonth();
		currentYear = today.getFullYear();
		calendarDays = generateCalendar(currentYear, currentMonth);
	}
</script>

<div class="flex h-full w-full flex-col overflow-hidden">
	<!-- Calendar header -->
	<div class="shrink-0 px-2">
		<div class="mb-2 flex flex-row items-center justify-between">
			<div class="flex items-center gap-4">
				<h2 class="text-lg font-semibold text-[#6A6A70] capitalize">
					{formatDate(new Date(currentYear, currentMonth, 1))}
				</h2>
				<Button onclick={goToCurrentMonth} size="sm" class="h-6 rounded-md bg-[#697691] text-xs">
					{$t('audits.calendar.showCurrentMonth')}
				</Button>
			</div>
			<div class="flex">
				<Button
					variant="ghost"
					size="sm"
					class="text-lg text-[#6A6A70]"
					onclick={prevMonth}
					title={$t('audits.calendar.previousMonth')}
				>
					<ChevronLeft class="size-3" />
					<span>{$t('audits.calendar.previous')}</span>
				</Button>
				<Button
					variant="ghost"
					size="sm"
					class="text-lg text-[#6A6A70]"
					onclick={nextMonth}
					title={$t('audits.calendar.nextMonth')}
				>
					<span>{$t('audits.calendar.next')}</span>
					<ChevronRight class="size-3" />
				</Button>
			</div>
		</div>
	</div>

	<!-- Calendar grid -->
	<div class="min-h-0 flex-1 px-2 pb-2">
		<!-- Complete calendar grid with header and weeks -->
		<div
			class="grid h-full w-full overflow-hidden rounded-lg border border-gray-200"
			style="grid-template-columns: 60px repeat(7, minmax(0, 1fr)); grid-template-rows: auto repeat(6, minmax(0, 1fr));"
		>
			<!-- Header row -->
			<div class="border-r py-1.5 text-center text-xs font-medium"></div>
			{#each getDaysOfWeek() as day}
				<div
					class="border-r bg-[#414E6B] px-3 py-3 text-left text-lg font-medium text-white capitalize last:border-r-0"
				>
					{day}
				</div>
			{/each}

			<!-- Calendar weeks as grid items -->
			{#each getCalendarWeeks() as { weekNumber, days }}
				<!-- Week number -->
				<div
					class="flex items-center justify-center border-r text-center text-sm font-medium text-gray-500"
				>
					W{weekNumber}
				</div>

				<!-- Days of the week -->
				{#each days as { date, isCurrentMonth }}
					{@const dateAudits = getAuditsForDate(date)}
					{@const visibleAudits = dateAudits.slice(0, MAX_VISIBLE_EVENTS)}
					{@const remainingCount = Math.max(0, dateAudits.length - MAX_VISIBLE_EVENTS)}
					<div
						class="relative flex min-h-0 flex-col overflow-hidden border-r border-b p-1 last:border-r-0 {isCurrentMonth
							? 'bg-white'
							: 'bg-gray-50'}"
					>
						<!-- Day number -->
						<div class="mb-1 shrink-0 text-right">
							<span
								class="inline-flex h-5 w-5 items-center justify-center p-4 font-medium {isToday(
									date
								)
									? 'rounded-md bg-[#414E6B] p-4 text-white'
									: isCurrentMonth
										? 'text-gray-900'
										: 'text-gray-400'}"
							>
								{date.getDate()}
							</span>
						</div>

						<!-- Audits -->
						<div class="min-h-0 flex-1 space-y-0.5 overflow-hidden">
							{#each visibleAudits as audit}
								<button
									class="block w-full cursor-pointer overflow-hidden rounded-md px-2 py-0.5 text-left transition {!isCurrentMonth
										? 'opacity-50'
										: ''} {getAuditStatusClass(audit.progress, audit.completionDate)}"
									title={`${audit.code} - ${getAuditTypeName(audit)} – ${audit.workplace?.name || ''} (${getAuditStatusText(audit.progress, audit.completionDate)})`}
									onclick={() => handleAuditClick(audit.id, audit.progress, audit.completionDate)}
								>
									<div class="flex min-w-0 items-center gap-2 truncate">
										<div
											class="size-2 shrink-0 rounded-full {getAuditStatusDotClass(
												audit.progress,
												audit.completionDate
											)}"
										></div>
										<span class=" text-[11px] font-semibold text-gray-900">{audit.code}</span>
										<span class="truncate text-[9px] text-gray-600">{getAuditTypeName(audit)}</span>
									</div>
								</button>
							{/each}

							{#if remainingCount > 0}
								<Popover.Root>
									<Popover.Trigger class="w-full">
										<div
											class="w-full cursor-pointer truncate rounded-md bg-gray-100 px-1.5 py-0.5 text-center text-[10px] font-medium text-gray-600 transition hover:bg-gray-200 {!isCurrentMonth
												? 'opacity-50'
												: ''}"
										>
											+{remainingCount}
											{$t('audits.calendar.more')}
										</div>
									</Popover.Trigger>
									<Popover.Content class="w-64 p-2" align="center">
										<div class="flex flex-col gap-1">
											<div class="mb-1 border-b pb-1 text-sm font-medium">
												{date.toLocaleDateString($locale, {
													day: 'numeric',
													month: 'long'
												})}
											</div>
											{#each dateAudits as audit}
												<button
													class="flex w-full items-center gap-2 rounded px-2 py-1.5 text-left text-sm transition hover:bg-gray-50"
													onclick={() =>
														handleAuditClick(audit.id, audit.progress, audit.completionDate)}
												>
													<div
														class="size-2 shrink-0 rounded-full {getAuditStatusDotClass(
															audit.progress,
															audit.completionDate
														)}"
													></div>
													<div class="flex-1">
														<div class="font-medium text-gray-900">{audit.code}</div>
														<div class="text-xs text-gray-500">
															{getAuditTypeName(audit)} – {getAuditStatusText(
																audit.progress,
																audit.completionDate
															)}
														</div>
													</div>
												</button>
											{/each}
										</div>
									</Popover.Content>
								</Popover.Root>
							{/if}
						</div>
					</div>
				{/each}
			{/each}
		</div>
	</div>
</div>
