<script lang="ts">
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import Button from '$lib/components/ui/button/button.svelte';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import type { TemplateQuestion, TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
	import type { AttachmentFile } from '$lib/DTO/audits/auditEvaluation';
	import QuestionForm from '../evaluate/_components/question-form.svelte';
	import AttachmentForm from '../evaluate/_components/attachment-form.svelte';
	import { onDestroy, type Component } from 'svelte';
	import { Goal, Loader2, MessageCircleMore, Upload } from '@lucide/svelte';
	import ContactForm from '../evaluate/_components/contact-form.svelte';
	import Stepper from './Stepper.svelte';
	import { AutoSaver, type AutoSaveData } from '$lib/utils/autoSaver';
	import { page } from '$app/state';
	import { debounce } from '$lib/utils/debounce';

	type DialogSection = 'question' | 'attachments' | 'contact';

	let {
		open = $bindable(false),
		code,
		onAnswer,
		onPrevious,
		onNext,
		isFirst,
		isLast,
		onClose,
		onShowConfirm,
		categories = [],
		categoryIds = [],
		currentCategory = 0,
		answers,
		questions,
		question,
		onCategoryChange,
		realDuration,
		auditData = null,
		auditInstanceEvaluationConfig = null
	} = $props<{
		open: boolean;
		code: string;
		onAnswer: (value: string, note: string, files: AttachmentFile[]) => void;
		onPrevious: () => void;
		onNext: () => void;
		isFirst: boolean;
		isLast: boolean;
		onClose: () => void;
		onShowConfirm?: () => void;
		categories: string[];
		categoryIds?: string[];
		currentCategory: number;
		answers: Record<string, any>;
		questions: TemplateQuestions;
		question: TemplateQuestion;
		onCategoryChange: (index: number) => void;
		realDuration: number;
		auditData?: any;
		auditInstanceEvaluationConfig?: any;
	}>();

	//TODO: Refactor entire dialog

	let isSaving = $state(false);
	let selectedValue = $state<string | null>(null);
	let currentNote = $state('');
	let uploadedFiles = $state<AttachmentFile[]>([]);
	let filesToRemove = $state<string[]>([]);
	let currentSection: DialogSection = $state('question');

	// Auto-save state
	let autoSaver = new AutoSaver();
	let retryCount = 0;
	const MAX_RETRIES = 3;
	let isAutoSaving = $state(false);
	let isNavigating = $state(false);

	let currentQuestionId = $state(question.questionId);
	let isFirstEffectRun = $state(true);

	let lastQuestionId: string | null = null;

	let userInteracted = $state(false);

	function handleUserInteraction() {
		userInteracted = true;
	}

	$effect(() => {
		selectedValue = answers[question.questionId]?.evaluationValue ?? null;
		currentNote = answers[question.questionId]?.note ?? '';
	});

	$effect(() => {
		if (open) {
			isFirstEffectRun = true;
		}
	});

	// Reset state only when question actually changes
	$effect(() => {
		if (currentQuestionId !== question.questionId) {
			if (autoSaver.hasChanges(getCurrentAutoSaveData())) {
				performAutoSave();
			}
			uploadedFiles.forEach((file) => {
				if (file.previewUrl) {
					URL.revokeObjectURL(file.previewUrl);
				}
			});
			currentQuestionId = question.questionId;
			uploadedFiles = [];
			filesToRemove = [];
			currentSection = 'question';
			autoSaver.reset();
			retryCount = 0;
			isFirstEffectRun = true;
			const initialData = {
				evaluationValue: answers[question.questionId]?.evaluationValue ?? null,
				note: answers[question.questionId]?.note ?? '',
				attachments: answers[question.questionId]?.files ?? [],
				realDuration: 0
			};
			autoSaver.initializeWithData(initialData);
		}
	});

	// Auto-save API call
	async function performAutoSave(): Promise<void> {
		if (!userInteracted) {
			return;
		}
		const data = getCurrentAutoSaveData();
		try {
			isAutoSaving = true;
			const result = await autoSaver.save(data, async (saveData: AutoSaveData) => {
				const auditId = page.params.id;
				const response = await fetch(`/api/audit/${auditId}/questions/${question.questionId}`, {
					method: 'PUT',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(saveData)
				});
				if (!response.ok) {
					const error = await response.text();
					throw new Error(`[AUTOSAVE] Auto-save failed: ${error}`);
				}
				return response.json();
			});
			if (result) {
				if (result.success) {
					retryCount = 0;
					const finalValue = selectedValue;
					const currentFiles = result.files || answers[question.questionId]?.files || [];
					onAnswer(finalValue, currentNote, currentFiles);
					uploadedFiles = [];
					filesToRemove = [];
					const updatedData = {
						evaluationValue: finalValue,
						note: currentNote,
						attachments: [],
						filesToRemove: [],
						realDuration: realDuration
					};
					autoSaver.initializeWithData(updatedData);

					// success toast
					toast.success($t('audits.auditEvaluation.autoSave.saved'));
				} else {
					throw new Error(result.error || '[AUTOSAVE] Auto-save failed');
				}
			}
		} catch (error) {
			if (retryCount < MAX_RETRIES) {
				retryCount++;
				setTimeout(
					() => {
						performAutoSave();
					},
					1000 * Math.pow(2, retryCount)
				);
			} else {
				toast.error($t('audits.auditEvaluation.autoSave.error'));
			}
		} finally {
			isAutoSaving = false;
		}
	}

	const debouncedAutoSaveNote = debounce(() => {
		performAutoSave();
	}, 1000);

	// Get current data for auto-save
	function getCurrentAutoSaveData(): AutoSaveData {
		return {
			evaluationValue: selectedValue ?? null,
			note: currentNote,
			attachments: uploadedFiles,
			filesToRemove: filesToRemove,
			realDuration: realDuration
		};
	}

	// Auto-save effect - monitors changes to selectedValue and currentNote only
	$effect(() => {
		if (isAutoSaving) return;
		if (!userInteracted) {
			return;
		}

		const watchedQuestionId = question.questionId;

		const isQuestionNavigation = lastQuestionId !== null && lastQuestionId !== watchedQuestionId;
		if (isQuestionNavigation) {
			lastQuestionId = watchedQuestionId;
			isFirstEffectRun = true;
			const currentData = {
				evaluationValue: selectedValue ?? null,
				note: currentNote ?? '',
				attachments: uploadedFiles,
				realDuration: 0
			};
			autoSaver.initializeWithData(currentData);
			if (typeof window !== 'undefined') {
				console.log('[AUTOSAVE] Navigace na novou otázku, inicializuji AutoSaver:', currentData);
			}
			return;
		}

		if (isFirstEffectRun) {
			isFirstEffectRun = false;

			return;
		}

		const currentData = {
			evaluationValue: selectedValue ?? null,
			note: currentNote ?? '',
			attachments: uploadedFiles,
			realDuration: 0
		};

		const hasChanges = autoSaver.hasChanges(currentData);
		if (!hasChanges) {
			return;
		}

		if (hasChanges) {
			const lastData = autoSaver.getLastSavedData();
			const valueChanged = lastData?.evaluationValue !== (selectedValue ?? null);
			const noteChanged = lastData?.note !== (currentNote ?? '');

			if (valueChanged) {
				performAutoSave();
			} else if (noteChanged) {
				// Debounced save for note changes (2s)
				debouncedAutoSaveNote();
			}
		}
	});

	// Cleanup on destroy
	onDestroy(() => {
		// Cleanup any preview URLs
		uploadedFiles.forEach((file) => {
			if (file.previewUrl) {
				URL.revokeObjectURL(file.previewUrl);
			}
		});
	});

	function getCurrentFiles(): AttachmentFile[] {
		const questionId = question.questionId;
		const existingFiles = answers[questionId]?.files || [];

		const filteredExisting = existingFiles.filter(
			(file: AttachmentFile) => !filesToRemove.includes(file.url)
		);

		return [...filteredExisting, ...uploadedFiles];
	}

	function handleFileAdd(file: AttachmentFile) {
		handleUserInteraction();
		const existingFileIndex = uploadedFiles.findIndex((f) => f.filename === file.filename);
		const existingDBFiles = getCurrentFiles();
		const existingInDB = existingDBFiles.some((f) => f.filename === file.filename);

		if (existingFileIndex !== -1 || existingInDB) {
			// File already exists, no need to add again
			return;
		}

		uploadedFiles = [...uploadedFiles, file];

		performAutoSave();
	}

	async function handleFileRemove(index: number) {
		handleUserInteraction();
		const existingFiles = answers[question.questionId]?.files || [];
		const filteredExisting = existingFiles.filter(
			(file: AttachmentFile) => !filesToRemove.includes(file.url)
		);

		if (index < filteredExisting.length) {
			const fileToRemove = filteredExisting[index];

			try {
				const response = await fetch(
					`/api/audit/${page.params.id}/questions/${question.questionId}?attachmentId=${fileToRemove.id}`,
					{
						method: 'DELETE'
					}
				);

				if (!response.ok) {
					throw new Error('Failed to delete attachment');
				}

				const updatedFiles = existingFiles.filter(
					(file: AttachmentFile) => file.id !== fileToRemove.id
				);
				answers = {
					...answers,
					[question.questionId]: {
						...answers[question.questionId],
						files: updatedFiles
					}
				};
			} catch (error) {
				console.error('Error deleting attachment:', error);
				toast.error($t('audits.auditEvaluation.attachments.deleteFailed'));
			}
		} else {
			const newFileIndex = index - filteredExisting.length;
			if (newFileIndex >= 0 && newFileIndex < uploadedFiles.length) {
				const fileToRemove = uploadedFiles[newFileIndex];

				if (fileToRemove.previewUrl) {
					URL.revokeObjectURL(fileToRemove.previewUrl);
				}

				uploadedFiles = uploadedFiles.filter((_, i) => i !== newFileIndex);
				toast.success($t('audits.auditEvaluation.attachments.successfullyDeleted'));
			}
		}
	}

	function handleNextClick() {
		if (isAutoSaving) {
			isNavigating = true;
			waitForAutoSaveAndNavigate(() => {
				if (isLast) {
					onClose();
					if (onShowConfirm) {
						onShowConfirm();
					}
				} else {
					onNext();
				}
			});
		} else {
			if (isLast) {
				onClose();
				if (onShowConfirm) {
					onShowConfirm();
				}
			} else {
				onNext();
			}
		}
	}

	function handlePreviousClick() {
		if (isAutoSaving) {
			// If autosave in progress
			isNavigating = true;
			waitForAutoSaveAndNavigate(() => {
				onPrevious();
			});
		} else {
			onPrevious();
		}
	}

	async function waitForAutoSaveAndNavigate(callback: () => void) {
		const maxWaitTime = 5000;
		const startTime = Date.now();

		while (isAutoSaving && Date.now() - startTime < maxWaitTime) {
			await new Promise((resolve) => setTimeout(resolve, 100));
		}

		isNavigating = false;

		callback();
	}

	function handleSectionClick(section: DialogSection) {
		currentSection = section;
	}

	let formattedQuestions = $derived(
		Object.fromEntries(
			Object.entries(questions || {}).map(([key, category]) => [
				key,
				(category as { questions: any }).questions
			])
		)
	);

	let displayedCategory = $derived.by(() => {
		if (typeof window === 'undefined') {
			// Server-side: return currentCategory to avoid hydration mismatch
			return currentCategory;
		}

		const currentQuestionId = question.questionId;

		for (let i = 0; i < categories.length; i++) {
			const categoryKey = categoryIds ? categoryIds[i] : categories[i];
			const categoryQuestions = formattedQuestions[categoryKey] || [];

			if (categoryQuestions.some((q: any) => q.questionId === currentQuestionId)) {
				return i;
			}
		}

		return currentCategory;
	});
</script>

<CustomDialog
	{open}
	title={code}
	subtitle={$t('audits.auditEvaluation.subtitle')}
	{onClose}
	width="max-w-2xl"
>
	<div class="flex h-full min-h-0 flex-col">
		<!-- Stepper -->
		<div class="shrink-0 px-2 py-3 sm:px-0 sm:pt-4 sm:pb-2">
			<Stepper
				{categories}
				{categoryIds}
				activeCategory={displayedCategory}
				onCategorySelect={onCategoryChange}
				questions={formattedQuestions}
				{answers}
			/>
		</div>

		<div class="flex-1 overflow-y-auto px-2 sm:px-0">
			{#if currentSection === 'attachments'}
				<AttachmentForm
					{question}
					files={getCurrentFiles()}
					onFileAdd={handleFileAdd}
					onFileRemove={handleFileRemove}
					trackUpload={(uploadId) => autoSaver.trackUpload(uploadId)}
					untrackUpload={(uploadId) => autoSaver.untrackUpload(uploadId)}
				/>
			{:else if currentSection === 'question'}
				<QuestionForm
					{question}
					{selectedValue}
					note={currentNote}
					onAnswerChange={(value: string, note: string) => {
						handleUserInteraction();
						selectedValue = value;
						currentNote = note;
					}}
					{auditInstanceEvaluationConfig}
					userInteractedCallback={handleUserInteraction}
				/>

				<!-- Attachment info -->
				{#if getCurrentFiles().length > 0 || uploadedFiles.length > 0}
					<div class="mt-3 rounded-md bg-gray-50 p-2 sm:p-3">
						<div class="text-sm font-medium text-[#4B505A]">
							{$t('audits.auditEvaluation.attachments.attachments')}: {getCurrentFiles().length}
						</div>
					</div>
				{/if}
			{:else if currentSection === 'contact'}
				<ContactForm
					{question}
					auditData={auditData || {}}
					onBack={() => (currentSection = 'question')}
				/>
			{/if}

			{#if currentSection === 'question'}
				<hr class="my-3 border-2 sm:my-4" />

				<div class="mt-2 flex flex-col gap-3 sm:gap-4">
					<div class="flex items-start justify-between">
						<div class="text-base text-[#4B505A]">
							{$t('audits.auditEvaluation.actions.actions')}
						</div>
					</div>
				</div>

				<div class="my-2 flex min-h-0 flex-1 flex-col gap-2">
					{@render evaluationAction(
						$t('audits.auditEvaluation.actions.uploadAttachment'),
						Upload,
						() => handleSectionClick('attachments')
					)}
					{@render evaluationAction(
						$t('audits.auditEvaluation.actions.contactPerson'),
						MessageCircleMore,
						() => handleSectionClick('contact')
					)}
					{@render externalAction(
						$t('audits.auditEvaluation.actions.suggestEkaizen'),
						Goal,
						'https://demo.ekaizen.app/MYCOMP/PRG01/forms/lean-audit-improvement'
					)}
				</div>
			{/if}

			<div
				class="mt-4 flex shrink-0 flex-col gap-3 px-2 pb-3 sm:mt-6 sm:flex-row sm:justify-between sm:gap-4 sm:px-0 sm:pb-0"
			>
				<div>
					{#if currentSection !== 'question'}
						<Button
							type="button"
							onclick={() => (currentSection = 'question')}
							class="w-full rounded-lg bg-[#7D9AD3] p-6 text-white uppercase hover:bg-[#7D9AD3]/80 hover:text-white sm:w-auto sm:p-6"
						>
							{$t('audits.auditEvaluation.dialog.backToEvaluation')}
						</Button>
					{/if}
				</div>

				<div class="flex flex-col gap-3 sm:flex-row sm:gap-4">
					<Button
						type="button"
						disabled={isFirst || isNavigating}
						onclick={handlePreviousClick}
						class="relative w-full rounded-lg bg-gray-500 p-6 uppercase hover:bg-gray-500/80 sm:w-auto sm:min-w-[120px]"
					>
						<span class={isNavigating ? 'invisible' : ''}>
							{$t('audits.auditEvaluation.dialog.previous')}
						</span>
						{#if isNavigating}
							<div class="absolute inset-0 flex items-center justify-center">
								<Loader2 class="h-5 w-5 animate-spin" />
							</div>
						{/if}
					</Button>

					<Button
						type="button"
						onclick={handleNextClick}
						class="relative w-full rounded-lg p-6 uppercase sm:w-auto sm:min-w-[120px]"
						disabled={isSaving || (!selectedValue && question.required) || isNavigating}
					>
						<span class={isNavigating ? 'invisible' : ''}>
							{$t('audits.auditEvaluation.dialog.next')}
						</span>
						{#if isNavigating}
							<div class="absolute inset-0 flex items-center justify-center">
								<Loader2 class="h-5 w-5 animate-spin" />
							</div>
						{/if}
					</Button>
				</div>
			</div>
		</div>
	</div>
</CustomDialog>

{#snippet evaluationAction(text: string, Icon: Component, onClick: () => void)}
	<div class="flex min-w-0 flex-row items-center gap-4">
		<Icon class="hidden size-8 shrink-0 rounded-md bg-[#EAEDF5] p-2 sm:block sm:size-9" />
		<Button
			variant="link"
			onclick={onClick}
			class="min-w-0 p-0 text-left text-sm text-wrap sm:text-sm">{text}</Button
		>
	</div>
{/snippet}

{#snippet externalAction(text: string, Icon: Component, href: string)}
	<div class="flex min-w-0 flex-row items-center gap-4">
		<Icon class="hidden size-8 shrink-0 rounded-md bg-[#EAEDF5] p-2 sm:block sm:size-9" />
		<Button
			variant="link"
			{href}
			target="_blank"
			rel="noopener noreferrer"
			class="min-w-0 p-0 text-left text-sm text-wrap sm:text-sm"
		>
			{text}
		</Button>
	</div>
{/snippet}
