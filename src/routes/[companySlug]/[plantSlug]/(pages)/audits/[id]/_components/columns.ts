import { renderComponent } from '$lib/components/ui/data-table';
import type { AuditListDTO } from '$lib/DTO/audits/audits';
import type { ColumnDef } from '@tanstack/table-core';
import DataTableActions from './data-table-actions.svelte';
import { t, locale } from '$lib/translations';
import { get } from 'svelte/store';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import WorkplaceLink from './table-format/workplace-cell.svelte';
import CodeCell from '$lib/customComponents/tableFormat/code-cell.svelte';
import AuditorCell from '$lib/customComponents/tableFormat/auditor-cell.svelte';
import PlannedDateCell from './table-format/planned-date-cell.svelte';
import { formatDate } from '$lib/utils/date';
import ProgressBadge from '$lib/customComponents/tableFormat/progress-badge.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import { getStatusSortPriority } from '$lib/utils/audit-status';
import { AuditScoreService } from '$lib/utils/auditScore';
import type { PlantEvaluationConfig } from '$lib/db/schema/company';

export function createColumns(
	callbacks: {
		onEdit: (id: string) => void;
	},
	plantSlug: string,
	companySlug: string,
	userId: string,
	plantConfig?: PlantEvaluationConfig | null
): ColumnDef<AuditListDTO>[] {
	return [
		{
			id: 'code',
			accessorKey: 'code',
			header: ({ column }) => renderComponent(CustomHeader, { text: '#', column }),
			cell: ({ row }) => renderComponent(CodeCell, { code: row.getValue('code') as string }),
			enableSorting: true
		},
		{
			id: 'auditType',
			accessorKey: 'auditType',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.auditType'),
					column
				}),
			cell: ({ row }) =>
				renderComponent(Cell, { content: (row.getValue('auditType') as { name: string }).name }),
			enableSorting: true
		},
		{
			id: 'auditor',
			accessorFn: (row: AuditListDTO) => {
				const auditor = row.auditor;
				if (!auditor) return '';
				return `${auditor.firstName || ''} ${auditor.lastName || ''}`.trim();
			},
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.auditor'),
					column
				}),
			cell: ({ row }) => {
				const auditor = row.original.auditor;
				if (!auditor) return null;

				return renderComponent(AuditorCell, {
					firstName: auditor.firstName,
					lastName: auditor.lastName
				});
			},
			enableSorting: true
		},
		{
			id: 'workplace',
			accessorFn: (row: AuditListDTO) => row.workplace?.name ?? '',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.workplace'),
					column
				}),
			cell: ({ row }) => {
				const workplace = row.original.workplace;
				if (!workplace) return null;

				return renderComponent(WorkplaceLink, {
					workplace: workplace,
					plantSlug: plantSlug,
					companySlug: companySlug
				});
			},
			enableSorting: true
		},
		{
			id: 'plannedDate',
			accessorKey: 'plannedDate',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.plannedDate'),
					column
				}),
			cell: ({ row }) => {
				const date = new Date(row.getValue('plannedDate'));
				const isOverdue = row.original.progress === -1;

				return renderComponent(PlannedDateCell, {
					plannedDate: date.toISOString(),
					isOverdue
				});
			},
			enableSorting: true
		},
		{
			id: 'completionDate',
			accessorKey: 'completionDate',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.completionDate'),
					column
				}),
			cell: ({ row }) => {
				if (row.getValue('completionDate') === null) {
					return '';
				}
				const date = new Date(row.getValue('completionDate'));
				return renderComponent(Cell, {
					content: formatDate(date.toISOString(), get(locale))
				});
			},
			enableSorting: true
		},
		{
			id: 'progress',
			accessorFn: (row: AuditListDTO) => getStatusSortPriority(row),
			sortingFn: (rowA, rowB) => {
				const priorityA = getStatusSortPriority(rowA.original);
				const priorityB = getStatusSortPriority(rowB.original);
				return priorityA - priorityB;
			},
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.progress'),
					column
				}),
			cell: ({ row }) => {
				const progress = row.original.progress;
				const completionDate = row.original.completionDate;
				const successRate = row.original.successRate;

				let text: string;
				let variant:
					| 'default'
					| 'filled'
					| 'warning'
					| 'success'
					| 'success-good'
					| 'success-average'
					| 'success-poor'
					| 'destructive' = 'default';

				if (progress === -1) {
					// Late
					text = get(t)('audits.progress.late');
					variant = 'destructive';
				} else if (completionDate && successRate !== undefined && successRate !== null) {
					// Completed (has completionDate and successRate)
					text = get(t)('audits.progress.closed');

					const rulesResult = row.original.rulesResult;
					const auditInstanceConfig = row.original.auditInstanceConfig;

					if (plantConfig) {
						const badgeColor = AuditScoreService.getAuditBadgeColor(
							rulesResult,
							successRate,
							plantConfig,
							auditInstanceConfig
						);

						if (badgeColor === 'success') {
							variant = 'success-good';
						} else if (badgeColor === 'average') {
							variant = 'success-average';
						} else if (badgeColor === 'bad') {
							variant = 'success-poor';
						}
					} else {
						console.log('No plant config - using fallback');
						variant = 'success';
					}
				} else if (progress === 100) {
					// Filled but not completed
					text = get(t)('audits.progress.completed');
					variant = 'filled';
				} else if (progress > 0) {
					// In progress - show only text, no percentage
					text = get(t)('audits.progress.inProgress');
					variant = 'default';
				} else {
					text = get(t)('audits.progress.planned');
					variant = 'warning';
				}

				return renderComponent(ProgressBadge, {
					progress,
					text,
					variant,
					auditId: row.original.id,
					completionDate: row.original.completionDate,
					successRate: row.original.successRate ?? undefined,
					userId: userId,
					auditorId: row.original.auditor!.id
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',
			enableSorting: false,
			enableHiding: false,
			meta: {
				headerClass: 'text-right',
				cellClass: 'text-right'
			},
			cell: ({ row }) => {
				return renderComponent(DataTableActions, {
					id: row.original.id,
					code: row.original.code,
					progress: row.original.progress,
					row: row.original,
					onEdit: callbacks.onEdit
				});
			}
		}
	];
}
