<script lang="ts">
	import { formatDate } from '$lib/utils/date';
	import { locale } from '$lib/translations';
	import { get } from 'svelte/store';

	interface Props {
		plannedDate: string;
		isOverdue: boolean;
	}

	let { plannedDate, isOverdue = false }: Props = $props();

	let formattedDate = $derived(formatDate(plannedDate, get(locale)));
</script>

<span
	class={`text-sm italic ${isOverdue ? 'font-bold text-[#f88a68]' : 'font-light text-[#747C8A]'}`}
>
	{formattedDate}
</span>
