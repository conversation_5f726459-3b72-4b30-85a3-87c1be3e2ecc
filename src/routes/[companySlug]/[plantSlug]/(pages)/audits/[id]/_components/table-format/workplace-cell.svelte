<script lang="ts">
	interface Props {
		workplace: {
			id: string;
			name: string;
			code?: string;
		};
		companySlug: string;
		plantSlug: string;
	}

	let { workplace, companySlug, plantSlug }: Props = $props();

	const isDeleted = workplace.code?.startsWith('DELETED_') ?? false;
</script>

{#if isDeleted}
	<span class="font-light text-gray-500 italic">
		{workplace.name[0].toUpperCase()}{workplace.name.slice(1)}
	</span>
{:else}
	<a
		href={`/${companySlug}/${plantSlug}/workplaces/${workplace.id}`}
		class="font-light text-[#7D9AD3] italic underline"
	>
		{workplace.name[0].toUpperCase()}{workplace.name.slice(1)}
	</a>
{/if}
