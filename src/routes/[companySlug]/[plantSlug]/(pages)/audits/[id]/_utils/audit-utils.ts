import type { QuestionAnswer } from '$lib/DTO/audits/auditEvaluation';

/**
 * Returns initialized answer for question
 */
export function getInitialAnswer(questionId: string): QuestionAnswer {
  return {
    evaluationValue: null,
    note: '',
    files: [],
    questionId,
    auditId: null
  };
}

/**
 * Returns if the value is valid for sending the form
 */
export function isValueValid(value: string | null, isRequired: boolean): boolean {
  if (isRequired) {
    return !!value;
  }
  return true;
}

/**
 * Return icon for file type
 */
export function getFileIcon(type: string): string | null {
  if (type.startsWith('image/')) {
    return null; // For images, we show a preview
  }
  return 'file-text';
} 