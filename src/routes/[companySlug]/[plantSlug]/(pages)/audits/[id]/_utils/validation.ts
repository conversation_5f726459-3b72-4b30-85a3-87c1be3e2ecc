import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from '$lib/constants/file';
import type { FileValidationResult } from '$lib/DTO/audits/auditEvaluation';

/**
 * Validates file for upload
 * @param file File to validate
 * @returns Object with validation result and possible error message
 */
export function validateFile(file: File): FileValidationResult {
	// Check size
	if (file.size > MAX_FILE_SIZE) {
		return {
			valid: false,
			message: `File is too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024} MB.`
		};
	}

	// Check file type
	if (ALLOWED_FILE_TYPES.length > 0 && !ALLOWED_FILE_TYPES.includes(file.type)) {
		return {
			valid: false,
			message: 'File has an unsupported type. Allowed types are images, PDF and Office documents.'
		};
	}

	return { valid: true };
}

/**
 * Format size
 */
export function formatFileSize(bytes: number): string {
	if (bytes < 1024) return bytes + ' B';
	if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
	return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
}
