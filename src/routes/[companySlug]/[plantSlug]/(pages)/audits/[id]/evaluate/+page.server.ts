import { AuditEvaluateService, AuditEvaluationConfigService } from '$lib/server/services/audits';
import { fail, redirect } from '@sveltejs/kit';
import { createAuditBreadcrumbs } from '../../_utils/createBreadcrumbs';
import { QuestionsService } from '$lib/server/services/questions';
import { getFileSignedUrl } from '$lib/server/storage';
import { superValidate } from 'sveltekit-superforms';
import { contactMessageSchema } from '$lib/schemas/messages';
import { zod } from 'sveltekit-superforms/adapters';
import { EmailService } from '$lib/server/services/email';
import type { AttachmentFile } from '$lib/DTO/audits/auditEvaluation';

export const load = async ({ params, parent, locals }) => {
	const { context, user } = await parent();

	const [evaluation, existingAnswers, questionTexts, auditInstanceEvaluationConfig] =
		await Promise.all([
			AuditEvaluateService.getEvaluationQuestions(params.id),
			AuditEvaluateService.getAuditAnswers(params.id),
			QuestionsService.getQuestions(context.plantId, true),
			AuditEvaluationConfigService.getAuditInstanceEvaluationConfig(params.id)
		]);

	if (!evaluation[0].responsiblePerson || evaluation[0].responsiblePerson.id !== user!.id) {
		redirect(303, `/${context.companySlug}/${context.plantSlug}/audits/`);
	}

	const existingAnswersWithSignedUrls = {} as typeof existingAnswers;

	for (const [questionId, answer] of Object.entries(existingAnswers)) {
		const filesWithSignedUrls = await Promise.all(
			(answer.files || []).map(async (file: AttachmentFile) => {
				const signedUrl = await getFileSignedUrl(file.url);
				return {
					...file,
					url: signedUrl
				};
			})
		);

		existingAnswersWithSignedUrls[questionId] = {
			...answer,
			files: filesWithSignedUrls
		};
	}

	return {
		evaluation,
		existingAnswers: existingAnswersWithSignedUrls,
		questionTexts,
		auditInstanceEvaluationConfig,
		plantDefaultLanguage: locals.plantDefaultLanguage,
		breadcrumbs: createAuditBreadcrumbs(
			params.id,
			evaluation[0].code,
			'common.navigation.auditEvaluation',
			context.companySlug,
			context.plantSlug
		)
	};
};

export const actions = {
	saveDraft: async ({ request, params }) => {
		const formData = await request.formData();

		const questionId = formData.get('questionId')?.toString();
		const value = formData.get('value')?.toString();
		const note = formData.get('note')?.toString();
		const realDuration = Number(formData.get('realDuration') || 0);

		const uploadedFilesJson = formData.get('uploadedFiles')?.toString();
		const savedFiles = uploadedFilesJson ? JSON.parse(uploadedFilesJson) : [];

		const filesToRemoveJson = formData.get('filesToRemove')?.toString();
		const filesToRemove = filesToRemoveJson ? JSON.parse(filesToRemoveJson) : [];

		if (!questionId) {
			console.error('Missing questionId');
			return fail(400, {
				type: 'error',
				error: 'Missing questionId'
			});
		}

		try {
			const result = await AuditEvaluateService.saveQuestionAnswer(
				params.id,
				questionId,
				value ?? null,
				note || '',
				realDuration,
				savedFiles,
				filesToRemove
			);

			const filesWithSignedUrls = await Promise.all(
				result.files.map(async (file) => {
					const signedUrl = await getFileSignedUrl(file.url);
					return {
						...file,
						url: signedUrl
					};
				})
			);

			return {
				type: 'success',
				data: {
					files: filesWithSignedUrls
				}
			};
		} catch (error) {
			console.error('Error saving answer:', error);
			return fail(500, {
				type: 'error',
				error: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	},

	completeAudit: async ({ request, params }) => {
		const formData = await request.formData();
		const realDuration = Number(formData.get('realDuration') || 0);
		const answersJson = formData.get('answers')?.toString();

		try {
			if (!answersJson) {
				throw new Error('No answers provided');
			}

			const answers = JSON.parse(answersJson);
			await AuditEvaluateService.completeAudit(params.id, answers, realDuration);

			return {
				type: 'success'
			};
		} catch (error) {
			console.error('Error completing audit:', error);
			return fail(500, {
				type: 'error',
				error: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	},

	sendEmail: async ({ request, locals }) => {
		const form = await superValidate(request, zod(contactMessageSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			if (!form.data.templateAuditorId && !form.data.workplaceManagerId) {
				return fail(400, {
					form,
					error: 'Musíte vybrat alespoň jednoho příjemce'
				});
			}

			const emailLanguage = locals.plantDefaultLanguage || 'en';

			if (form.data.templateAuditorId) {
				await EmailService.sendEmail(
					form.data.templateAuditorId,
					form.data.auditTypeName,
					form.data.message,
					form.data.questionId,
					emailLanguage
				);
			}

			if (form.data.workplaceManagerId) {
				await EmailService.sendEmail(
					form.data.workplaceManagerId,
					form.data.auditTypeName,
					form.data.message,
					form.data.questionId,
					emailLanguage
				);
			}

			return { form, success: true };
		} catch (error) {
			console.error('Error sending email:', error);
			return fail(500, {
				form,
				error: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	}
};
