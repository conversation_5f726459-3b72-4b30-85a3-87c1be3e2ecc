<script lang="ts">
	import { t } from '$lib/translations';
	import * as Accordion from '$lib/components/ui/accordion/index';
	import Progress from '$lib/components/ui/progress/progress.svelte';
	import {
		CalendarFold,
		Circle,
		CircleCheck,
		FoldVertical,
		Hash,
		Hourglass,
		LandPlot,
		LoaderCircle,
		Pause,
		Play,
		QrCode,
		ShieldCheck,
		Timer,
		UnfoldVertical,
		User
	} from '@lucide/svelte';
	import { type Component } from 'svelte';
	import type { TemplateQuestion } from '$lib/schemas/audits/auditQuestions';
	import { Button } from '$lib/components/ui/button';
	import { locale } from '$lib/translations';
	import { fade } from 'svelte/transition';
	import { formatDuration, minutesToHHMM, formatSecondsDuration } from '$lib/utils/time';
	import { formatDate } from '$lib/utils/date';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { onMount, onDestroy } from 'svelte';
	import AuditEvaluationDialog from '../_components/audit-evaluation-dialog.svelte';
	import ConfirmDialog from '$lib/customComponents/confirm-dialog.svelte';
	import type { AuditQuestion, QuestionAnswer } from '$lib/DTO/audits/auditEvaluation';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import QRCode from 'qrcode';
	import { page } from '$app/state';
	import { formatEvaluationValue } from '../results/_utils/evaluation-value';

	let { data } = $props();

	// State management
	let answers = $state<Record<string, QuestionAnswer>>(
		Object.fromEntries(
			Object.entries(data.existingAnswers || {}).map(([key, answer]) => [
				key,
				{
					questionId: answer.questionId,
					auditId: answer.auditId,
					evaluationValue: String(answer.evaluationValue),
					note: answer.note || '',
					files: answer.files || []
				}
			])
		)
	);

	let availableQuestions = $derived(() =>
		Object.values(data.evaluation?.[0]?.questions || {}).flatMap(
			(category) => category?.questions || []
		)
	);

	let categoriesData = $derived(
		Object.entries(data.evaluation?.[0]?.questions || {}).map(([id, category]) => ({
			id,
			name: category?.name || id
		}))
	);

	let categories = $derived(categoriesData.map((cat) => cat.name));
	let categoryIds = $derived(categoriesData.map((cat) => cat.id));

	function getCategoryIdByIndex(index: number): string {
		return categoriesData[index]?.id || '';
	}

	function findLastInProgressCategory() {
		for (let i = 0; i < categoriesData.length; i++) {
			const categoryId = categoriesData[i].id;
			const categoryQuestions = data.evaluation?.[0]?.questions?.[categoryId]?.questions || [];
			const hasUnansweredQuestions = categoryQuestions.some((question) => {
				const answer = answers[question.questionId];
				return !answer || !answer.evaluationValue || answer.evaluationValue === 'Visited';
			});
			if (hasUnansweredQuestions) {
				return i;
			}
		}
		return categoriesData.length - 1;
	}

	let lastInProgressIndex = $derived(categories.length > 0 ? findLastInProgressCategory() : 0);
	let currentCategory = $state(0);
	let currentQuestionIndex = $state(0);

	let initialized = $state(false);
	$effect(() => {
		if (categories.length > 0 && !initialized) {
			const initialCategory = findLastInProgressCategory();
			currentCategory = initialCategory;

			const categoryId = getCategoryIdByIndex(initialCategory);
			const categoryQuestions = data.evaluation?.[0]?.questions?.[categoryId]?.questions || [];
			let initialQuestionIndex = 0;

			for (let i = 0; i < categoryQuestions.length; i++) {
				const question = categoryQuestions[i];
				const answer = answers[question.questionId];
				if (
					!answer ||
					!answer.evaluationValue ||
					answer.evaluationValue === 'Visited' ||
					answer.evaluationValue === 'NotVisited'
				) {
					initialQuestionIndex = i;
					break;
				}
			}

			currentQuestionIndex = initialQuestionIndex;
			initialized = true;

			console.log(
				'[INIT] Initialized to category:',
				currentCategory,
				'question index:',
				currentQuestionIndex
			);
		}
	});
	let expandedCategories = $derived<string[]>(
		categories[lastInProgressIndex] ? [categories[lastInProgressIndex]] : []
	);

	let currentCategoryQuestions = $derived(
		data.evaluation?.[0]?.questions?.[getCategoryIdByIndex(currentCategory)]?.questions || []
	);

	let answeredQuestions = $derived(
		Object.entries(answers).filter(([questionId, answer]) => {
			const question = availableQuestions().find((q) => q && q.questionId === questionId);
			return (
				question &&
				answer?.evaluationValue !== null &&
				answer?.evaluationValue !== undefined &&
				answer?.evaluationValue !== 'NotVisited'
			);
		}).length
	);

	let totalQuestions = $derived(availableQuestions().length);
	let progress = $derived(Math.round((answeredQuestions / totalQuestions) * 100));

	//Dialog
	let showQuestionDialog = $state(false);
	let currentQuestion = $state<AuditQuestion | null>(null);
	let isLast = $state(false);

	// Confirm dialog
	let showConfirmDialog = $state(false);

	// Loading state
	let isCompletingAudit = $state(false);

	// QR Dialog
	let showQrDialog = $state(false);
	let qrCodeUrl = $state('');

	// Generate QR code when dialog opens
	$effect(() => {
		if (showQrDialog && page.url) {
			QRCode.toDataURL(page.url.toString(), {
				width: 256,
				margin: 2,
				color: {
					dark: '#000000',
					light: '#ffffff'
				}
			})
				.then((url) => {
					qrCodeUrl = url;
				})
				.catch((err) => {
					console.error('Error generating QR code:', err);
				});
		}
	});

	function handleConfirmDialogClose() {
		showConfirmDialog = false;
	}

	function handleConfirmDialogConfirm() {
		showConfirmDialog = false;
		completeAudit();
	}

	function handleShowConfirmDialog() {
		showConfirmDialog = true;
	}

	function completeAudit() {
		const completeButton = document.querySelector(
			'#complete-audit-form-submit'
		) as HTMLButtonElement;
		if (completeButton) {
			completeButton.click();
		}
	}

	function handleDialogClose() {
		showQuestionDialog = false;
	}

	function convertToAuditQuestion(question: TemplateQuestion): AuditQuestion {
		const questionText = data.questionTexts.find((q) => q.id === question.questionId);
		return {
			...question,
			text: questionText?.text || 'Unknown question',
			subtext: questionText?.subtext || null,
			evaluationType: questionText?.evaluationType || 'yesno'
		};
	}

	let questionFilesMap = $state<Record<string, any[]>>({});
	let currentQuestionFiles = $state<any[]>([]);
	let isFilesLoading = $state(false);

	async function handleQuestionClick(question: TemplateQuestion) {
		const position = findQuestionPosition(question);
		if (position) {
			currentCategory = position.categoryIndex;
			currentQuestionIndex = position.questionIndex;
		}

		const auditId = data.evaluation[0]?.id;
		const qId = question.questionId;

		currentQuestionFiles = questionFilesMap[qId] || answers[qId]?.files || [];
		showQuestionDialog = true;
		currentQuestion = convertToAuditQuestion(question);
		isLast = areAllRequiredAnsweredExceptCurrent();
		startTimer();

		//Refetching attachments optimization
		if (questionFilesMap[qId]) return;

		// GET attachments
		isFilesLoading = true;
		try {
			const res = await fetch(`/api/audit/${auditId}/questions/${qId}`);
			if (res.ok) {
				const result = await res.json();
				questionFilesMap[qId] = result.files || [];
				currentQuestionFiles = questionFilesMap[qId];
			} else {
				questionFilesMap[qId] = [];
			}
		} catch (e) {
			questionFilesMap[qId] = [];
		}
		isFilesLoading = false;
	}

	function handleAnswer(
		value: string,
		note: string,
		files: Array<{ url: string; filename: string; type: string }>
	) {
		if (currentQuestion) {
			answers = {
				...answers,
				[currentQuestion.questionId]: {
					questionId: currentQuestion.questionId,
					auditId: data.evaluation[0]?.id || '',
					evaluationValue: value,
					note: note,
					files: files
				}
			};

			isLast = areAllRequiredAnsweredExceptCurrent();
		}
	}

	function handlePrevious() {
		if (currentQuestionIndex > 0) {
			currentQuestionIndex--;
			const prevQuestion = currentCategoryQuestions[currentQuestionIndex];
			currentQuestion = convertToAuditQuestion(prevQuestion);
		} else if (currentCategory > 0) {
			currentCategory--;
			const categoryId = getCategoryIdByIndex(currentCategory);
			const questions = data.evaluation?.[0]?.questions?.[categoryId]?.questions || [];
			currentQuestionIndex = questions.length - 1;
			const prevQuestion = questions[currentQuestionIndex];
			currentQuestion = prevQuestion ? convertToAuditQuestion(prevQuestion) : null;
			updateExpandedCategories(currentCategory);
		}

		if (currentQuestion) {
			isLast = areAllRequiredAnsweredExceptCurrent();
		}
	}

	function handleNext() {
		if (currentQuestion && !answers[currentQuestion.questionId] && !currentQuestion.required) {
			handleAnswer('Visited', '', []);
		}

		const currentCategoryLength = currentCategoryQuestions.length;

		if (currentQuestionIndex < currentCategoryLength - 1) {
			currentQuestionIndex++;
			const nextQuestion = currentCategoryQuestions[currentQuestionIndex];
			currentQuestion = convertToAuditQuestion(nextQuestion);
		} else if (currentCategory < categories.length - 1) {
			currentCategory++;
			currentQuestionIndex = 0;
			const categoryId = getCategoryIdByIndex(currentCategory);
			const nextQuestion = data.evaluation?.[0]?.questions?.[categoryId]?.questions?.[0];
			currentQuestion = nextQuestion ? convertToAuditQuestion(nextQuestion) : null;
			updateExpandedCategories(currentCategory);
		}

		if (currentQuestion) {
			isLast = areAllRequiredAnsweredExceptCurrent();
		}
	}

	function findQuestionPosition(question: TemplateQuestion) {
		for (let i = 0; i < categoriesData.length; i++) {
			const categoryId = categoriesData[i].id;
			const categoryQuestions = data.evaluation?.[0]?.questions?.[categoryId]?.questions || [];
			const questionIndex = categoryQuestions.findIndex(
				(q) => q.questionId === question.questionId
			);
			if (questionIndex !== -1) {
				return { categoryIndex: i, questionIndex };
			}
		}
		return null;
	}

	function switchCategory(newCategoryIndex: number) {
		currentCategory = newCategoryIndex;
		currentQuestionIndex = 0;
		const categoryId = getCategoryIdByIndex(newCategoryIndex);

		const categoryQuestions = data.evaluation?.[0]?.questions?.[categoryId]?.questions || [];
		let targetQuestionIndex = 0;

		for (let i = 0; i < categoryQuestions.length; i++) {
			const question = categoryQuestions[i];
			const answer = answers[question.questionId];
			if (
				!answer ||
				!answer.evaluationValue ||
				answer.evaluationValue === 'Visited' ||
				answer.evaluationValue === 'NotVisited'
			) {
				targetQuestionIndex = i;
				break;
			}
		}

		currentQuestionIndex = targetQuestionIndex;
		const newQuestion = categoryQuestions[targetQuestionIndex];
		currentQuestion = newQuestion ? convertToAuditQuestion(newQuestion) : null;

		updateExpandedCategories(newCategoryIndex);
	}

	function updateExpandedCategories(currentIndex: number) {
		const categoriesToExpand = categories.slice(0, currentIndex + 1);

		const categoriesToKeepExpanded = categoriesToExpand.filter((category, index) => {
			const categoryId = getCategoryIdByIndex(index);
			const categoryQuestions = data.evaluation?.[0]?.questions?.[categoryId]?.questions || [];

			const allQuestionsAnswered = categoryQuestions.every((question) => {
				const answer = answers[question.questionId];
				return answer && answer.evaluationValue && answer.evaluationValue !== 'Visited';
			});

			return !allQuestionsAnswered || category === categories[currentIndex];
		});

		expandedCategories = categoriesToKeepExpanded;
	}

	//Accordion
	// svelte-ignore state_referenced_locally
	function expandAll() {
		expandedCategories = [...categories];
	}
	function collapseAll() {
		expandedCategories = [];
	}

	// Tracking real duration
	let isTracking = $state(false);
	let startTimestamp = $state<number | null>(null);
	let pausedDuration = $state(0);
	let pauseStartedAt = $state<number | null>(null);
	let displayTime = $state(data.evaluation?.[0]?.realDuration || 0);

	function updateDisplayTime() {
		if (isTracking && startTimestamp !== null) {
			displayTime =
				(data.evaluation?.[0]?.realDuration || 0) +
				Math.floor((Date.now() - startTimestamp - pausedDuration) / 1000);
		}
	}

	let timeUpdateInterval: ReturnType<typeof setInterval>;

	onMount(() => {
		timeUpdateInterval = setInterval(updateDisplayTime, 1000);
	});

	onDestroy(() => {
		if (timeUpdateInterval) clearInterval(timeUpdateInterval);
	});

	$effect(() => {
		if (isTracking) {
			updateDisplayTime();
		}
	});

	function startTimer() {
		if (!isTracking) {
			isTracking = true;
			if (pauseStartedAt !== null) {
				pausedDuration += Date.now() - pauseStartedAt;
				pauseStartedAt = null;
			}
			if (startTimestamp === null) {
				startTimestamp = Date.now();
			}
		}
	}

	function pauseTimer() {
		if (isTracking) {
			isTracking = false;
			pauseStartedAt = Date.now();
		}
	}

	function getWorkplaceName() {
		const workplace = data.evaluation[0]?.workplace;
		if (typeof workplace === 'string') return workplace;
		if (workplace && typeof workplace === 'object' && 'name' in workplace) return workplace.name;
		return 'N/A';
	}

	function getAuditTypeName() {
		const auditType = data.evaluation[0]?.auditType;
		if (typeof auditType === 'string') return auditType;
		if (auditType && typeof auditType === 'object' && 'name' in auditType) return auditType.name;
		return 'N/A';
	}

	function canCompleteAudit() {
		return availableQuestions()
			.filter((q) => q.required)
			.every((q) => answers[q.questionId]?.evaluationValue);
	}

	function areAllRequiredAnsweredExceptCurrent() {
		return availableQuestions()
			.filter(
				(q) => q.required && (!currentQuestion || q.questionId !== currentQuestion.questionId)
			)
			.every((q) => answers[q.questionId]?.evaluationValue);
	}
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.auditEvaluation')} - {data.evaluation[0]!.code}</title>
</svelte:head>

<div in:fade={{ duration: 300 }} class="mt-4 mb-8 flex h-[calc(100vh-10rem)] w-full gap-4">
	<div class="flex h-full w-full flex-col lg:flex-row lg:gap-4">
		<div
			class="flex h-full w-full flex-col rounded-2xl bg-white p-2 lg:flex-row lg:gap-4 lg:rounded-none lg:bg-transparent"
		>
			<!-- Info panel -->
			<div class="w-full p-3 lg:w-96 lg:shrink-0 lg:rounded-2xl lg:bg-white lg:p-6">
				<div class="grid grid-cols-2 gap-1 text-base lg:grid-cols-1 lg:gap-4">
					<div class="hidden lg:col-span-1 lg:flex lg:items-center lg:justify-between">
						{@render auditStats(
							$t('audits.auditEvaluation.stats.auditNumber'),
							data.evaluation[0]?.code || 'N/A',
							Hash
						)}
						<ButtonIcon
							Icon={QrCode}
							class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white"
							onClickAction={() => {
								showQrDialog = true;
							}}
							iconSize={7}
							backgroundColor="#B1B7C3"
							hoverBackgroundColor="#B1B7C3/80"
						/>
					</div>
					<div class="lg:hidden">
						{@render auditStats(
							$t('audits.auditEvaluation.stats.auditNumber'),
							data.evaluation[0]?.code || 'N/A',
							Hash
						)}
					</div>
					{@render auditStats(
						$t('audits.auditEvaluation.stats.workspace'),
						getWorkplaceName(),
						LandPlot
					)}
					{@render auditStats(
						$t('audits.auditEvaluation.stats.auditType'),
						getAuditTypeName(),
						ShieldCheck
					)}
					{@render auditStats(
						$t('audits.auditEvaluation.stats.auditor'),
						data.evaluation[0]?.responsiblePerson?.firstName +
							' ' +
							data.evaluation[0]?.responsiblePerson?.lastName || 'N/A',
						User
					)}
					{@render auditStats(
						$t('audits.auditEvaluation.stats.plannedDate'),
						formatDate(data.evaluation[0]?.plannedEvaluationDate || 'N/A', $locale, 'long'),
						Timer
					)}
					{@render auditStats(
						$t('audits.auditEvaluation.stats.plannedDuration'),
						formatDuration(minutesToHHMM(data.evaluation[0].expectedDuration!)),
						CalendarFold
					)}
					{@render auditStats(
						$t('audits.auditEvaluation.stats.actualDuration'),
						formatSecondsDuration(displayTime, $locale),
						Hourglass
					)}
				</div>

				<!-- Progress -->
				<div class="mt-4 lg:mt-4">
					<div class="flex flex-col space-y-3 lg:space-y-3">
						<div class="flex items-center justify-between">
							<h3 class="text-sm text-[#747C8A] lg:text-sm">
								{$t('audits.auditEvaluation.progress')}
							</h3>
							<div class="flex items-center space-x-1.5 lg:space-x-2">
								<span class="text-2xl font-bold text-[#697691] lg:text-2xl"
									>{answeredQuestions}</span
								>
								<span class="text-sm text-slate-500 lg:text-sm">
									{$t('audits.auditEvaluation.questionsCount.of')}
									{totalQuestions}
									{#if totalQuestions === 1}
										{$t('audits.auditEvaluation.questionsCount.one')}
									{:else if totalQuestions >= 2 && totalQuestions <= 4}
										{$t('audits.auditEvaluation.questionsCount.few')}
									{:else}
										{$t('audits.auditEvaluation.questionsCount.many')}
									{/if}
								</span>
							</div>
						</div>
						<Progress
							value={progress}
							class="h-6 rounded-md bg-[#697691] lg:h-6"
							indicatorClass="bg-[#414E6B]"
							showLabel={true}
						/>

						<!-- Complete audit button -->
						<div class="mt-2 lg:mt-4">
							<Button
								class="bg-primary font-titillium hover:bg-primary/80 flex h-12 w-full items-center justify-center gap-2 rounded-xl text-sm text-white uppercase lg:hidden [&_svg]:size-6!"
								onclick={handleShowConfirmDialog}
								disabled={!canCompleteAudit() || isCompletingAudit}
							>
								{#if isCompletingAudit}
									<LoaderCircle class="size-6 animate-spin" />
									<span class="text-xs">{$t('audits.auditEvaluation.actions.completing')}</span>
								{:else}
									<ShieldCheck class="size-6" />
									<span class="text-xs">{$t('audits.auditEvaluation.actions.complete')}</span>
								{/if}
							</Button>
						</div>
					</div>
				</div>
			</div>

			<!-- Questions -->
			<div class="flex-1 overflow-auto p-4 lg:rounded-2xl lg:bg-white lg:p-6">
				<div class="mb-3 hidden lg:flex lg:flex-row lg:justify-end lg:gap-2">
					<Button
						class="bg-primary font-titillium hover:bg-primary/80 h-12 rounded-xl text-sm text-white uppercase [&_svg]:size-6!"
						onclick={handleShowConfirmDialog}
						disabled={!canCompleteAudit() || isCompletingAudit}
					>
						{#if isCompletingAudit}
							<LoaderCircle class="size-6 animate-spin" />
							<span class="text-xs">{$t('audits.auditEvaluation.actions.completing')}</span>
						{:else}
							<ShieldCheck class="size-6" />
							<span class="text-xs">{$t('audits.auditEvaluation.actions.complete')}</span>
						{/if}
					</Button>
					<div class="bg-secondary flex rounded-xl">
						<Button
							class="size-12 rounded-xl [&_svg]:size-6! {!isTracking
								? 'bg-primary'
								: 'bg-secondary'} hover:bg-primary/80 text-white transition-colors"
							onclick={pauseTimer}
						>
							<Pause />
						</Button>
						<Button
							class="size-12 rounded-xl [&_svg]:size-6! {isTracking
								? 'bg-primary'
								: 'bg-secondary'} hover:bg-primary/80 text-white transition-colors"
							onclick={startTimer}
						>
							<Play />
						</Button>
					</div>
					<Button
						class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
						onclick={collapseAll}
					>
						<FoldVertical />
						<span class="sr-only">{$t('audits.auditEvaluation.actions.collapseAll')}</span>
					</Button>
					<Button
						class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
						onclick={expandAll}
					>
						<UnfoldVertical />
						<span class="sr-only">{$t('audits.auditEvaluation.actions.expandAll')}</span>
					</Button>
				</div>
				<Accordion.Root value={expandedCategories} type="multiple">
					{#each categoriesData as categoryData, index}
						<Accordion.Item value={categoryData.name}>
							<Accordion.Trigger
								class="mb-2 w-full rounded-md bg-[#EAEDF5] px-3 py-2.5 text-sm hover:no-underline lg:mb-4 lg:px-4 lg:py-2.5 lg:text-base"
							>
								<div class="flex w-full flex-row items-center justify-between">
									<span class="hover:underline">{categoryData.name}</span>
									<div class="mx-2 flex items-center text-[#697691] hover:no-underline">
										{#if answers}
											<span class="text-sm hover:no-underline">
												{(
													data.evaluation?.[0]?.questions?.[categoryData.id]?.questions?.filter(
														(q) => q && answers[q.questionId]
													) || []
												).length}
											</span>
										{/if}
										<span class="text-[#B1B7C3] hover:no-underline">/</span>
										<span class="text-sm hover:no-underline">
											{data.evaluation?.[0]?.questions?.[categoryData.id]?.questions?.length || 0}
										</span>
									</div>
								</div>
							</Accordion.Trigger>
							<Accordion.Content class="p-1.5 lg:p-2">
								{#each data.evaluation?.[0]?.questions?.[categoryData.id]?.questions || [] as question}
									<button
										type="button"
										class="flex w-full cursor-pointer items-center gap-3 rounded-lg border border-transparent p-2 text-left hover:border-dashed hover:border-[#697691]/30 focus:ring-2 focus:ring-black lg:gap-4"
										onclick={() => handleQuestionClick(question)}
										data-question={JSON.stringify(question)}
									>
										<div class="shrink-0">
											{#if answers[question.questionId] && answers[question.questionId].evaluationValue !== 'NotVisited'}
												<CircleCheck class="h-6 w-6 text-[#697691] lg:h-5 lg:w-5" />
											{:else}
												<Circle class="h-6 w-6 text-[#B1B7C3] lg:h-5 lg:w-5" />
											{/if}
										</div>

										<div class="flex-1">
											<p class="line-clamp-2 text-xs leading-tight text-[#747C8A] lg:text-sm">
												{data.questionTexts.find((q) => q.id === question.questionId)?.text ||
													'Unknown question'}
											</p>
											<p>
												{#if !question.required}
													<span class="ml-1 text-xs text-[#B1B7C3] italic">
														({$t('audits.evaluationTypes.optional')})
													</span>
												{/if}
											</p>
										</div>

										{#if answers[question.questionId]?.evaluationValue && answers[question.questionId].evaluationValue !== 'NotVisited'}
											{#if answers[question.questionId].evaluationValue !== 'Visited'}
												<div class="shrink-0">
													<span
														class="inline-flex h-8 min-w-[3rem] items-center justify-center rounded-lg px-3 text-xs font-semibold text-white uppercase
		{data.questionTexts.find((q) => q.id === question.questionId)?.evaluationType ===
														'meetsreservations'
															? 'text-[10px] sm:text-xs'
															: ''}"
														style="background-color: {formatEvaluationValue(
															answers[question.questionId].evaluationValue!.toLowerCase(),
															data.questionTexts.find((q) => q.id === question.questionId)
																?.evaluationType,
															data.auditInstanceEvaluationConfig
														)}"
													>
														{#if data.questionTexts.find((q) => q.id === question.questionId)?.evaluationType === 'points'}
															{answers[question.questionId].evaluationValue}
														{:else if data.questionTexts.find((q) => q.id === question.questionId)?.evaluationType === 'percentage'}
															{answers[question.questionId].evaluationValue}%
														{:else if data.questionTexts.find((q) => q.id === question.questionId)?.evaluationType === 'yesno_inverse'}
															{$t(
																`audits.evaluationTypes.evaluationValues.${answers[question.questionId].evaluationValue?.toLowerCase?.() ?? ''}`
															)}
														{:else if data.questionTexts.find((q) => q.id === question.questionId)?.evaluationType === 'notvisited'}
															""
														{:else}
															{$t(
																`audits.evaluationTypes.evaluationValues.${answers[question.questionId].evaluationValue?.toLowerCase?.() ?? ''}`
															)}
														{/if}
													</span>
												</div>
											{/if}
										{/if}
									</button>
								{/each}
							</Accordion.Content>
						</Accordion.Item>
					{/each}
				</Accordion.Root>
			</div>
		</div>
	</div>
</div>

{#snippet auditStats(name: string, content: string, Icon: Component)}
	<div class="flex flex-row items-start gap-2.5 lg:gap-4">
		<Icon class="mt-0.5 size-6 shrink-0 stroke-[1.5] text-[#EAEDF5] lg:mt-1 lg:size-8" />
		<div class="flex min-w-0 flex-col text-left">
			<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
			<span class="text-sm break-words text-[#4B505A] lg:-mt-1 lg:text-base">{content}</span>
		</div>
	</div>
{/snippet}

{#if currentQuestion}
	<AuditEvaluationDialog
		open={showQuestionDialog}
		code={data.evaluation?.[0]?.code || ''}
		question={currentQuestion || ({ questionId: '' } as AuditQuestion)}
		onAnswer={handleAnswer}
		onPrevious={handlePrevious}
		onNext={handleNext}
		isFirst={currentCategory === 0 && currentQuestionIndex === 0}
		{isLast}
		onClose={handleDialogClose}
		onShowConfirm={handleShowConfirmDialog}
		{categories}
		{categoryIds}
		{currentCategory}
		{answers}
		questions={data.evaluation?.[0]?.questions || {}}
		onCategoryChange={switchCategory}
		realDuration={displayTime}
		auditData={data.evaluation?.[0]}
		auditInstanceEvaluationConfig={data.auditInstanceEvaluationConfig}
	/>
{/if}

<ConfirmDialog
	open={showConfirmDialog}
	title={$t('audits.auditEvaluation.confirmDialog.title')}
	description={$t('audits.auditEvaluation.confirmDialog.description')}
	confirmButtonText={$t('audits.auditEvaluation.confirmDialog.confirmButton')}
	closeButtonText={$t('audits.auditEvaluation.confirmDialog.closeButton')}
	onClose={handleConfirmDialogClose}
	onConfirm={handleConfirmDialogConfirm}
/>

<CustomDialog
	open={showQrDialog}
	subtitle={data.evaluation[0].code}
	onClose={() => (showQrDialog = false)}
	title={$t('audits.dialogs.launchAudit')}
>
	<div class="flex flex-col items-center justify-center space-y-4 p-4">
		{#if qrCodeUrl}
			<img src={qrCodeUrl} alt="QR Code" class="rounded-lg" />
		{:else}
			<div class="flex h-64 w-64 items-center justify-center rounded-lg border bg-gray-100">
				<LoaderCircle class="h-8 w-8 animate-spin text-gray-400" />
			</div>
		{/if}
	</div>
</CustomDialog>

<!-- Hidden form for completing audit -->
<form
	method="POST"
	action="?/completeAudit"
	use:enhance={() => {
		isCompletingAudit = true;
		return async ({ result }) => {
			if (result.type === 'success') {
				toast.success($t('audits.messages.auditCompleted'));
				goto(
					`/${page.params.companySlug}/${page.params.plantSlug}/audits/${page.params.id}/results`
				);
			} else {
				isCompletingAudit = false;
			}
		};
	}}
	style="display: none;"
>
	<input type="hidden" name="realDuration" value={displayTime} />
	<input type="hidden" name="answers" value={JSON.stringify(answers)} />
	<button type="submit" id="complete-audit-form-submit">Submit</button>
</form>
