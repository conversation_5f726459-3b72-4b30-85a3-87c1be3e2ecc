<script lang="ts">
	import type { AttachmentFormProps } from '$lib/DTO/audits/auditEvaluation';
	import { t } from '$lib/translations';
	import FileUpload from '../_components/file-upload.svelte';

	let {
		question,
		files = [],
		onFileAdd,
		onFileRemove,
		trackUpload,
		untrackUpload
	}: AttachmentFormProps = $props();
</script>

<div class="flex flex-col gap-4">
	<hr class="border-2" />
	<div class="flex items-start justify-between">
		<div class="max-h-[120px] overflow-y-auto pr-2 text-base text-[#4B505A]">
			{question.text}
		</div>
	</div>

	<hr class="border-2" />

	<!-- Attachments -->
	<div class="mt-4">
		<h3 class="mb-4 text-sm font-medium text-[#4B505A]">
			{$t('audits.auditEvaluation.attachments.attachments')} ({files.length})
		</h3>
		<div class="w-full">
			<FileUpload {files} {onFileAdd} {onFileRemove} {trackUpload} {untrackUpload} />
		</div>
	</div>
</div>
