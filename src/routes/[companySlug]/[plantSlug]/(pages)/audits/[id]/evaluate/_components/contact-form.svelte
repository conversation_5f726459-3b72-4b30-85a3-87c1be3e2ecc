<script lang="ts">
	import * as Avatar from '$lib/components/ui/avatar/index';
	import Textarea from '$lib/components/ui/textarea/textarea.svelte';
	import type { AuditQuestion } from '$lib/DTO/audits/auditEvaluation';
	import { Send } from '@lucide/svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { contactMessageSchema, type ContactMessageForm } from '$lib/schemas/messages';
	import * as Form from '$lib/components/ui/form/index';
	import { toast } from 'svelte-sonner';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import { t, locale } from '$lib/translations';

	let { question, auditData, onBack } = $props<{
		question: AuditQuestion;
		auditData: any;
		onBack: () => void;
	}>();

	const defaultForm = {
		message: '',
		questionId: question.id,
		templateAuditorId: '',
		workplaceManagerId: '',
		auditTypeName: ''
	};

	let auditTypeResponsiblePerson = $derived(auditData?.auditTypeResponsiblePerson!);
	let workplaceResponsiblePerson = $derived(auditData?.workplaceResponsiblePerson!);

	let sendToWorkplaceManager = $state(false);
	let sendToTemplateAuditor = $state(false);

	let isSending = $state(false);

	const form = superForm<ContactMessageForm>(
		{ ...defaultForm },
		{
			validators: zodClient(contactMessageSchema),
			onResult: ({ result }) => {
				if (result.type === 'success') {
					isSending = true;
					form.reset();
					sendToTemplateAuditor = false;
					sendToWorkplaceManager = false;
					toast.success($t('audits.auditEvaluation.contact.messageSent'));
					isSending = false;
					onBack();
				} else {
					isSending = false;
				}
			}
		}
	);

	const { form: formData, enhance, errors } = form;
</script>

<div>
	<hr class="my-4 border-2" />
	<div class="mb-4 text-base text-[#4B505A]">
		{question.text}
	</div>
	<hr class="my-4 border-2" />

	<form method="POST" action="?/sendEmail" use:enhance>
		{#if sendToTemplateAuditor}
			<input type="hidden" name="templateAuditorId" value={auditTypeResponsiblePerson?.id} />
		{/if}

		{#if sendToWorkplaceManager}
			<input type="hidden" name="workplaceManagerId" value={workplaceResponsiblePerson?.id} />
		{/if}
		<input type="hidden" name="questionId" value={question.id} />
		<input type="hidden" name="auditTypeName" value={auditData?.auditType?.name} />

		<div class="flex flex-row items-center gap-2">
			<div class="min-w-0 flex-1">
				<button
					type="button"
					class="w-full cursor-pointer rounded-full px-2 py-2 transition-colors duration-200 {sendToTemplateAuditor
						? 'bg-[#414E6B] hover:bg-[#414E6B]/80'
						: 'bg-[#EAEDF5] hover:bg-[#EAEDF5]/80'}"
					onclick={() => (sendToTemplateAuditor = !sendToTemplateAuditor)}
				>
					<div class="flex flex-row items-center gap-2">
						<Avatar.Root class="h-8 w-8">
							<Avatar.Fallback
								class="text-sm transition-colors duration-200 {sendToTemplateAuditor
									? 'bg-[#101826] text-white'
									: 'bg-white text-black'}"
							>
								{auditTypeResponsiblePerson?.firstName?.charAt(
									0
								)}{auditTypeResponsiblePerson?.lastName?.charAt(0)}
							</Avatar.Fallback>
						</Avatar.Root>
						<div class="flex min-w-0 flex-col py-1 text-left">
							<span
								class="-mb-0.5 truncate text-sm font-semibold {sendToTemplateAuditor
									? 'text-[#7D9AD3]'
									: 'text-[#101826]'}"
								>{auditTypeResponsiblePerson?.firstName}
								{auditTypeResponsiblePerson?.lastName}</span
							>
							<span
								class="-mt-0.5 truncate text-xs {sendToTemplateAuditor
									? 'text-[#F1F4FA]'
									: 'text-[#4B505A]'}"
							>
								{$t('audits.auditEvaluation.contact.responsiblePersonLabel')}: {auditData?.auditType
									?.name}
							</span>
						</div>
					</div>
				</button>
			</div>

			<div class="min-w-0 flex-1">
				<button
					type="button"
					class="w-full cursor-pointer rounded-full px-2 py-2 transition-colors duration-200 {sendToWorkplaceManager
						? 'bg-[#414E6B] hover:bg-[#414E6B]/80'
						: 'bg-[#EAEDF5] hover:bg-[#EAEDF5]/80'}"
					onclick={() => (sendToWorkplaceManager = !sendToWorkplaceManager)}
				>
					<div class="flex flex-row items-center gap-2">
						<Avatar.Root class="h-8 w-8">
							<Avatar.Fallback
								class="text-sm transition-colors duration-200 {sendToWorkplaceManager
									? 'bg-[#101826] text-white'
									: 'bg-white text-black'}"
							>
								{workplaceResponsiblePerson?.firstName?.charAt(
									0
								)}{workplaceResponsiblePerson?.lastName?.charAt(0)}
							</Avatar.Fallback>
						</Avatar.Root>
						<div class="flex min-w-0 flex-col py-1 text-left">
							<span
								class="-mb-0.5 truncate text-sm font-semibold {sendToWorkplaceManager
									? 'text-[#7D9AD3]'
									: 'text-[#101826]'}"
								>{workplaceResponsiblePerson?.firstName}
								{workplaceResponsiblePerson?.lastName}</span
							>
							<span
								class="-mt-0.5 truncate text-xs {sendToWorkplaceManager
									? 'text-[#F1F4FA]'
									: 'text-[#4B505A]'}"
							>
								{$t('audits.auditEvaluation.contact.responsiblePersonLabel')}: {auditData?.workplace
									?.name}
							</span>
						</div>
					</div>
				</button>
			</div>

			{#if isSending}
				<div class="shrink-0">
					<button
						type="button"
						class="flex size-12 cursor-pointer items-center justify-center rounded-xl bg-[#7D9AD3] text-white transition-colors duration-200 hover:bg-[#7D9AD3]/80"
						disabled
						aria-label="Loading"
					>
						<div class="h-6 w-6 animate-spin">
							<svg viewBox="0 0 24 24" fill="none" class="size-full">
								<circle
									class="opacity-25"
									cx="12"
									cy="12"
									r="10"
									stroke="currentColor"
									stroke-width="4"
								></circle>
								<path
									class="opacity-75"
									fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
								></path>
							</svg>
						</div>
					</button>
				</div>
			{:else}
				<ButtonIcon
					Icon={Send}
					class="size-12"
					backgroundColor="#7D9AD3"
					hoverBackgroundColor="#7D9AD3"
					hoverOpacity="80"
					onClickAction={() => {
						if (!sendToTemplateAuditor && !sendToWorkplaceManager) {
							toast.error($t('audits.auditEvaluation.contact.atLeastOne'));
							return;
						}
						isSending = true;
						form.submit();
					}}
				/>
			{/if}
		</div>

		<div class="my-6">
			<Form.Field {form} name="message">
				<Form.Control>
					{#snippet children({ props })}
						<Textarea
							{...props}
							bind:value={$formData.message}
							class="min-h-32 bg-white {$errors.message ? 'border-red-500' : ''}"
							placeholder={$t('audits.auditEvaluation.contact.messagePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
			</Form.Field>
		</div>
	</form>
</div>
