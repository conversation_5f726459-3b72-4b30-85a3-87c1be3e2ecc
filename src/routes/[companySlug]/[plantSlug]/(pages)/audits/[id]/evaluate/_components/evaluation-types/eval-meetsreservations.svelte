<script lang="ts">
	import { t } from '$lib/translations';
	import { Ban, Check, TriangleAlert } from '@lucide/svelte';
	import { formatEvaluationValue } from '../../../results/_utils/evaluation-value';

	interface Props {
		value?: string | null;
		onValueChange?: (value: string | null) => void;
		auditInstanceEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
	}

	let { value = null, onValueChange, auditInstanceEvaluationConfig }: Props = $props();

	function handleOptionSelect(option: string) {
		// Toggle selection - if same option is clicked, deselect it
		const newValue = value === option ? null : option;
		onValueChange?.(newValue);
	}
</script>

<div class="grid w-full grid-cols-3 gap-4">
	<!-- Meets button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#83B091] transition-colors"
		style={value === 'meets'
			? `background-color: ${formatEvaluationValue('meets', 'meetsreservations', auditInstanceEvaluationConfig)}; color: white;`
			: 'background: transparent; color: #697691;'}
		onclick={() => handleOptionSelect('meets')}
	>
		<Check class="h-6 w-6" />
		<span class="text-sm md:text-lg">{$t('audits.evaluationTypes.evaluationValues.meets')}</span>
	</button>

	<!-- /w Reservations button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#BCB36F] transition-colors"
		style={value === 'wreservations'
			? `background-color: ${formatEvaluationValue('wreservations', 'meetsreservations', auditInstanceEvaluationConfig)}; color: white;`
			: 'background: transparent; color: #697691;'}
		onclick={() => handleOptionSelect('wreservations')}
	>
		<TriangleAlert class="h-6 w-6" />
		<span class="text-sm md:text-lg"
			>{$t('audits.evaluationTypes.evaluationValues.wreservations')}</span
		>
	</button>

	<!-- Doesnt meet button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#9E6D6B] transition-colors"
		style={value === 'doesntmeet'
			? `background-color: ${formatEvaluationValue('doesntmeet', 'meetsreservations', auditInstanceEvaluationConfig)}; color: white;`
			: 'background: transparent; color: #697691;'}
		onclick={() => handleOptionSelect('doesntmeet')}
	>
		<Ban class="h-6 w-6" />
		<span class="text-sm md:text-lg"
			>{$t('audits.evaluationTypes.evaluationValues.doesntmeet')}</span
		>
	</button>
</div>
