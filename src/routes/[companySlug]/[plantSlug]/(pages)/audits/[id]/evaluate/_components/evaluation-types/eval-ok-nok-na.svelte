<script lang="ts">
	import { Check, Ban, ShieldQuestion } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { formatEvaluationValue } from '../../../results/_utils/evaluation-value';

	interface Props {
		value?: string | null;
		onValueChange: (value: string | null) => void;
		auditInstanceEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
	}

	let { value = null, onValueChange, auditInstanceEvaluationConfig }: Props = $props();
	function handleOptionSelect(option: string) {
		// Toggle logic - if same option is clicked, deselect it
		if (value === option) {
			onValueChange(null);
		} else {
			onValueChange(option);
		}
	}
</script>

<div class="grid w-full grid-cols-3 gap-4">
	<!-- OK button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#83B091] transition-colors"
		style={value === 'ok'
			? `background-color: ${formatEvaluationValue('ok', 'oknok', auditInstanceEvaluationConfig)};color: white;`
			: 'background: transparent;  color: #697691;'}
		onclick={() => handleOptionSelect('ok')}
	>
		<Check class="h-6 w-6" />
		<span class="text-lg">OK</span>
	</button>

	<!-- NOK button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#9E6D6B] transition-colors"
		style={value === 'nok'
			? `background-color: ${formatEvaluationValue('nok', 'oknok', auditInstanceEvaluationConfig)}; color: white;`
			: 'background: transparent; color: #697691;'}
		onclick={() => handleOptionSelect('nok')}
	>
		<Ban class="h-6 w-6" />
		<span class="text-lg">NOK</span>
	</button>

	<!-- N/A button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#414E6B] transition-colors"
		style={value === 'na'
			? `background-color: ${formatEvaluationValue('na', 'oknok', auditInstanceEvaluationConfig)};  color: white;`
			: 'background: transparent;color: #697691;'}
		onclick={() => handleOptionSelect('na')}
	>
		<ShieldQuestion class="h-6 w-6" />
		<span class="text-lg">N/A</span>
	</button>
</div>
