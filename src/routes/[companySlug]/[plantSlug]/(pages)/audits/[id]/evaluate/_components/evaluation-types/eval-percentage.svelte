<script lang="ts">
	import { Slider } from 'bits-ui';
	import { formatEvaluationValue } from '../../../results/_utils/evaluation-value';

	interface Props {
		value?: string | null;
		onValueChange: (value: string | null) => void;
		min?: number;
		max?: number;
		auditInstanceEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
	}

	let {
		value = null,
		onValueChange,
		min = 0,
		max = 100,
		auditInstanceEvaluationConfig
	}: Props = $props();

	let currentValue = $state(value ? parseInt(value) : min);
	let showTooltip = $state(false);

	function updateValue(newValue: number) {
		const clampedValue = Math.max(min, Math.min(max, newValue));
		currentValue = clampedValue;
		onValueChange(clampedValue.toString());
	}

	$effect(() => {
		if (value !== null) {
			const numValue = parseInt(value);
			if (!isNaN(numValue)) {
				currentValue = Math.max(min, Math.min(max, numValue));
			}
		}
	});
</script>

<div class="my-4 flex w-full flex-col items-center space-y-4">
	<!-- Slider -->
	<div class="w-full max-w-sm">
		<Slider.Root
			type="single"
			bind:value={currentValue}
			onValueChange={updateValue}
			{min}
			{max}
			step={1}
			class="relative flex w-full touch-none items-center select-none"
		>
			<span
				class="relative h-2 w-full grow cursor-pointer overflow-hidden rounded-full bg-gray-200"
			>
				<Slider.Range
					class="absolute h-full transition-colors duration-300"
					style={`background-color: ${formatEvaluationValue(String(currentValue), 'percentage', auditInstanceEvaluationConfig)}`}
				/>
			</span>
			<Slider.Thumb
				index={0}
				class="z-5 block size-[20px] cursor-pointer rounded-full border-2 border-[#414E6B] bg-white shadow-sm transition-colors hover:border-[#697691] focus-visible:ring-2 focus-visible:ring-[#414E6B] focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 data-active:scale-[0.98] data-active:border-[#697691]"
				onmouseenter={() => (showTooltip = true)}
				onmouseleave={() => {
					showTooltip = false;
				}}
				onpointerdown={() => {
					showTooltip = true;
				}}
				onpointerup={() => {
					showTooltip = false;
				}}
			/>
			{#if showTooltip}
				<Slider.ThumbLabel
					index={0}
					position="top"
					class="mb-5 rounded-md bg-[#414E6B] px-2 py-1 text-sm text-nowrap text-white"
				>
					{currentValue}%
				</Slider.ThumbLabel>
			{/if}
		</Slider.Root>
	</div>

	<!-- Range -->
	<div class="flex w-full max-w-sm justify-between text-xs font-semibold text-[#B1B7C3]">
		<span>{min}%</span>
		<span class="font-bold text-[#414E6B]">{currentValue}%</span>
		<span>{max}%</span>
	</div>
</div>
