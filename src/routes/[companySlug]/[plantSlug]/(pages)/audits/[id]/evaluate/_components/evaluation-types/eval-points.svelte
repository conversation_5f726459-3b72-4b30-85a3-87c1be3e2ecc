<script lang="ts">
	import { Minus, Plus } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import Button from '$lib/components/ui/button/button.svelte';
	import { t } from '$lib/translations';

	interface Props {
		value?: string | null;
		onValueChange: (value: string | null) => void;
		min?: number;
		max?: number;
		auditInstanceEvaluationConfig?: any;
	}

	let {
		value = null,
		onValueChange,
		min = 0,
		max = 10,
		auditInstanceEvaluationConfig = null
	}: Props = $props();

	// Use plant evaluation config if available
	const actualMin = auditInstanceEvaluationConfig?.pointsRangeMin ?? min;
	const actualMax = auditInstanceEvaluationConfig?.pointsRangeMax ?? max;

	// Calculate middle value for unvisited questions
	const middleValue = Math.round((actualMin + actualMax) / 2);

	let currentValue = $state(value ? parseInt(value) : middleValue);
	let hasUserInteracted = false;

	function updateValue(newValue: number) {
		const clampedValue = Math.max(actualMin, Math.min(actualMax, newValue));
		if (currentValue !== clampedValue) {
			currentValue = clampedValue;
			hasUserInteracted = true;
			onValueChange(clampedValue.toString());
		} else {
			// I když je stejná, zavoláme onValueChange pro jistotu reaktivity parentu
			hasUserInteracted = true;
			onValueChange(clampedValue.toString());
		}
	}

	function decrement() {
		if (currentValue > actualMin) {
			updateValue(currentValue - 1);
		}
	}

	function increment() {
		if (currentValue < actualMax) {
			updateValue(currentValue + 1);
		}
	}

	function handleInputChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = parseInt(target.value);

		if (!isNaN(newValue)) {
			updateValue(newValue);
		}
	}

	$effect(() => {
		if (value !== null && value !== undefined && value !== '') {
			const numValue = parseInt(value);
			if (!isNaN(numValue)) {
				currentValue = Math.max(actualMin, Math.min(actualMax, numValue));
			}
		} else {
			// Pokud není hodnota, nastavíme medián, ale NEvoláme onValueChange (nechceme autosave)
			currentValue = middleValue;
			hasUserInteracted = false;
		}
	});
</script>

<div class="flex w-full justify-center">
	<div
		class="inline-flex items-center overflow-hidden rounded-lg border-2 border-[#414E6B] bg-white shadow-sm"
	>
		<Button
			onclick={decrement}
			disabled={currentValue <= actualMin}
			class="flex h-18 w-20 items-center justify-center border-r border-[#414E6B] bg-transparent text-[#697691] transition-colors hover:bg-[#697691]/10 disabled:cursor-not-allowed disabled:opacity-50 md:w-24"
		>
			<Minus class="h-6 w-6 md:h-5 md:w-5" />
		</Button>

		<!-- Number input -->
		<div class="flex h-18 w-24 items-center justify-center px-3 md:w-32 md:px-4">
			<input
				type="number"
				value={currentValue}
				oninput={handleInputChange}
				min={actualMin}
				max={actualMax}
				class="font-titillium w-full border-none bg-transparent text-center text-xl font-bold text-[#4B505A] outline-none [-moz-appearance:textfield] focus:ring-0 md:text-2xl [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
			/>
		</div>

		<!-- Plus button -->
		<Button
			onclick={increment}
			disabled={currentValue >= actualMax}
			class="flex h-18 w-20 items-center justify-center border-l border-[#414E6B] bg-transparent text-[#697691] transition-colors hover:bg-[#697691]/10 disabled:cursor-not-allowed disabled:opacity-50 md:w-24"
		>
			<Plus class="h-6 w-6 md:h-5 md:w-5" />
		</Button>
	</div>
</div>

<!-- Show min/max range below -->
<div class="mt-2 text-center text-xs text-[#B1B7C3]">
	{$t('audits.evaluationTypes.pointsRange')
		.replace('{min}', actualMin.toString())
		.replace('{max}', actualMax.toString())}
</div>
