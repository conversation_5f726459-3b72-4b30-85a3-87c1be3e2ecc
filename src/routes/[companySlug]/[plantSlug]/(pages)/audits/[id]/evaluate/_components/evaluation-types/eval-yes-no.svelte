<script lang="ts">
	import { t } from '$lib/translations';
	import { Ban, Check } from '@lucide/svelte';
	import { formatEvaluationValue } from '../../../results/_utils/evaluation-value';

	interface Props {
		value?: string | null;
		onValueChange?: (value: string | null) => void;
		auditInstanceEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
	}

	let { value = null, onValueChange, auditInstanceEvaluationConfig }: Props = $props();

	function handleOptionSelect(option: string) {
		// Toggle selection - if same option is clicked, deselect it
		const newValue = value === option ? null : option;
		onValueChange?.(newValue);
	}
</script>

<div class="grid w-full grid-cols-2 gap-4">
	<!-- Yes button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#83B091] transition-colors"
		style={value === 'yes'
			? `background-color: ${formatEvaluationValue('yes', 'yesno', auditInstanceEvaluationConfig)}; color: white;`
			: 'background: transparent; color: #697691;'}
		onclick={() => handleOptionSelect('yes')}
	>
		<Check class="h-6 w-6" />
		<span class="text-lg">{$t('audits.evaluationTypes.evaluationValues.yes')}</span>
	</button>

	<!-- No button -->
	<button
		type="button"
		class="font-titillium flex min-h-[80px] w-full cursor-pointer flex-col items-center justify-center gap-0.5 rounded-md border-2 border-[#9E6D6B] transition-colors"
		style={value === 'no'
			? `background-color: ${formatEvaluationValue('no', 'yesno', auditInstanceEvaluationConfig)}; color: white;`
			: 'background: transparent; color: #697691;'}
		onclick={() => handleOptionSelect('no')}
	>
		<Ban class="h-6 w-6" />
		<span class="text-lg">{$t('audits.evaluationTypes.evaluationValues.no')}</span>
	</button>
</div>
