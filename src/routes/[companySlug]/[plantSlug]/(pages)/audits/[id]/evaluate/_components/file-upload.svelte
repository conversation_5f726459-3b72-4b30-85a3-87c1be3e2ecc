<script lang="ts">
	import { t } from '$lib/translations';
	import { X, Upload, FileText, Loader2 } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';
	import type { AttachmentFile } from '$lib/DTO/audits/auditEvaluation';
	import { validateFile } from '../../_utils/validation';
	import { MAX_FILE_SIZE } from '$lib/constants/file';
	import { onDestroy } from 'svelte';
	import {
		uploadFileComplete,
		uploadFileCompleteWithTracking,
		createFilePreview,
		revokeFilePreview,
		uploadResultToAttachment,
		type UploadProgress
	} from '$lib/utils/s3Upload';
	import { page } from '$app/state';

	let {
		files = [],
		onFileAdd,
		onFileRemove,
		trackUpload,
		untrackUpload
	} = $props<{
		files: AttachmentFile[];
		onFileAdd: (file: AttachmentFile) => void;
		onFileRemove: (index: number) => Promise<void>;
		trackUpload?: (uploadId: string) => void;
		untrackUpload?: (uploadId: string) => void;
	}>();

	let fileInput: HTMLInputElement;
	let isDragging = $state(false);
	// Track files being uploaded
	let uploadingFiles = $state<
		Array<{
			file: File;
			progress: UploadProgress;
			previewUrl: string;
		}>
	>([]);

	let hasFiles = $derived(files.length > 0 || uploadingFiles.length > 0);
	let isUploading = $state(false);
	// Track which files are being deleted (by index)
	let deletingFiles = $state<Set<number>>(new Set());

	onDestroy(() => {
		// Cleanup uploading file preview URLs
		uploadingFiles.forEach((item) => revokeFilePreview(item.previewUrl));
	});

	async function handleFileChange(event: Event) {
		const input = event.target as HTMLInputElement;
		if (!input.files || input.files.length === 0) return;

		await processFiles(Array.from(input.files));
		input.value = '';
	}

	function handleDrop(e: DragEvent) {
		e.preventDefault();
		isDragging = false;

		const dropFiles = e.dataTransfer?.files;
		if (!dropFiles || dropFiles.length === 0) return;

		processFiles(Array.from(dropFiles));
	}

	async function processFiles(filesToUpload: File[]) {
		if (isUploading) return;

		isUploading = true;

		try {
			for (const file of filesToUpload) {
				const validation = validateFile(file);

				if (!validation.valid) {
					toast.error(validation.message || 'Invalid file');
					continue;
				}

				await uploadSingleFile(file);
			}
		} catch (error) {
			console.error('Error processing files:', error);
			toast.error($t('audits.auditEvaluation.errors.fileUploadFailed'));
		} finally {
			isUploading = false;
		}
	}

	async function uploadSingleFile(file: File) {
		// Create preview URL immediately
		const previewUrl = createFilePreview(file);

		// Add to uploading files for immediate UI feedback
		const uploadItem = {
			file,
			progress: { loaded: 0, total: file.size, percentage: 0 },
			previewUrl
		};
		uploadingFiles = [...uploadingFiles, uploadItem];

		try {
			const companySlug = page.params.companySlug;
			// Get plant slug from URL
			const plantSlug = page.params.plantSlug;

			// Upload to S3 with progress tracking and auto-save integration
			const result = await uploadFileCompleteWithTracking(
				file,
				companySlug,
				plantSlug,
				'audits_attachments',
				(progress) => {
					// Update progress for this specific file
					const index = uploadingFiles.findIndex((item) => item.file === file);
					if (index !== -1) {
						uploadingFiles[index].progress = progress;
						uploadingFiles = [...uploadingFiles]; // Trigger reactivity
					}
				},
				trackUpload,
				untrackUpload
			);

			// Remove from uploading files
			uploadingFiles = uploadingFiles.filter((item) => item.file !== file);

			if (result.success) {
				// Convert to AttachmentFile and add to completed files
				const attachment = uploadResultToAttachment(result, file);
				if (attachment) {
					onFileAdd(attachment);
					// Toast removed - will be handled by autosave
				}
			} else {
				throw new Error(result.error || 'Upload failed');
			}
		} catch (error) {
			// Remove from uploading files on error
			uploadingFiles = uploadingFiles.filter((item) => item.file !== file);

			console.error('Upload failed:', error);
			toast.error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
		} finally {
			// Cleanup preview URL
			revokeFilePreview(previewUrl);
		}
	}

	function handleDragEnter(e: DragEvent) {
		e.preventDefault();
		isDragging = true;
	}

	function handleDragLeave(e: DragEvent) {
		e.preventDefault();
		isDragging = false;
	}

	async function handleClick(e: Event, index: number) {
		e.preventDefault();
		e.stopPropagation();

		// Prevent multiple deletions of the same file
		if (deletingFiles.has(index)) {
			return;
		}

		// Mark file as being deleted
		deletingFiles.add(index);
		deletingFiles = new Set(deletingFiles); // Trigger reactivity

		try {
			await onFileRemove(index);
		} finally {
			// Remove from deleting state
			deletingFiles.delete(index);
			deletingFiles = new Set(deletingFiles); // Trigger reactivity
		}
	}
</script>

<div class="flex flex-row flex-wrap items-start gap-4 overflow-x-auto">
	<!-- Upload box -->
	<div
		class="flex h-[150px] w-[150px] shrink-0 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-[#E2E8F0] bg-white transition-colors hover:border-[#CBD5E1] hover:bg-[#F8FAFC]"
		class:border-[#94A3B8]={isDragging}
		class:cursor-not-allowed={isUploading}
		class:opacity-70={isUploading}
		onclick={() => !isUploading && fileInput.click()}
		ondragenter={handleDragEnter}
		ondragleave={handleDragLeave}
		ondrop={handleDrop}
		ondragover={(e) => e.preventDefault()}
		onkeydown={(e) => e.key === 'Enter' && !isUploading && fileInput.click()}
		role="button"
		tabindex="0"
		aria-label={$t('audits.auditEvaluation.uploadFile')}
	>
		<div class="mb-2">
			{#if isUploading}
				<div class="h-10 w-10 animate-spin text-[#64748B]">
					<svg viewBox="0 0 24 24" fill="none" class="size-full">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
						></circle>
						<path
							class="opacity-75"
							fill="currentColor"
							d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
						></path>
					</svg>
				</div>
			{:else}
				<Upload class="h-10 w-10 text-[#64748B]" />
			{/if}
		</div>
		<div class="px-2 text-center">
			<p class="mb-1 text-sm font-medium text-[#64748B]">
				{isUploading
					? $t('audits.auditEvaluation.attachments.uploading')
					: $t('audits.auditEvaluation.attachments.loadFile')}
			</p>
			<p class="mb-1 text-xs text-[#94A3B8]">
				{isUploading
					? $t('audits.auditEvaluation.attachments.wait')
					: $t('audits.auditEvaluation.attachments.clickChoose')}
			</p>
			<p class="text-[10px] text-[#94A3B8]">Max. {MAX_FILE_SIZE / 1024 / 1024} MB</p>
		</div>
	</div>

	{#if hasFiles}
		<!-- Completed/uploaded files -->
		{#each files as file, index}
			<div
				class="group relative h-[150px] w-[150px] shrink-0 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm"
			>
				<div
					class="absolute -inset-0.5 z-0 rounded-lg bg-linear-to-tr from-blue-200 to-blue-400 opacity-30"
				></div>

				<div class="relative z-10 h-full w-full">
					{#if file.type.startsWith('image/')}
						<!-- Use previewUrl for newly uploaded files, otherwise use signed URL -->
						<img
							src={file.previewUrl || file.url}
							alt={file.filename}
							class="h-full w-full object-cover"
						/>
					{:else}
						<div class="flex h-full w-full flex-col items-center justify-center bg-[#F8FAFC]">
							<FileText class="h-10 w-10 text-[#64748B]" />
							<span class="mt-2 line-clamp-2 px-2 text-center text-xs text-[#64748B]"
								>{file.filename}</span
							>
						</div>
					{/if}
				</div>
				<div
					class="absolute inset-0 z-20 flex items-center justify-center gap-1 bg-black/50 transition-opacity"
					class:opacity-0={!deletingFiles.has(index)}
					class:opacity-100={deletingFiles.has(index)}
					class:group-hover:opacity-100={!deletingFiles.has(index)}
				>
					<button
						type="button"
						class="cursor-pointer rounded-md p-2 text-white/90 transition-colors hover:bg-white/20 hover:text-white"
						class:cursor-not-allowed={deletingFiles.has(index)}
						class:opacity-50={deletingFiles.has(index)}
						onclick={(e) => handleClick(e, index)}
						disabled={deletingFiles.has(index)}
						aria-label="Delete file"
					>
						{#if deletingFiles.has(index)}
							<Loader2 class="h-5 w-5 animate-spin" />
						{:else}
							<X class="h-5 w-5" />
						{/if}
					</button>
				</div>
			</div>
		{/each}

		<!-- Files being uploaded -->
		{#each uploadingFiles as uploadItem, index}
			<div
				class="group relative h-[150px] w-[150px] shrink-0 overflow-hidden rounded-lg border border-yellow-200 bg-white shadow-sm"
			>
				<div
					class="absolute -inset-0.5 z-0 rounded-lg bg-linear-to-tr from-yellow-200 to-yellow-400 opacity-30"
				></div>

				<div class="relative z-10 h-full w-full">
					{#if uploadItem.file.type.startsWith('image/')}
						<img
							src={uploadItem.previewUrl}
							alt={uploadItem.file.name}
							class="h-full w-full object-cover"
						/>
					{:else}
						<div class="flex h-full w-full flex-col items-center justify-center bg-[#F8FAFC]">
							<FileText class="h-10 w-10 text-[#64748B]" />
							<span class="mt-2 line-clamp-2 px-2 text-center text-xs text-[#64748B]"
								>{uploadItem.file.name}</span
							>
						</div>
					{/if}
				</div>

				<!-- Progress overlay -->
				<div class="absolute inset-0 z-20 flex items-center justify-center bg-black/60">
					<div class="text-center text-white">
						<div class="mb-2">
							<div class="mx-auto h-8 w-8 animate-spin">
								<svg viewBox="0 0 24 24" fill="none" class="size-full">
									<circle
										class="opacity-25"
										cx="12"
										cy="12"
										r="10"
										stroke="currentColor"
										stroke-width="4"
									></circle>
									<path
										class="opacity-75"
										fill="currentColor"
										d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
									></path>
								</svg>
							</div>
						</div>
						<div class="text-xs font-medium">{uploadItem.progress.percentage}%</div>
						<div class="text-[10px] opacity-75">
							{$t('audits.auditEvaluation.attachments.uploading')}
						</div>
					</div>
				</div>
			</div>
		{/each}
	{/if}
</div>

<input
	type="file"
	accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
	class="hidden"
	bind:this={fileInput}
	onchange={handleFileChange}
	capture="environment"
	multiple
	disabled={isUploading}
/>

<div class="mt-2 hidden text-xs text-gray-500 sm:block">
	{$t('audits.auditEvaluation.attachments.maxSize')}: {MAX_FILE_SIZE / 1024 / 1024} MB.
</div>
