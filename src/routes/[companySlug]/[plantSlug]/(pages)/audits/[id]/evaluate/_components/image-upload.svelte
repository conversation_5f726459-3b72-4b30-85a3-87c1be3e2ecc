<script lang="ts">
	import { t } from '$lib/translations';
	import { X, Upload, Replace } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';

	let {
		imageUrl = null,
		onImageChange,
		onImageRemove
	} = $props<{
		imageUrl: string | null;
		onImageChange: (file: File, previewUrl: string) => void;
		onImageRemove: () => void;
	}>();

	let imageInput: HTMLInputElement;
	let isDragging = $state(false);

	async function handleImageChange(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			try {
				const file = input.files[0];
				const reader = new FileReader();

				reader.onload = (e) => {
					const previewUrl = e.target?.result as string;
					onImageChange(file, previewUrl);
				};

				reader.readAsDataURL(file);
			} catch (error) {
				console.error('Error uploading image:', error);
				toast.error($t('audits.auditEvaluation.errors.imageUploadFailed'));
			}
		}
	}

	function handleDragEnter(e: DragEvent) {
		e.preventDefault();
		isDragging = true;
	}

	function handleDragLeave(e: DragEvent) {
		e.preventDefault();
		isDragging = false;
	}

	function handleDrop(e: DragEvent) {
		e.preventDefault();
		isDragging = false;

		const file = e.dataTransfer?.files[0];
		if (file && file.type.startsWith('image/')) {
			const reader = new FileReader();
			reader.onload = (e) => {
				const previewUrl = e.target?.result as string;
				onImageChange(file, previewUrl);
			};
			reader.readAsDataURL(file);
		}
	}
</script>

<div class="flex items-center justify-center gap-3">
	{#if !imageUrl}
		<button
			type="button"
			class="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-[#E2E8F0] bg-[#F8FAFC] transition-colors hover:border-[#CBD5E1] hover:bg-[#F1F5F9]"
			class:border-[#94A3B8]={isDragging}
			onclick={() => imageInput.click()}
			ondragenter={handleDragEnter}
			ondragleave={handleDragLeave}
			ondrop={handleDrop}
			onkeydown={(e) => e.key === 'Enter' && imageInput.click()}
			aria-label={$t('audits.auditEvaluation.attachments.loadFile')}
		>
			<Upload class="h-5 w-5 text-[#64748B]" />
		</button>
	{:else}
		<div class="group relative h-24 w-24">
			<img
				src={imageUrl}
				alt={$t('audits.auditEvaluation.imagePreview')}
				class="h-24 w-24 rounded-lg object-cover"
			/>
			<div
				class="absolute inset-0 flex items-center justify-center gap-1 rounded-lg bg-black/50 opacity-0 transition-opacity group-hover:opacity-100"
			>
				<button
					type="button"
					class="rounded-md p-1.5 text-white/90 transition-colors hover:bg-white/20 hover:text-white"
					onclick={() => imageInput.click()}
				>
					<Replace class="h-4 w-4" />
				</button>
				<button
					type="button"
					class="rounded-md p-1.5 text-white/90 transition-colors hover:bg-white/20 hover:text-white"
					onclick={onImageRemove}
				>
					<X class="h-4 w-4" />
				</button>
			</div>
		</div>
	{/if}
</div>

<input
	type="file"
	accept="image/*"
	class="hidden"
	bind:this={imageInput}
	onchange={handleImageChange}
/>
