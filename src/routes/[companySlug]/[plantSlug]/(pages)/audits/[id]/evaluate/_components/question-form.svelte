<script lang="ts">
	import { t } from '$lib/translations';
	import { Textarea } from '$lib/components/ui/textarea';
	import EvalYesNo from './evaluation-types/eval-yes-no.svelte';
	import EvalOkNokNa from './evaluation-types/eval-ok-nok-na.svelte';
	import EvalPoints from './evaluation-types/eval-points.svelte';
	import EvalPercentage from './evaluation-types/eval-percentage.svelte';
	import EvalMeetsreservations from './evaluation-types/eval-meetsreservations.svelte';
	import EvalYesNoInverse from './evaluation-types/eval-yes-no-inverse.svelte';

	let {
		question,
		selectedValue = null,
		note = '',
		onAnswerChange,
		auditInstanceEvaluationConfig,
		userInteractedCallback
	} = $props();

	function handleNoteChange(event: Event) {
		const textarea = event.target as HTMLTextAreaElement;
		userInteractedCallback && userInteractedCallback();
		onAnswerChange(selectedValue, textarea.value);
	}
</script>

<div class="flex flex-col gap-4">
	<hr class="border-2" />
	<div class="flex items-start justify-between">
		<div class="max-h-[120px] overflow-y-auto pr-2 text-base text-[#4B505A]">
			{question.text}
			{#if !question.required}
				<span class="ml-2 text-sm text-[#B1B7C3] italic">
					({$t('audits.evaluationTypes.optional')})
				</span>
			{/if}
		</div>
	</div>

	{#if question.subtext}
		<div
			class="font-titillium max-h-[100px] overflow-y-auto rounded-lg bg-[#EAEDF5] px-4 py-2 text-sm text-[#747C8A]"
		>
			{question.subtext}
		</div>
	{/if}

	<div class="mt-2">
		<!-- Evaluation options -->
		{#if question.evaluationType === 'yesno'}
			<EvalYesNo
				value={selectedValue}
				onValueChange={(value) => {
					userInteractedCallback && userInteractedCallback();
					onAnswerChange(value, note);
				}}
				{auditInstanceEvaluationConfig}
			/>
		{:else if question.evaluationType === 'yesno_inverse'}
			<EvalYesNoInverse
				value={selectedValue}
				onValueChange={(value) => {
					userInteractedCallback && userInteractedCallback();
					onAnswerChange(value, note);
				}}
				{auditInstanceEvaluationConfig}
			/>
		{:else if question.evaluationType === 'oknok'}
			<EvalOkNokNa
				value={selectedValue}
				onValueChange={(value) => {
					userInteractedCallback && userInteractedCallback();
					onAnswerChange(value, note);
				}}
				{auditInstanceEvaluationConfig}
			/>
		{:else if question.evaluationType === 'points'}
			<EvalPoints
				value={selectedValue}
				onValueChange={(value) => {
					userInteractedCallback && userInteractedCallback();
					onAnswerChange(value, note);
				}}
				{auditInstanceEvaluationConfig}
			/>
		{:else if question.evaluationType === 'percentage'}
			<EvalPercentage
				value={selectedValue}
				onValueChange={(value) => {
					userInteractedCallback && userInteractedCallback();
					onAnswerChange(value, note);
				}}
				{auditInstanceEvaluationConfig}
			/>
		{:else if question.evaluationType === 'meetsreservations'}
			<EvalMeetsreservations
				value={selectedValue}
				onValueChange={(value) => {
					userInteractedCallback && userInteractedCallback();
					onAnswerChange(value, note);
				}}
				{auditInstanceEvaluationConfig}
			/>
		{/if}
	</div>

	<!-- Note textarea -->
	<div class="">
		<Textarea
			name="note"
			placeholder={$t('audits.auditEvaluation.notePlaceholder')}
			value={note}
			oninput={handleNoteChange}
			class="font-titillium mt-2 h-24 border-2 bg-white placeholder:text-gray-300"
			rows={4}
		/>
	</div>
</div>
