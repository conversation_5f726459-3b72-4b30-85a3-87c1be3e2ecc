import { AuditResultService } from '$lib/server/services/audits';
import { t } from '$lib/translations';
import { createAuditBreadcrumbs } from '../../_utils/createBreadcrumbs';
import { getPlantEvaluationConfig } from '$lib/server/services/tenants/plantEvaluationConfig';
import { EmailService } from '$lib/server/email';
import { fail } from '@sveltejs/kit';
import { formatDate } from '$lib/utils/date';

export const load = async ({ params, parent }) => {
	const { context } = await parent();

	const [auditInfo, questions, answers, plantEvaluationConfig] = await Promise.all([
		AuditResultService.getAuditInfo(params.id),
		AuditResultService.getResultsQuestions(params.id),
		AuditResultService.getAnswersWithRefreshedUrls(params.id),
		getPlantEvaluationConfig(context.plantId)
	]);

	if (!auditInfo) {
		return {
			message: t.get('errors.audits.auditNotFound'),
			auditInfo: null,
			questions: null,
			answers: null
		};
	}

	return {
		auditInfo,
		questions,
		answers,
		auditSuccessRate: auditInfo.successRate,
		plantEvaluationConfig,
		auditEvaluationConfig: auditInfo.evaluationConfig,
		breadcrumbs: createAuditBreadcrumbs(
			params.id,
			auditInfo.code,
			'common.navigation.auditResults',
			context.companySlug,
			context.plantSlug
		)
	};
};

export const actions = {
	sendNegativeCheckpointsEmail: async ({ request, params, url }) => {
		try {
			const formData = await request.formData();
			const negativeCheckpointsJson = formData.get('negativeCheckpoints') as string;
			const recipientEmail = formData.get('recipientEmail') as string;
			const recipientName = formData.get('recipientName') as string;
			const locale = (formData.get('locale') as string) || 'en';

			if (!negativeCheckpointsJson || !recipientEmail || !recipientName) {
				return fail(400, { error: 'Missing required data' });
			}

			const negativeCheckpoints = JSON.parse(negativeCheckpointsJson);

			// Get audit info
			const auditInfo = await AuditResultService.getAuditInfo(params.id);
			if (!auditInfo) {
				return fail(404, { error: 'Audit not found' });
			}

			const auditorName = auditInfo.auditor
				? `${auditInfo.auditor.firstName || ''} ${auditInfo.auditor.lastName || ''}`.trim()
				: 'N/A';

			const workplaceName =
				typeof auditInfo.workplace === 'object' && auditInfo.workplace?.name
					? auditInfo.workplace.name
					: typeof auditInfo.workplace === 'string'
						? auditInfo.workplace
						: 'N/A';

			const auditUrl = `${url.origin}/${params.companySlug}/${params.plantSlug}/audits/${params.id}/results`;

			const emailConfig = {
				recipientName,
				auditTitle: auditInfo.auditType!.name,
				auditCode: auditInfo.code,
				auditDate: formatDate(auditInfo.completionDate || new Date().toISOString(), locale),
				workplaceName: workplaceName,
				auditorName,
				negativeCheckpoints,
				auditUrl,
				locale
			};

			const success = await EmailService.negativeCheckpointsEmail(recipientEmail, emailConfig);

			if (success) {
				return { success: true };
			} else {
				return fail(500, { error: 'Failed to send email' });
			}
		} catch (error) {
			console.error('Error sending negative checkpoints email:', error);
			return fail(500, { error: 'Failed to send email' });
		}
	}
};
