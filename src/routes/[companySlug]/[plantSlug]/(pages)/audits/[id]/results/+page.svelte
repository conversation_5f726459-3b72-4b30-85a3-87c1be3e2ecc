<script lang="ts">
	import { t, locale } from '$lib/translations/index.js';
	import { fade, fly } from 'svelte/transition';
	import AuditResultInfo from './_components/audit-results-info.svelte';
	import AuditResultsAnswers from './_components/audit-results-answers.svelte';
	import { page } from '$app/state';
	import { toast } from 'svelte-sonner';
	import SuccessRateIcon from '$lib/assets/successRateIcon.png';
	import AuditResultScore from './_components/audit-result-score.svelte';

	let { data } = $props();
	let isDownloading = $state(false);

	async function downloadAuditPdf(auditId: string) {
		if (isDownloading) return;

		try {
			isDownloading = true;
			toast.info($t('audits.results.generatingPDF'));

			const response = await fetch(
				`/api/audits/${auditId}/export-pdf?lang=${$locale}&companySlug=${page.params.companySlug}&plantSlug=${page.params.plantSlug}`
			);
			if (!response.ok) throw new Error('Failed to download PDF');

			const blob = await response.blob();

			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `audit-${data.auditInfo!.code || data.auditInfo!.id}.pdf`;
			document.body.appendChild(a);
			a.click();

			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			toast.success($t('audits.results.pdfGenerated'));
		} catch (error) {
			console.error('Error downloading PDF:', error);
		} finally {
			isDownloading = false;
		}
	}
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.auditResults')} - {data.auditInfo!.code}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-2 mb-4 flex h-[calc(100vh-8rem)] w-full gap-2 overflow-y-auto lg:mt-4 lg:mb-8 lg:h-[calc(100vh-10rem)] lg:gap-4 lg:overflow-hidden"
>
	<div class="flex h-full w-full flex-col lg:flex-row lg:gap-4">
		<div
			class="flex h-full w-full flex-col rounded-2xl bg-white p-3 lg:flex-row lg:gap-4 lg:rounded-none lg:bg-transparent lg:p-2"
		>
			<!-- Result + Info panel -->
			<div class="w-full lg:flex lg:h-full lg:w-[30%] lg:shrink-0 lg:flex-col lg:gap-4">
				<!-- Result -->
				<AuditResultScore
					auditSuccessRate={data.auditSuccessRate}
					plantEvaluationConfig={data.plantEvaluationConfig}
					auditEvaluationConfig={data.auditEvaluationConfig}
					answers={data.answers}
					evaluationMode={data.auditEvaluationConfig?.evaluationMode ?? 'percentage'}
				/>

				<!-- Info panel -->
				<div
					class="w-full px-3 pt-4 pb-3 lg:flex-1 lg:overflow-y-auto lg:rounded-2xl lg:bg-white lg:p-6"
				>
					<div class="" in:fly={{ duration: 300 }}>
						<AuditResultInfo audit={data.auditInfo} successRate={data.auditSuccessRate} />
					</div>
				</div>
			</div>

			<!-- Answers panel  -->
			<div class="flex-1 overflow-y-auto p-3 lg:overflow-hidden lg:rounded-2xl lg:bg-white lg:p-6">
				<div class="flex h-full flex-col lg:h-full">
					<AuditResultsAnswers
						questions={data.questions!}
						answers={data.answers!}
						plantEvaluationConfig={data.plantEvaluationConfig}
						auditInfo={data.auditInfo}
						onDownload={() => downloadAuditPdf(data.auditInfo!.id)}
					/>
				</div>
			</div>
		</div>
	</div>
</div>
