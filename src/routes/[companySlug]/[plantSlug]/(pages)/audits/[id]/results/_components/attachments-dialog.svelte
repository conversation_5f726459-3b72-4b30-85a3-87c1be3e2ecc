<script lang="ts">
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import { Download, Maximize, FileText, MessageSquareQuote } from '@lucide/svelte';
	import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';
	import { Badge } from '$lib/components/ui/badge';
	import { formatEvaluationValue } from '../_utils/evaluation-value';

	// Props
	let {
		open = $bindable(false),
		answers = [],
		plantEvaluationConfig,
		onOpenImagePreview,
		onDownloadFile,
		isImageFile
	} = $props<{
		open: boolean;
		answers: AuditAnswerResultsDTO[];
		plantEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
		onOpenImagePreview: (url: string, filename: string) => void;
		onDownloadFile: (url: string, filename: string) => void;
		isImageFile: (filename: string) => boolean;
	}>();

	const answersWithAttachments = $derived(() => {
		return answers.filter(
			(answer: AuditAnswerResultsDTO) => answer.files && answer.files.length > 0
		);
	});

	const totalAttachments = $derived(() => {
		return answersWithAttachments().reduce((total: number, answer: AuditAnswerResultsDTO) => {
			return total + (answer.files?.length || 0);
		}, 0);
	});
</script>

<CustomDialog
	{open}
	title={$t('audits.results.attachments.allAttachments')}
	subtitle={`${$t('audits.results.attachments.total')}: ${totalAttachments()}`}
	onClose={() => (open = false)}
	width="max-w-4xl"
>
	<div class="max-h-[60vh] flex-1 overflow-y-auto pr-2">
		{#if answersWithAttachments().length === 0}
			<div class="text-muted-foreground py-8 text-center">
				{$t('audits.results.attachments.noAttachments')}
			</div>
		{:else}
			<div class="space-y-6">
				{#each answersWithAttachments() as answer}
					<div class="rounded-lg border p-4">
						<div class="mb-3 flex items-start justify-between gap-4">
							<h3 class="line-clamp-2 font-medium">{answer.questionText}</h3>
							{#if answer.evaluationValue && answer.evaluationValue !== 'Visited'}
								<span
									class="inline-flex h-8 items-center justify-center gap-1 rounded-lg px-3 text-xs font-semibold text-white uppercase {answer?.note &&
									answer.note.trim()
										? 'min-w-14'
										: 'w-14'}"
									style="background-color: {formatEvaluationValue(
										answer.evaluationValue.toLowerCase(),
										answer.evaluationType,
										plantEvaluationConfig
									)}"
								>
									{#if answer?.note && answer.note.trim()}
										<div class="flex items-center gap-1">
											<MessageSquareQuote class="h-3 w-3" strokeWidth={3} />
										</div>
										<div class="mx-1 h-4 w-px bg-white/25"></div>
									{/if}

									<span class="whitespace-nowrap">
										{#if answer.evaluationType === 'points'}
											{answer.evaluationValue}
										{:else if answer.evaluationType === 'percentage'}
											{answer.evaluationValue}%
										{:else}
											{$t(
												`audits.evaluationTypes.evaluationValues.${answer.evaluationValue.toLowerCase()}`
											)}
										{/if}
									</span>
								</span>
							{/if}
						</div>

						{#if answer.note}
							<div class="mb-3 rounded-lg bg-[#EAEDF5] p-3 text-sm text-[#747C8A]">
								{answer.note}
							</div>
						{/if}

						<div class="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
							{#each answer.files || [] as file}
								<div
									class="group relative flex h-28 flex-col rounded-lg border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md"
								>
									<div class="flex h-full w-full items-center justify-center p-2">
										{#if isImageFile(file.filename)}
											<button
												type="button"
												class="flex h-full w-full cursor-pointer items-center justify-center border-0 bg-transparent p-0"
												onclick={() => {
													open = false;
													setTimeout(() => onOpenImagePreview(file.url, file.filename), 100);
												}}
											>
												<img
													src={file.url}
													alt={file.filename}
													class="max-h-full max-w-full object-contain"
												/>
											</button>
										{:else}
											<div class="flex items-center justify-center">
												<FileText class="h-10 w-10 text-gray-400" />
											</div>
										{/if}
									</div>

									<div
										class="absolute right-0 bottom-0 left-0 truncate bg-black/70 px-2 py-1 text-[10px] text-white"
									>
										{file.filename}
									</div>

									<div
										class="absolute inset-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100"
									>
										{#if isImageFile(file.filename)}
											<Button
												variant="ghost"
												size="icon"
												class="h-8 w-8 rounded-full bg-white/20 p-1.5 text-white hover:bg-white/30"
												onclick={() => {
													open = false;
													setTimeout(() => onOpenImagePreview(file.url, file.filename), 100);
												}}
											>
												<Maximize class="h-4 w-4" />
											</Button>
										{/if}
										<Button
											variant="ghost"
											size="icon"
											class="h-8 w-8 rounded-full bg-white/20 p-1.5 text-white hover:bg-white/30"
											onclick={() => onDownloadFile(file.url, file.filename)}
										>
											<Download class="h-4 w-4" />
										</Button>
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<div class="mt-4 flex justify-end">
		<Button
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={() => (open = false)}
		>
			{$t('common.buttons.close')}
		</Button>
	</div>
</CustomDialog>
