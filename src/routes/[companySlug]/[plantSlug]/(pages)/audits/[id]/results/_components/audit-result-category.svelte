<script lang="ts">
	import * as Accordion from '$lib/components/ui/accordion';
	import { t } from '$lib/translations';
	import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';
	import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
	import AuditResultQuestionItem from './audit-result-question-item.svelte';

	// Props
	let {
		categoryId,
		questions,
		answersMap,
		plantEvaluationConfig,
		isExpanded,
		onToggleQuestionExpand,
		onOpenImagePreview,
		onDownloadFile,
		isImageFile
	} = $props<{
		categoryId: string;
		questions: TemplateQuestions;
		answersMap: Map<string, AuditAnswerResultsDTO>;
		plantEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
		isExpanded: (questionId: string) => boolean;
		onToggleQuestionExpand: (questionId: string) => void;
		onOpenImagePreview: (url: string, filename: string) => void;
		onDownloadFile: (url: string, filename: string) => void;
		isImageFile: (filename: string) => boolean;
	}>();

	function getCategoryName(categoryId: string): string {
		return questions[categoryId]?.name || categoryId;
	}
</script>

<Accordion.Item value={categoryId} class="my-2">
	<Accordion.Trigger class="group rounded-lg bg-[#EAEDF5] p-4 text-lg font-semibold">
		<div class="flex w-full items-center justify-start">
			<span>{getCategoryName(categoryId)}</span>
		</div>
	</Accordion.Trigger>

	<Accordion.Content>
		<div class="space-y-2 p-4">
			{#if !questions[categoryId]?.questions || questions[categoryId]?.questions.length === 0}
				<p class="text-muted-foreground flex items-center justify-center py-4 text-sm">
					{$t('auditTypes.notemplateQuestions')}
				</p>
			{:else}
				{#each questions[categoryId]?.questions || [] as question (question.questionId)}
					{#if answersMap.has(question.questionId)}
						{@const answer = answersMap.get(question.questionId)}
						<AuditResultQuestionItem
							{answer}
							{question}
							{plantEvaluationConfig}
							isExpanded={isExpanded(question.questionId)}
							onToggleExpand={onToggleQuestionExpand}
							{onOpenImagePreview}
							{onDownloadFile}
							{isImageFile}
						/>
					{/if}
				{/each}
			{/if}
		</div>
	</Accordion.Content>
</Accordion.Item>
