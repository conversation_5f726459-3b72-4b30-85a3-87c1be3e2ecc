<script lang="ts">
	import { slide } from 'svelte/transition';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import { Maximize, Paperclip, FileText, Download, MessageSquareQuote } from '@lucide/svelte';
	import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';
	import { formatEvaluationValue } from '../_utils/evaluation-value';

	// Props
	let {
		answer,
		question,
		plantEvaluationConfig,
		isExpanded = false,
		onToggleExpand,
		onOpenImagePreview,
		onDownloadFile,
		isImageFile
	} = $props<{
		answer: AuditAnswerResultsDTO;
		question: { questionId: string; required: boolean };
		plantEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
		isExpanded: boolean;
		onToggleExpand: (questionId: string) => void;
		onOpenImagePreview: (url: string, filename: string) => void;
		onDownloadFile: (url: string, filename: string) => void;
		isImageFile: (filename: string) => boolean;
	}>();
</script>

<div
	class="group cursor-pointer rounded-lg border border-transparent px-4 py-2 hover:border-dashed hover:border-gray-300
  {isExpanded ? 'border-solid border-gray-200 bg-white shadow-[0_0_10px_rgba(0,0,0,0.2)]' : ''}"
	onclick={() => onToggleExpand(question.questionId)}
	onkeydown={(e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			onToggleExpand(question.questionId);
		}
	}}
	role="button"
	tabindex="0"
	aria-expanded={isExpanded}
>
	<div class=" flex flex-col">
		<div class="relative flex min-h-[40px] items-center text-sm text-[#747C8A]">
			<div class="flex-1 overflow-hidden pr-16" style="max-width: 70%;">
				<h4
					class="{isExpanded ? '' : 'line-clamp-2'} 
          leading-tight font-medium"
				>
					{answer?.questionText || $t('auditTypes.newQuestion.noQuestionFound')}
				</h4>
			</div>

			<div class="absolute top-0 right-0 flex h-8 items-center gap-2">
				{#if !question.required}
					<span class="text-muted-foreground text-xs whitespace-nowrap italic">
						({$t('auditTypes.newQuestion.optional')})
					</span>
				{/if}

				{#if answer?.evaluationValue && answer.evaluationValue !== 'Visited'}
					<div class="shrink-0">
						<span
							class="inline-flex min-h-8 items-center justify-center gap-1 rounded-lg px-3 text-xs font-semibold text-white uppercase {(answer?.files &&
								answer.files.length > 0) ||
							(answer?.note && answer.note.trim())
								? 'min-w-22'
								: 'w-22'}"
							style="background-color: {formatEvaluationValue(
								answer.evaluationValue.toLowerCase(),
								answer.evaluationType,
								plantEvaluationConfig
							)}"
						>
							{#if (answer?.files && answer.files.length > 0) || (answer?.note && answer.note.trim())}
								<div class="flex items-center gap-1">
									{#if answer?.files && answer.files.length > 0}
										<Paperclip class="h-3 w-3" strokeWidth={3} />
									{/if}
									{#if answer?.note && answer.note.trim()}
										<MessageSquareQuote class="h-3 w-3" strokeWidth={3} />
									{/if}
								</div>
								<div class="mx-1 h-4 w-px bg-white/25"></div>
							{/if}

							{#if answer.evaluationType === 'meetsreservations'}
								<span
									class="block max-w-[80px] text-center text-[11px] leading-none break-words whitespace-pre-line"
								>
									{$t(
										`audits.evaluationTypes.evaluationValues.${answer.evaluationValue.toLowerCase()}`
									)}
								</span>
							{:else}
								<span class="whitespace-nowrap">
									{#if answer.evaluationType === 'points'}
										{answer.evaluationValue}
									{:else if answer.evaluationType === 'percentage'}
										{answer.evaluationValue}%
									{:else}
										{$t(
											`audits.evaluationTypes.evaluationValues.${answer.evaluationValue.toLowerCase()}`
										)}
									{/if}
								</span>
							{/if}
						</span>
					</div>
				{/if}
			</div>
		</div>

		{#if isExpanded}
			<div transition:slide={{ duration: 400 }} class="mt-2 overflow-hidden">
				{#if answer?.note}
					<div class="">
						<div class="font-titillium rounded-lg bg-[#EAEDF5] p-3 text-sm text-[#747C8A]">
							{answer.note}
						</div>
					</div>
				{/if}

				{#if answer?.files && answer.files.length > 0}
					<div class="mt-3">
						<div class="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
							{#each answer.files as file}
								<div
									class="group/file relative flex h-24 flex-col rounded-lg border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md"
								>
									<div class="flex h-full w-full items-center justify-center p-2">
										{#if isImageFile(file.filename)}
											<button
												type="button"
												class="flex h-full w-full cursor-pointer items-center justify-center border-0 bg-transparent p-0"
												onclick={(e) => {
													e.stopPropagation();
													onOpenImagePreview(file.url, file.filename);
												}}
												onkeydown={(e) => {
													if (e.key === 'Enter' || e.key === ' ') {
														e.stopPropagation();
														onOpenImagePreview(file.url, file.filename);
													}
												}}
												aria-label="Show image preview"
											>
												<img
													src={file.url}
													alt={file.filename}
													class="max-h-full max-w-full object-contain"
												/>
											</button>
										{:else}
											<div class="flex items-center justify-center">
												<FileText class="h-8 w-8 text-gray-400" />
											</div>
										{/if}
									</div>
									<div
										class="absolute inset-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 transition-opacity group-hover/file:opacity-100"
									>
										{#if isImageFile(file.filename)}
											<Button
												variant="ghost"
												size="icon"
												class="h-8 w-8 rounded-full bg-white/20 p-1.5 text-white hover:bg-white/30"
												onclick={(e) => {
													e.stopPropagation();
													onOpenImagePreview(file.url, file.filename);
												}}
											>
												<Maximize class="h-4 w-4" />
											</Button>
										{/if}
										<Button
											variant="ghost"
											size="icon"
											class="h-8 w-8 rounded-full bg-white/20 p-1.5 text-white hover:bg-white/30"
											onclick={(e) => {
												e.stopPropagation();
												onDownloadFile(file.url, file.filename);
											}}
										>
											<Download class="h-4 w-4" />
										</Button>
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>
