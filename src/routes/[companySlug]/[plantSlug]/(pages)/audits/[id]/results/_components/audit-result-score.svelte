<script lang="ts">
	import SuccessRateIcon from '$lib/assets/successRateIcon.png';
	import Button from '$lib/components/ui/button/button.svelte';
	import { t } from '$lib/translations';
	import { Info, Undo2 } from '@lucide/svelte';
	import { getAuditResultColorCombined } from '$lib/utils/auditScore';
	let { auditSuccessRate, plantEvaluationConfig, auditEvaluationConfig, answers, evaluationMode } =
		$props();

	let isCardTurned = $state(false);

	let backgroundGradient = $derived.by(() => {
		const successRate = auditSuccessRate ?? 0;

		const color = getAuditResultColorCombined(
			successRate,
			answers || [],
			plantEvaluationConfig,
			auditEvaluationConfig
		);

		switch (color) {
			case 'success':
				return 'bg-gradient-to-br from-[#90DAB4] to-[#82D0A8]';
			case 'average':
				return 'bg-gradient-to-br from-[#EFDF66] to-[#E7D65A]';
			case 'bad':
				return 'bg-gradient-to-br from-[#D18385] to-[#C77B7F]';
			default:
				return 'bg-gradient-to-br from-[#B1B7C3] to-[#A5A9B3]';
		}
	});
</script>

<div
	class="relative mx-auto min-h-[100px] w-full overflow-visible [perspective:1000px] lg:min-h-[160px]"
>
	<div
		class="relative h-full min-h-[100px] w-full rounded-2xl transition-transform duration-500 [transform-style:preserve-3d] lg:min-h-[160px]"
		style="transform: rotateY({isCardTurned ? 180 : 0}deg);"
	>
		<div
			class="absolute inset-0 h-full w-full rounded-2xl {backgroundGradient} overflow-hidden px-2 py-2 [backface-visibility:hidden] lg:px-4 lg:py-2"
		>
			<div
				class="absolute inset-0 rounded-2xl"
				style="background: linear-gradient(60deg, transparent 50%, rgba(0,0,0,0.05) 50%);"
			></div>
			<Button
				variant="ghost"
				class="absolute top-2 left-2 z-10 size-6 cursor-pointer hover:text-white/70 lg:top-3 lg:left-3"
				onclick={() => (isCardTurned = true)}
			>
				<Info strokeWidth={2} class="text-white" />
			</Button>
			<div
				class="relative flex h-full min-h-[60px] w-full flex-col items-center justify-center gap-1 lg:gap-2"
			>
				<div class="relative inline-block">
					<img
						src={SuccessRateIcon}
						alt="Success Rate Icon"
						class="h-auto w-8 max-w-full lg:w-14"
					/>
					<div class="absolute inset-0 flex items-center justify-center">
						<span class="mb-1 text-sm font-bold text-white lg:text-[18px]">{auditSuccessRate}</span>
					</div>
				</div>
				<span class="text-center text-xs font-semibold text-white uppercase lg:text-base"
					>{$t('audits.results.totalScore')}</span
				>
			</div>
		</div>

		<div
			class="absolute inset-0 flex h-full w-full [transform:rotateY(180deg)] flex-col items-center justify-center gap-1 overflow-hidden rounded-2xl bg-white p-2 text-black [backface-visibility:hidden] lg:gap-2 lg:p-4"
		>
			<Button
				variant="ghost"
				class="absolute top-2 left-2 z-10 size-6 cursor-pointer hover:text-white/70 lg:top-3 lg:left-3"
				onclick={() => (isCardTurned = false)}
			>
				<Undo2 strokeWidth={2} class="text-black" />
			</Button>
			<span class="text-base font-bold">{$t('audits.results.scoreBackPage.evaluationMode')}</span>
			<span class="text-center opacity-80">{$t('questions.evaluationModes.' + evaluationMode)}</span
			>
		</div>
	</div>
</div>
