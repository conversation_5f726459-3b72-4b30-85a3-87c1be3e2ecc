<script lang="ts">
	import Button from '$lib/components/ui/button/button.svelte';
	import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';
	import { t } from '$lib/translations';
	import { Download, FoldVertical, Paperclip, ShieldAlert, UnfoldVertical } from '@lucide/svelte';

	let {
		onAttachments,
		onDownload,
		onCollapse,
		onExpand,
		onNegativeCheckpoints,
		negativeCheckpoints
	}: {
		onAttachments: () => void;
		onDownload: () => void;
		onCollapse: () => void;
		onExpand: () => void;
		onNegativeCheckpoints: () => void;
		negativeCheckpoints: AuditAnswerResultsDTO[];
	} = $props();
</script>

<Button
	class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
	onclick={onNegativeCheckpoints}
>
	<ShieldAlert />
	<span class="sr-only">{$t('audits.auditEvaluation.actions.collapseAll')}</span>
</Button>
<Button
	class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
	onclick={onAttachments}
>
	<Paperclip />
	<span class="sr-only">{$t('audits.auditEvaluation.actions.collapseAll')}</span>
</Button>
<Button
	class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
	onclick={onDownload}
>
	<Download />
	<span class="sr-only">{$t('audits.auditEvaluation.actions.collapseAll')}</span>
</Button>
<Button
	class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
	onclick={onCollapse}
>
	<FoldVertical />
	<span class="sr-only">{$t('audits.auditEvaluation.actions.collapseAll')}</span>
</Button>
<Button
	class="bg-secondary hover:bg-secondary/80 size-12 rounded-xl text-white [&_svg]:size-6!"
	onclick={onExpand}
>
	<UnfoldVertical />
	<span class="sr-only">{$t('audits.auditEvaluation.actions.expandAll')}</span>
</Button>
