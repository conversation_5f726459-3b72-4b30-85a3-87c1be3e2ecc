<script lang="ts">
	import * as Accordion from '$lib/components/ui/accordion';
	import { t } from '$lib/translations';
	import { type TemplateQuestions } from '$lib/schemas/audits/auditQuestions';
	import type { AuditAnswerResultsDTO } from '$lib/DTO/audits/auditResults';

	import AuditResultsAnswersActions from './audit-results-answers-actions.svelte';
	import AuditResultCategory from './audit-result-category.svelte';
	import AttachmentsDialog from './attachments-dialog.svelte';
	import ImagePreviewDialog from '$lib/customComponents/image-preview-dialog.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import { formatEvaluationValue } from '../_utils/evaluation-value';
	import { slide } from 'svelte/transition';
	import { Button } from '$lib/components/ui/button';
	import { Maximize, Paperclip, FileText, Download, MessageSquareQuote } from '@lucide/svelte';
	import NegativeCheckpointsDialog from './negative-checkpoints-dialog.svelte';

	// Props
	let {
		questions,
		categories = Object.keys(questions),
		answers,
		plantEvaluationConfig,
		auditInfo,
		onDownload
	} = $props<{
		questions: TemplateQuestions;
		answers: AuditAnswerResultsDTO[];
		categories?: string[];
		plantEvaluationConfig?: {
			auditThreshold_average?: number | null;
			auditThreshold_success?: number | null;
			pointsRangeMin?: number | null;
			pointsRangeMax?: number | null;
		} | null;
		auditInfo?: any;
		onDownload: () => void;
	}>();

	// State
	let imageDialogOpen = $state(false);
	let attachmentsDialogOpen = $state(false);
	let negativeCheckpointsDialogOpen = $state(false);
	let expandedCategories = $state<string[]>(categories[0] ? [categories[0]] : []);
	let expandedQuestionId = $state<string | null>(null);
	let imagePreviewUrl = $state<string | null>(null);
	let imagePreviewFilename = $state<string | null>(null);
	let currentImageIndex = $state<number>(0);
	let currentImages = $state<Array<{ url: string; filename: string }>>([]);

	let negativeCheckpoints = $derived(() =>
		answers.filter((answer: AuditAnswerResultsDTO) => {
			if (
				answer.evaluationValue === 'doesntmeet' ||
				answer.evaluationValue === 'nok' ||
				answer.evaluationValue === 'no' ||
				answer.evaluationValue === 'yes_inverse'
			) {
				return true;
			}

			if (
				answer.evaluationType === 'points' &&
				plantEvaluationConfig?.pointsRangeMin != null &&
				plantEvaluationConfig?.pointsRangeMax != null &&
				plantEvaluationConfig?.auditThreshold_average != null
			) {
				const value = Number(answer.evaluationValue);
				const min = plantEvaluationConfig.pointsRangeMin;
				const max = plantEvaluationConfig.pointsRangeMax;
				const avg = plantEvaluationConfig.auditThreshold_average;
				if (!isNaN(value) && max > min) {
					const percent = ((value - min) / (max - min)) * 100;
					if (percent < avg) {
						return true;
					}
				}
			}

			if (
				answer.evaluationType === 'percentage' &&
				plantEvaluationConfig?.auditThreshold_average != null
			) {
				const value = Number(answer.evaluationValue);
				if (!isNaN(value) && value < plantEvaluationConfig.auditThreshold_average) {
					return true;
				}
			}

			return false;
		})
	);

	const answersMap = $derived(() => {
		const map = new Map<string, AuditAnswerResultsDTO>();
		answers.forEach((answer: AuditAnswerResultsDTO) => {
			map.set(answer.questionId, answer);
		});
		return map;
	});

	// Helpers
	function toggleQuestionExpand(questionId: string) {
		if (expandedQuestionId === questionId) {
			expandedQuestionId = null;
		} else {
			expandedQuestionId = questionId;
		}
	}

	function collapseAll() {
		expandedCategories = [];
	}

	function expandAll() {
		expandedCategories = categories;
	}

	function isImageFile(filename: string): boolean {
		const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
		const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
		return imageExtensions.includes(ext);
	}

	function openImagePreview(url: string, filename: string) {
		if (isImageFile(filename)) {
			const allImages = answers
				.filter((answer: AuditAnswerResultsDTO) => answer.files && answer.files.length > 0)
				.flatMap((answer: AuditAnswerResultsDTO) =>
					answer.files
						.filter((file: { filename: string }) => isImageFile(file.filename))
						.map((file: { url: string; filename: string }) => ({
							url: file.url,
							filename: file.filename
						}))
				);

			currentImages = allImages;
			currentImageIndex = allImages.findIndex((img: { url: string }) => img.url === url);
			imagePreviewUrl = url;
			imagePreviewFilename = filename;
			imageDialogOpen = true;
		}
	}

	function handleNextImage() {
		if (currentImageIndex < currentImages.length - 1) {
			currentImageIndex++;
			const nextImage = currentImages[currentImageIndex];
			imagePreviewUrl = nextImage.url;
			imagePreviewFilename = nextImage.filename;
		}
	}

	function handlePreviousImage() {
		if (currentImageIndex > 0) {
			currentImageIndex--;
			const prevImage = currentImages[currentImageIndex];
			imagePreviewUrl = prevImage.url;
			imagePreviewFilename = prevImage.filename;
		}
	}

	async function downloadFile(url: string, filename: string) {
		try {
			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to download file');

			// Create blob from response
			const blob = await response.blob();

			// Create object URL and download
			const objectUrl = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = objectUrl;
			a.download = filename;
			document.body.appendChild(a);
			a.click();

			// Clean up
			window.URL.revokeObjectURL(objectUrl);
			document.body.removeChild(a);
		} catch (error) {
			console.error('Error downloading file:', error);
			const a = document.createElement('a');
			a.href = url;
			a.target = '_blank';
			a.download = filename;
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
		}
	}

	function isExpanded(questionId: string): boolean {
		return expandedQuestionId === questionId;
	}

	function openAttachmentsDialog() {
		attachmentsDialogOpen = true;
	}
</script>

<div class="flex h-full flex-col">
	<div class="mb-3 hidden shrink-0 lg:flex lg:flex-row lg:justify-end lg:gap-2">
		<AuditResultsAnswersActions
			onAttachments={openAttachmentsDialog}
			{onDownload}
			onCollapse={collapseAll}
			onExpand={expandAll}
			onNegativeCheckpoints={() => {
				negativeCheckpointsDialogOpen = true;
			}}
			negativeCheckpoints={negativeCheckpoints()}
		/>
	</div>

	<div class="flex-1 space-y-4 overflow-y-auto lg:overflow-y-auto">
		<Accordion.Root type="multiple" bind:value={expandedCategories}>
			{#each categories as categoryId (categoryId)}
				<AuditResultCategory
					{categoryId}
					{questions}
					answersMap={answersMap()}
					{plantEvaluationConfig}
					{isExpanded}
					onToggleQuestionExpand={toggleQuestionExpand}
					onOpenImagePreview={openImagePreview}
					onDownloadFile={downloadFile}
					{isImageFile}
				/>
			{/each}
		</Accordion.Root>
	</div>
</div>

<!-- Image Preview Dialog -->
{#if imagePreviewUrl && imagePreviewFilename}
	<ImagePreviewDialog
		bind:open={imageDialogOpen}
		imageUrl={imagePreviewUrl}
		imageName={imagePreviewFilename}
		onNext={handleNextImage}
		onPrevious={handlePreviousImage}
		hasNext={currentImageIndex < currentImages.length - 1}
		hasPrevious={currentImageIndex > 0}
		onDownload={downloadFile}
	/>
{/if}

<!-- Attachments Dialog -->
<AttachmentsDialog
	bind:open={attachmentsDialogOpen}
	{answers}
	{plantEvaluationConfig}
	onOpenImagePreview={openImagePreview}
	onDownloadFile={downloadFile}
	{isImageFile}
/>

<!-- Negative Checkpoints  -->
<NegativeCheckpointsDialog
	bind:open={negativeCheckpointsDialogOpen}
	onClose={() => {
		negativeCheckpointsDialogOpen = false;
	}}
	negativeCheckpoints={negativeCheckpoints()}
	onDownloadFile={downloadFile}
	onOpenImagePreview={openImagePreview}
	{isImageFile}
	{plantEvaluationConfig}
	{auditInfo}
/>
