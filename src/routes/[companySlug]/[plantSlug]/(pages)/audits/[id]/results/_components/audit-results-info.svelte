<script lang="ts">
	import { page } from '$app/state';
	import { locale, t } from '$lib/translations';
	import { formatDate } from '$lib/utils/date';
	import { formatName } from '$lib/utils/format-name';
	import { formatMinutesDuration, formatSecondsDuration } from '$lib/utils/time';
	import {
		CalendarCheck,
		CalendarFold,
		FileCheck,
		Hash,
		Hourglass,
		LandPlot,
		ShieldCheck,
		Timer,
		User,
		UserRoundCog,
		Users
	} from '@lucide/svelte';
	import type { Component } from 'svelte';
	import { getProgressInfo } from '$lib/utils/progress';
	import ProgressBadge from '$lib/customComponents/tableFormat/progress-badge.svelte';

	let { audit, successRate } = $props();
</script>

<div class="grid grid-cols-2 gap-3 text-base lg:grid-cols-1 lg:gap-4">
	<!-- TODO: Success rate semaphore Success Rate : {successRate} -->

	{@render auditResultsInfo($t('audits.results.info.code'), audit.code || '-', Hash)}

	{#if audit.workplace}
		{@render auditResultsInfo(
			$t('audits.results.info.workplace'),
			audit.workplace.name || '-',
			LandPlot
		)}

		{#if audit.workplace.responsiblePerson}
			{@render auditResultsInfo(
				$t('audits.results.info.workplaceResponsiblePerson'),
				formatName(
					audit.workplace.responsiblePerson.firstName,
					audit.workplace.responsiblePerson.lastName
				),
				Users
			)}
		{/if}
	{/if}

	{#if audit.auditType}
		{@render auditResultsInfo(
			$t('audits.results.info.auditType'),
			audit.auditType.name || '-',
			ShieldCheck
		)}

		{#if audit.auditType.responsiblePerson}
			{@render auditResultsInfo(
				$t('audits.results.info.auditTypeResponsiblePerson'),
				formatName(
					audit.auditType.responsiblePerson.firstName,
					audit.auditType.responsiblePerson.lastName
				),
				UserRoundCog
			)}
		{/if}
	{/if}

	{@render auditResultsInfo(
		$t('audits.results.info.auditor'),
		formatName(audit.auditor.firstName, audit.auditor.lastName),
		User
	)}

	{@render auditResultsInfo(
		$t('audits.results.info.plannedDate'),
		audit.plannedDate ? formatDate(audit.plannedDate, $locale) : '-',
		CalendarFold
	)}

	{@render auditResultsInfo(
		$t('audits.results.info.completionDate'),
		audit.completionDate ? formatDate(audit.completionDate, $locale) : '-',
		CalendarCheck
	)}

	{@render auditResultsInfo(
		$t('audits.results.info.expectedDuration'),
		audit.expectedDuration ? formatMinutesDuration(audit.expectedDuration, $locale) : '-',
		Timer
	)}

	{@render auditResultsInfo(
		$t('audits.results.info.realDuration'),
		audit.realDuration ? formatSecondsDuration(audit.realDuration, $locale) : '-',
		Hourglass
	)}
</div>

{#snippet auditResultsInfo(
	name: string,
	content: string,
	Icon: Component,
	additionalContentClass: string = ''
)}
	<div class="flex flex-row items-start gap-2 text-left lg:items-center lg:gap-4">
		<Icon class="mt-0.5 size-5 shrink-0 text-[#EAEDF5] lg:mt-0 lg:size-7" />
		<div class="flex min-w-0 flex-1 flex-col text-left">
			<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
			<span
				class={`-mt-0.5 text-sm leading-tight break-words text-[#4B505A] lg:text-base ${additionalContentClass}`}
				>{content}</span
			>
		</div>
	</div>
{/snippet}
