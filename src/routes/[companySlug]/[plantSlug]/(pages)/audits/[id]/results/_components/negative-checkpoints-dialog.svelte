<script lang="ts">
	import { t } from '$lib/translations';
	import {
		Download,
		FileText,
		Maximize,
		MessageSquareQuote,
		Paperclip,
		ShieldCheck
	} from '@lucide/svelte';
	import { formatEvaluationValue } from '../_utils/evaluation-value';
	import { slide } from 'svelte/transition';
	import Button from '$lib/components/ui/button/button.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { locale } from '$lib/translations';

	let {
		open = $bindable(),
		onClose,
		negativeCheckpoints,
		plantEvaluationConfig,
		onDownloadFile,
		onOpenImagePreview,
		isImageFile,
		auditInfo
	} = $props();

	let expandedNegativeQuestionId = $state<string | null>(null);
	let isSendingEmail = $state(false);

	function isNegativeExpanded(questionId: string): boolean {
		return expandedNegativeQuestionId === questionId;
	}

	function toggleNegativeQuestionExpand(questionId: string) {
		if (expandedNegativeQuestionId === questionId) {
			expandedNegativeQuestionId = null;
		} else {
			expandedNegativeQuestionId = questionId;
		}
	}
</script>

<CustomDialog
	{open}
	title={$t('audits.results.negativeCheckpoints')}
	onClose={() => {
		onClose();
	}}
	width={negativeCheckpoints.length === 0 ? 'max-w-xl' : 'max-w-4xl'}
>
	<div class="max-h-[60vh] space-y-2 overflow-y-auto pr-2">
		{#if negativeCheckpoints.length === 0}
			<div class="my-4 text-center text-sm text-gray-500">
				<ShieldCheck class="mx-auto mb-2 size-24 text-gray-400" />
				<span class="text-lg">{$t('audits.results.noNegativeCheckpoints')}</span>
			</div>
		{:else}
			{#each negativeCheckpoints as checkpoint}
				<div
					class="group m-2 cursor-pointer rounded-lg border border-transparent px-4 py-2 hover:border-dashed hover:border-gray-300
				{isNegativeExpanded(checkpoint.questionId)
						? 'border-solid border-gray-200 bg-white shadow-[0_0_10px_rgba(0,0,0,0.2)]'
						: ''}"
					onclick={() => toggleNegativeQuestionExpand(checkpoint.questionId)}
					onkeydown={(e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							toggleNegativeQuestionExpand(checkpoint.questionId);
						}
					}}
					role="button"
					tabindex="0"
					aria-expanded={isNegativeExpanded(checkpoint.questionId)}
				>
					<div class="flex flex-col">
						<div class="relative flex min-h-[40px] items-center text-sm text-[#747C8A]">
							<div class="flex-1 overflow-hidden pr-16" style="max-width: 70%;">
								<h4
									class="{isNegativeExpanded(checkpoint.questionId) ? '' : 'line-clamp-2'} 
								leading-tight font-medium"
								>
									{checkpoint.questionText || $t('auditTypes.newQuestion.noQuestionFound')}
								</h4>
							</div>

							<div class="absolute top-0 right-0 flex h-8 items-center gap-2">
								{#if checkpoint.evaluationValue && checkpoint.evaluationValue !== 'Visited'}
									<div class="shrink-0">
										<span
											class="inline-flex h-8 items-center justify-center gap-1 rounded-lg px-3 text-xs font-semibold text-white uppercase {(checkpoint?.files &&
												checkpoint.files.length > 0) ||
											(checkpoint?.note && checkpoint.note.trim())
												? 'min-w-18'
												: 'w-18'} {checkpoint.evaluationType === 'meetsreservations'
												? 'text-[11px] leading-none'
												: ''}"
											style="background-color: {formatEvaluationValue(
												checkpoint.evaluationValue.toLowerCase(),
												checkpoint.evaluationType,
												plantEvaluationConfig
											)}"
										>
											{#if (checkpoint?.files && checkpoint.files.length > 0) || (checkpoint?.note && checkpoint.note.trim())}
												<div class="flex items-center gap-1">
													{#if checkpoint?.files && checkpoint.files.length > 0}
														<Paperclip class="h-3 w-3" strokeWidth={3} />
													{/if}
													{#if checkpoint?.note && checkpoint.note.trim()}
														<MessageSquareQuote class="h-3 w-3" strokeWidth={3} />
													{/if}
												</div>
												<div class="mx-1 h-4 w-px bg-white/25"></div>
											{/if}

											<span class="whitespace-nowrap">
												{#if checkpoint.evaluationType === 'points'}
													{checkpoint.evaluationValue}
												{:else if checkpoint.evaluationType === 'percentage'}
													{checkpoint.evaluationValue}%
												{:else}
													{$t(
														`audits.evaluationTypes.evaluationValues.${checkpoint.evaluationValue.toLowerCase()}`
													)}
												{/if}
											</span>
										</span>
									</div>
								{/if}
							</div>
						</div>

						{#if isNegativeExpanded(checkpoint.questionId)}
							<div transition:slide={{ duration: 400 }} class="mt-2 overflow-hidden">
								{#if checkpoint?.note}
									<div class="">
										<div class="font-titillium rounded-lg bg-[#EAEDF5] p-3 text-sm text-[#747C8A]">
											{checkpoint.note}
										</div>
									</div>
								{/if}

								{#if checkpoint?.files && checkpoint.files.length > 0}
									<div class="mt-3">
										<div
											class="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
										>
											{#each checkpoint.files as file}
												<div
													class="group/file relative flex h-24 flex-col rounded-lg border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md"
												>
													<div class="flex h-full w-full items-center justify-center p-2">
														{#if isImageFile(file.filename)}
															<button
																type="button"
																class="flex h-full w-full cursor-pointer items-center justify-center border-0 bg-transparent p-0"
																onclick={(e) => {
																	e.stopPropagation();
																	onOpenImagePreview(file.url, file.filename);
																}}
																onkeydown={(e) => {
																	if (e.key === 'Enter' || e.key === ' ') {
																		e.stopPropagation();
																		onOpenImagePreview(file.url, file.filename);
																	}
																}}
																aria-label="Show image preview"
															>
																<img
																	src={file.url}
																	alt={file.filename}
																	class="max-h-full max-w-full object-contain"
																/>
															</button>
														{:else}
															<div class="flex items-center justify-center">
																<FileText class="h-8 w-8 text-gray-400" />
															</div>
														{/if}
													</div>
													<div
														class="absolute inset-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 transition-opacity group-hover/file:opacity-100"
													>
														{#if isImageFile(file.filename)}
															<Button
																variant="ghost"
																size="icon"
																class="h-8 w-8 rounded-full bg-white/20 p-1.5 text-white hover:bg-white/30"
																onclick={(e) => {
																	e.stopPropagation();
																	onOpenImagePreview(file.url, file.filename);
																}}
															>
																<Maximize class="h-4 w-4" />
															</Button>
														{/if}
														<Button
															variant="ghost"
															size="icon"
															class="h-8 w-8 rounded-full bg-white/20 p-1.5 text-white hover:bg-white/30"
															onclick={(e) => {
																e.stopPropagation();
																onDownloadFile(file.url, file.filename);
															}}
														>
															<Download class="h-4 w-4" />
														</Button>
													</div>
												</div>
											{/each}
										</div>
									</div>
								{/if}
							</div>
						{/if}
					</div>
				</div>
			{/each}
			<div class="mt-6 flex justify-end gap-4">
				<Button
					type="button"
					variant="outline"
					class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
					onclick={() => {
						onClose();
					}}
				>
					{$t('common.buttons.cancel')}
				</Button>

				<Button
					href="https://demo.ekaizen.app/MYCOMP/PRG01/forms/lean-audit-improvement"
					target="_blank"
					class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				>
					{$t('common.buttons.sendToEkaizen')}
				</Button>

				<Button
					class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
					disabled={isSendingEmail || negativeCheckpoints.length === 0}
					onclick={() => {
						if (auditInfo?.workplace?.responsiblePerson!.email) {
							const form = document.getElementById('send-email-form') as HTMLFormElement;
							if (form) form.requestSubmit();
						}
					}}
				>
					{isSendingEmail
						? $t('common.buttons.sending')
						: $t('common.buttons.sendToResponsiblePerson')}
				</Button>
			</div>
		{/if}
	</div>

	{#if auditInfo.workplace.responsiblePerson!.email}
		<form
			id="send-email-form"
			method="POST"
			action="?/sendNegativeCheckpointsEmail"
			use:enhance={() => {
				isSendingEmail = true;
				return async ({ result }) => {
					isSendingEmail = false;
					if (result.type === 'success') {
						toast.success($t('emails.negativeCheckpoints.emailSent'));
						onClose();
					} else {
						toast.error($t('emails.negativeCheckpoints.emailFailed'));
					}
				};
			}}
			style="display: none;"
		>
			<input type="hidden" name="negativeCheckpoints" value={JSON.stringify(negativeCheckpoints)} />
			<input
				type="hidden"
				name="recipientEmail"
				value={auditInfo.workplace.responsiblePerson.email}
			/>
			<input
				type="hidden"
				name="recipientName"
				value="{auditInfo.workplace.responsiblePerson.firstName} {auditInfo.workplace
					.responsiblePerson.lastName}"
			/>
			<input type="hidden" name="locale" value={$locale} />
		</form>
	{/if}
</CustomDialog>
