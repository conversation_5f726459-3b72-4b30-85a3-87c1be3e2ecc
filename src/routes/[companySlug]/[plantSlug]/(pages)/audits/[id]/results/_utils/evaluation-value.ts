export function formatEvaluationValue(
	value: string | null,
	evaluationType?: string,
	auditInstanceEvaluationConfig?: {
		auditThreshold_average?: number | null;
		auditThreshold_success?: number | null;
		percentageThreshold_average?: number | null;
		percentageThreshold_success?: number | null;
		pointsAvgThreshold?: number | null;
		pointsSuccessThreshold?: number | null;
		pointsRangeMin?: number | null;
		pointsRangeMax?: number | null;
	} | null
): string {
	if (!value) {
		return '#B1B7C3';
	}

	// Use plant config thresholds or defaults - separate for percentage and points
	const percentageAvgThreshold = auditInstanceEvaluationConfig?.percentageThreshold_average ?? 50;
	const percentageSuccessThreshold =
		auditInstanceEvaluationConfig?.percentageThreshold_success ?? 80;
	const pointsMin = auditInstanceEvaluationConfig?.pointsRangeMin ?? 0;
	const pointsMax = auditInstanceEvaluationConfig?.pointsRangeMax ?? 10;

	if (evaluationType === 'percentage') {
		const percentValue = parseFloat(value);
		if (isNaN(percentValue)) {
			return '#B1B7C3';
		}

		if (percentValue >= percentageSuccessThreshold) {
			return '#90DAB4'; // green - success
		} else if (percentValue >= percentageAvgThreshold) {
			return '#EFDF66'; // yellow - average
		} else {
			return '#D18385'; // red - poor
		}
	}

	if (evaluationType === 'points') {
		const pointValue = parseFloat(value);
		if (isNaN(pointValue)) {
			return '#B1B7C3';
		}

		// Use direct point thresholds if available, otherwise fall back to percentage calculation
		const pointsAvgThreshold = auditInstanceEvaluationConfig?.pointsAvgThreshold;
		const pointsSuccessThreshold = auditInstanceEvaluationConfig?.pointsSuccessThreshold;

		if (
			pointsAvgThreshold !== null &&
			pointsAvgThreshold !== undefined &&
			pointsSuccessThreshold !== null &&
			pointsSuccessThreshold !== undefined
		) {
			// Direct point comparison using absolute point values
			if (pointValue >= pointsSuccessThreshold) {
				return '#90DAB4'; // green - success
			} else if (pointValue >= pointsAvgThreshold) {
				return '#EFDF66'; // yellow - average
			} else {
				return '#D18385'; // red - poor
			}
		} else {
			// Fall back to percentage calculation with percentage thresholds
			const range = pointsMax - pointsMin;
			const normalizedValue = pointValue - pointsMin;
			const percentage = range > 0 ? (normalizedValue / range) * 100 : 0;
			const avgThreshold = auditInstanceEvaluationConfig?.percentageThreshold_average ?? 50;
			const successThreshold = auditInstanceEvaluationConfig?.percentageThreshold_success ?? 80;

			if (percentage >= successThreshold) {
				return '#90DAB4'; // green - success
			} else if (percentage >= avgThreshold) {
				return '#EFDF66'; // yellow - average
			} else {
				return '#D18385'; // red - poor
			}
		}
	}

	// Handle existing ok/nok/yes/no/na/wreservations values
	const lowerValue = value.toLowerCase();
	switch (lowerValue) {
		case 'ok':
		case 'yes':
		case 'no_inverse':
		case 'meets':
			return '#90DAB4'; // Green
		case 'nok':
		case 'no':
		case 'yes_inverse':
		case 'doesntmeet':
			return '#D18385'; // Red
		case 'wreservations':
			return '#EFDF66'; // Yellow - for "meets with reservations"
		case 'na':
			return '#B1B7C3'; // Gray
		default:
			return '#B1B7C3'; // Default gray
	}
}
