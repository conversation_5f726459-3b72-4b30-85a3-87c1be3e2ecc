<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import { Progress } from '$lib/components/ui/progress';
	import { locale, t } from '$lib/translations';
	import { Calendar, User, MapPin, CheckCircle } from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { getProgressInfo } from '$lib/utils/progress';

	let { audit, plantEvaluationConfig } = $props();

	let progressInfo = $derived.by(() => {
		// Use same logic as progress-badge.svelte
		const isClosed = !!audit.completionDate;
		const isOverdue = !isClosed && audit.plannedDate && new Date(audit.plannedDate) < new Date();

		if (isClosed) {
			// Closed audit - show success rate with color based on thresholds
			const successRate = audit.successRate ?? 0;
			const avgThreshold = plantEvaluationConfig?.auditThreshold_average ?? 70;
			const successThreshold = plantEvaluationConfig?.auditThreshold_success ?? 90;

			const text = `${$t('audits.progress.closed')} | ${successRate}%`;
			let variant = 'default';

			if (successRate >= successThreshold) {
				variant = 'success-good';
			} else if (successRate >= avgThreshold) {
				variant = 'success-average';
			} else {
				variant = 'success-poor';
			}

			return { text, variant, value: 100, successRate };
		} else {
			const progress = Math.min(Math.round(audit.progress || 0), 100);
			let variant = 'default';
			let text = $t('audits.progress.inProgress');

			if (isOverdue) {
				variant = 'destructive';
				text = $t('audits.progress.late');
			}

			return { text, variant, value: isOverdue ? 0 : progress };
		}
	});

	function getVariantClass(variant: string) {
		switch (variant) {
			case 'destructive':
				return 'bg-[#B4A1B0] hover:bg-[#B4A1B0] text-white border-rose-100';
			case 'success-good':
				return 'bg-[#90DAB4] hover:bg-[#90DAB4] text-white border-emerald-200';
			case 'success-average':
				return 'bg-[#EFDF66] hover:bg-[#EFDF66] text-white border-yellow-200';
			case 'success-poor':
				return 'bg-[#D18385] hover:bg-[#D18385] text-white border-rose-200';
			case 'success':
				return 'bg-[#90DAB4] hover:bg-[#90DAB4] text-white border-emerald-200';
			case 'warning':
				return 'bg-[#A6B5D6] hover:bg-[#A6B5D6] text-white border-[#A6B5D6]';
			case 'filled':
				return 'bg-[#242E46] hover:bg-[#242E46] text-white';
			case 'default':
			default:
				return 'bg-[#697691] hover:bg-[#697691] text-white border-[#697691]';
		}
	}

	function getProgressIndicatorClass(variant: string) {
		switch (variant) {
			case 'destructive':
				return 'bg-[#B4A1B0]';
			case 'success-good':
				return 'bg-emerald-500/20';
			case 'success-average':
				return 'bg-yellow-500/20';
			case 'success-poor':
				return 'bg-red-500/20';
			case 'success':
				return 'bg-emerald-500/20';
			case 'warning':
				return 'bg-[#A6B5D6]';
			case 'filled':
				return 'bg-[#242E46]';
			case 'default':
			default:
				return 'bg-[#414E6B]';
		}
	}

	function formatDate(dateStr: string) {
		if (!dateStr) return '-';
		return new Date(dateStr).toLocaleDateString($locale);
	}

	function handleCardClick() {
		if (audit.completionDate) {
			goto(`/${page.params.companySlug}/${page.params.plantSlug}/audits/${audit.id}/results`);
		} else {
			goto(`/${page.params.companySlug}/${page.params.plantSlug}/audits/${audit.id}/evaluate`);
		}
	}
</script>

<div
	role="button"
	tabindex="0"
	onclick={handleCardClick}
	onkeydown={(e) => e.key === 'Enter' && handleCardClick()}
	class="group relative cursor-pointer overflow-hidden rounded-lg border bg-white p-4 shadow-sm transition-all hover:border-gray-300 hover:shadow-md"
>
	<!-- Header -->
	<div class="mb-3 flex items-start justify-between">
		<div>
			<h3 class="font-semibold text-gray-900 group-hover:text-blue-600">{audit.code}</h3>
			<p class="text-sm text-gray-600">{audit.auditType?.name || '-'}</p>
		</div>
		<Badge class={'uppercase ' + getVariantClass(progressInfo.variant)}>
			{progressInfo.text}
		</Badge>
	</div>

	<!-- Progress bar -->
	<div class="mb-4">
		<div class="mb-2 flex justify-between text-xs">
			<span class="text-gray-600">{$t('audits.auditList.progress')}</span>
			<span class="font-medium">{progressInfo.value}%</span>
		</div>
		<Progress
			value={progressInfo.value}
			max={100}
			indicatorClass={getProgressIndicatorClass(progressInfo.variant)}
			class="h-2 bg-gray-200"
		/>
	</div>

	<!-- Info -->
	<div class="grid grid-cols-2 gap-2 text-sm">
		<!-- Planned date | Completion Date -->
		<div class="flex items-center gap-2 text-gray-600">
			<Calendar class="h-4 w-4 flex-shrink-0" />
			<span
				class="truncate {!audit.completionDate &&
				audit.plannedDate &&
				new Date(audit.plannedDate) < new Date()
					? 'font-bold text-[#8B6683]'
					: ''}"
			>
				{formatDate(audit.plannedDate)}
			</span>
		</div>

		<div
			class="flex items-center gap-2 {audit.completionDate ? 'text-green-600' : 'text-gray-400'}"
		>
			<CheckCircle class="h-4 w-4 flex-shrink-0" />
			<span class="truncate">{audit.completionDate ? formatDate(audit.completionDate) : '-'}</span>
		</div>

		<!-- Auditor | Workplace -->
		<div class="flex items-center gap-2 text-gray-600">
			<User class="h-4 w-4 flex-shrink-0" />
			<span class="truncate"
				>{audit.auditor ? `${audit.auditor.firstName} ${audit.auditor.lastName}` : '-'}</span
			>
		</div>

		<div class="flex items-center gap-2 text-gray-600">
			<MapPin class="h-4 w-4 flex-shrink-0" />
			<span class="truncate">{audit.workplace?.name || '-'}</span>
		</div>
	</div>
</div>
