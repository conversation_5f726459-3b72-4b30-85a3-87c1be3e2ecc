<script lang="ts">
	import AuditCard from './audit-card.svelte';
	import { Button } from '$lib/components/ui/button';
	import { ChevronLeft, ChevronRight } from '@lucide/svelte';
	import { t } from '$lib/translations';
	import type { AuditListDTO } from '$lib/DTO/audits/audits';

	let { audits, itemsPerPage = 8, globalFilter = '', plantEvaluationConfig } = $props();

	let currentPage = $state(1);

	let searchFilteredAudits = $derived.by(() => {
		if (!globalFilter.trim()) {
			return audits;
		}

		const filter = globalFilter.toLowerCase().trim();
		return audits.filter((audit: AuditListDTO) => {
			if (audit.code?.toLowerCase().includes(filter)) return true;

			if (audit.auditType?.name?.toLowerCase().includes(filter)) return true;

			if (audit.workplace?.name?.toLowerCase().includes(filter)) return true;

			if (audit.auditor) {
				const fullName =
					`${audit.auditor.firstName || ''} ${audit.auditor.lastName || ''}`.toLowerCase();
				if (fullName.includes(filter)) return true;
			}

			return false;
		});
	});

	let totalPages = $derived(Math.ceil(searchFilteredAudits.length / itemsPerPage));
	let paginatedAudits = $derived(
		searchFilteredAudits.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
	);

	function nextPage() {
		if (currentPage < totalPages) {
			currentPage++;
		}
	}

	function prevPage() {
		if (currentPage > 1) {
			currentPage--;
		}
	}
	$effect(() => {
		if (currentPage > totalPages && totalPages > 0) {
			currentPage = 1;
		}
	});

	$effect(() => {
		globalFilter;
		currentPage = 1;
	});
</script>

<div class="flex h-full flex-col">
	<!-- Cards Grid -->
	<div class="flex-1 space-y-4 overflow-y-auto">
		{#each paginatedAudits as audit (audit.id)}
			<AuditCard {audit} {plantEvaluationConfig} />
		{:else}
			<div class="flex h-32 items-center justify-center text-gray-500">
				{$t('audits.auditList.noAudits')}
			</div>
		{/each}
	</div>

	<!-- Pagination -->
	{#if totalPages > 1}
		<div class="mt-4 flex items-center justify-between border-t pt-4">
			<p class="text-sm text-gray-600">
				{$t('common.pagination.showing')}
				{(currentPage - 1) * itemsPerPage + 1} - {Math.min(
					currentPage * itemsPerPage,
					searchFilteredAudits.length
				)}
				{$t('common.pagination.of')}
				{searchFilteredAudits.length}
			</p>

			<div class="flex items-center gap-2">
				<Button variant="outline" size="sm" onclick={prevPage} disabled={currentPage === 1}>
					<ChevronLeft class="h-4 w-4" />
				</Button>

				<span class="text-sm font-medium">
					{currentPage} / {totalPages}
				</span>

				<Button
					variant="outline"
					size="sm"
					onclick={nextPage}
					disabled={currentPage === totalPages}
				>
					<ChevronRight class="h-4 w-4" />
				</Button>
			</div>
		</div>
	{/if}
</div>
