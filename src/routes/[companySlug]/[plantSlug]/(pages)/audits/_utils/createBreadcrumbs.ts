import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createAuditBreadcrumbs(
	auditId?: string,
	auditCode?: string,
	additionalLevel?: string,
	companySlug?: string,
	plantSlug?: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		}
	];

	if (additionalLevel) {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'common.navigation.overview',
				href: `/${companySlug}/${plantSlug}/audits`
			},
			{
				kind: 'static',
				label: additionalLevel
			},
			{
				kind: 'dynamic',
				label: auditCode!
			}
		);
	} else if (auditId) {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'common.navigation.overview',
				href: `/${companySlug}/${plantSlug}/audits`
			},
			{
				kind: 'dynamic',
				label: auditCode!
			}
		);
	} else {
		breadcrumbs.push({
			kind: 'static',
			label: 'common.navigation.overview'
		});
	}

	return breadcrumbs;
}
