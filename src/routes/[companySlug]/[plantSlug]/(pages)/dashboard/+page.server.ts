import { createDashboardBreadcrumbs } from './_utils/createBreadcrumbs';
import { DashboardService } from '$lib/server/services/dashboard';

export const load = async ({ locals, parent }) => {
	if (!locals.user) {
		return {
			lastFinished: [],
			comingSoon: [],
			overdue: [],
			auditStats: { closed: 0, planned: 0, inProgress: 0, overdue: 0 },
			auditSuccessRate: 0,
			chartData: [],
			breadcrumbs: createDashboardBreadcrumbs()
		};
	}

	const { context } = await parent();

	const [audits, auditStats, auditSuccessRate, chartData] = await Promise.all([
		DashboardService.getDashboardAudits(locals.user.id, context.plantId),
		DashboardService.getAuditStats(context.plantId),
		DashboardService.calculateAuditSuccessRate(context.plantId),
		DashboardService.getAuditCompletionChartData(context.plantId)
	]);

	return {
		...audits,
		auditStats,
		auditSuccessRate,
		chartData,
		breadcrumbs: createDashboardBreadcrumbs()
	};
};
