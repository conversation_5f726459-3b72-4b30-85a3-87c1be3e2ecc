<script lang="ts">
	import type { PageData } from './$types';
	import { fade } from 'svelte/transition';
	import AuditSuccessRate from './_components/audit-success-rate.svelte';
	import AuditChart from './_components/audit-chart.svelte';
	import DashboardDataTable from './_components/dashboard-data-table.svelte';
	import DashboardOverview from './_components/dashboard-overview.svelte';
	import { t } from '$lib/translations';

	let { data }: { data: PageData } = $props();
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.dashboard')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl"
>
	<div
		class=" grid h-full min-h-0 w-full grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-12 lg:grid-rows-2"
		style="grid-template-rows: 1fr 1fr;"
	>
		<div class="lg:col-span-3">
			<AuditSuccessRate successRate={data.auditSuccessRate} />
		</div>

		<div class="lg:col-span-9">
			<DashboardDataTable {data} />
		</div>

		<div class="lg:col-span-3">
			<DashboardOverview auditStats={data.auditStats} />
		</div>

		<div class="lg:col-span-9">
			<AuditChart data={data.chartData} />
		</div>
	</div>
</div>
