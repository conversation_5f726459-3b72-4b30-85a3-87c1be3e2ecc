<script lang="ts">
	import { t } from '$lib/translations';
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Chart from '$lib/components/ui/chart/index.js';
	import { BarChart, type ChartContextValue, Highlight, Spline } from 'layerchart';
	import { scaleBand, scaleLinear } from 'd3-scale';
	import { cubicInOut } from 'svelte/easing';

	interface ChartDataPoint {
		week: string;
		planned: number;
		completed: number;
	}

	let { data } = $props<{ data: ChartDataPoint[] }>();
	const yDomain = [0, 30];
	const chartData = $derived(
		(data || []).filter((item: ChartDataPoint) => {
			const [year, month] = item.week.split('-');
			return (
				!isNaN(Number(year)) && !isNaN(Number(month)) && Number(month) >= 1 && Number(month) <= 12
			);
		})
	);
	let context = $state<ChartContextValue>();

	const chartConfig = {
		audits: { label: 'Audity' },
		planned: {
			label: $t('dashboard.widgets.chart.planned'),
			color: 'oklch(0.6328 0.02 250)'
		},
		closed: {
			label: $t('dashboard.widgets.chart.closed'),
			color: 'oklch(0.33 0.04 255, 0.5)'
		}
	} satisfies Chart.ChartConfig;

	function getLastNWeeksData(data: ChartDataPoint[], n: number) {
		if (!data.length) return [];

		// Get the current week's end date to include all of current week
		const now = new Date();
		const currentWeekEnd = new Date(now);
		const day = currentWeekEnd.getDay();
		const daysToAdd = day === 0 ? 0 : 7 - day; // Sunday is end of week
		currentWeekEnd.setDate(currentWeekEnd.getDate() + daysToAdd);
		currentWeekEnd.setHours(23, 59, 59, 999);

		const filteredData = data.filter((item) => {
			try {
				const itemDate = new Date(item.week);
				// Include current week by comparing with end of current week
				return itemDate <= currentWeekEnd;
			} catch {
				return false;
			}
		});

		return filteredData.slice(-n);
	}

	const weeksShown = 28;
	const filteredChartData = $derived(getLastNWeeksData(chartData, weeksShown));
</script>

<Card.Root class="col-span-8 row-span-1 flex h-full flex-col border-none shadow-none">
	<Card.Header>
		<Card.Title class="text-base font-semibold uppercase sm:text-lg"
			>{$t('dashboard.widgets.chart.title')}</Card.Title
		>
	</Card.Header>
	<Card.Content class="flex-1">
		{#if filteredChartData.length > 0}
			<div class="relative h-[25vh] w-full sm:h-[35vh]">
				<div class="absolute inset-0">
					<Chart.Container config={chartConfig} class="h-full w-full">
						<BarChart
							bind:context
							data={filteredChartData}
							xScale={scaleBand().padding(0.4)}
							yScale={scaleLinear()}
							x="week"
							y={['planned', 'completed']}
							{yDomain}
							axis={true}
							series={[
								{
									key: 'planned',
									label: chartConfig.planned.label,
									color: chartConfig.planned.color
								},
								{
									key: 'completed',
									label: chartConfig.closed.label,
									color: chartConfig.closed.color
								}
							]}
							props={{
								bars: {
									stroke: 'none',
									rounded: 'all',
									radius: 4,
									motion: {
										x: { type: 'tween', duration: 500, easing: cubicInOut },
										width: { type: 'tween', duration: 500, easing: cubicInOut },
										height: { type: 'tween', duration: 500, easing: cubicInOut },
										y: { type: 'tween', duration: 500, easing: cubicInOut }
									}
								},
								highlight: { area: { fill: 'none' } },
								xAxis: {
									format: (d) => {
										const index = filteredChartData.findIndex(
											(item: ChartDataPoint) => item.week === d
										);
										if (index === -1) return '';
										if (index === 0) {
											const [year, month] = filteredChartData[0].week.split('-');
											return `${parseInt(month)}/${year}`;
										}
										const [prevYear, prevMonth] = filteredChartData[index - 1].week.split('-');
										const [currYear, currMonth] = filteredChartData[index].week.split('-');
										if (currMonth !== prevMonth || currYear !== prevYear) {
											return `${parseInt(currMonth)}/${currYear}`;
										}
										return '';
									}
								}
							}}
						>
							{#snippet belowMarks()}
								<Highlight area={{ class: 'fill-muted' }} />
							{/snippet}
						</BarChart>
					</Chart.Container>
				</div>

				<div class="absolute inset-0">
					<Chart.Container config={chartConfig} class="h-full w-full">
						<BarChart
							data={filteredChartData}
							x="week"
							y={['completed', 'planned']}
							yNice={4}
							{yDomain}
							padding={{ left: 16, bottom: 16 }}
							axis={false}
							grid={false}
							series={[
								{
									key: 'completed',
									label: chartConfig.closed.label,
									color: chartConfig.closed.color
								},
								{
									key: 'planned',
									label: chartConfig.planned.label,
									color: chartConfig.planned.color
								}
							]}
							props={{
								xAxis: { ticks: 10, rule: true },
								highlight: { area: { fill: 'none' } }
							}}
						>
							{#snippet marks()}
								<Spline y="planned" class="stroke-secondary stroke-2" />
								<Spline y="completed" class="stroke-primary stroke-2" />
							{/snippet}
							{#snippet belowMarks()}
								<Highlight area={{ class: 'fill-muted-foreground/20' }} />
							{/snippet}
							{#snippet tooltip()}
								<Chart.Tooltip
									class="p-2 sm:p-4"
									labelClassName="font-bold font-titillium text-sm sm:text-base"
									labelFormatter={(d: string) => {
										let year = '';
										let week = '';
										if (d.includes('-')) {
											const parts = d.split('-');
											if (parts.length === 3) {
												year = parts[0];
												const date = new Date(d);
												const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
												const pastDaysOfYear =
													(date.valueOf() - firstDayOfYear.valueOf()) / 86400000;
												week = String(
													Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)
												);
											} else if (parts.length === 2) {
												year = parts[0];
												week = parts[1];
											}
										}
										return `${$t('dashboard.widgets.chart.week')} ${week} - ${year}`;
									}}
									hideIndicator
								>
									{#snippet formatter({ name, value })}
										<div class="flex flex-row justify-between gap-2 sm:gap-4">
											<div class="font-titillium text-sm sm:text-base">
												{chartConfig[name as keyof typeof chartConfig]?.label || name}
											</div>
											<div class="text-sm sm:text-base">
												{value}
											</div>
										</div>
									{/snippet}
								</Chart.Tooltip>
							{/snippet}
						</BarChart>
					</Chart.Container>
				</div>
			</div>
		{:else}
			<div
				class="text-muted-foreground flex h-[25vh] items-center justify-center text-sm sm:h-[35vh] sm:text-base"
			>
				{$t('dashboard.widgets.chart.noData')}
			</div>
		{/if}
	</Card.Content>
</Card.Root>
