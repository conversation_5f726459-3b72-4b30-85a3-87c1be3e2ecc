<script lang="ts">
	import * as Card from '$lib/components/ui/card/index';
	import * as Chart from '$lib/components/ui/chart/index';
	import { t } from '$lib/translations';
	import { ArcChart, Text } from 'layerchart';
	import { onMount } from 'svelte';
	import { getAuditSuccessColor } from '$lib/utils/audit-success-color';

	let { successRate }: { successRate: number | 0 } = $props();

	const chartData = [{ succession: 'audits', percentage: successRate }];
	const colorConfig = $derived(getAuditSuccessColor(successRate));

	const chartConfig = {
		audits: { label: 'Audits', color: 'success-gradient' }
	} satisfies Chart.ChartConfig;

	let windowWidth = $state(0);

	onMount(() => {
		windowWidth = window.innerWidth;
		const handleResize = () => {
			windowWidth = window.innerWidth;
		};
		window.addEventListener('resize', handleResize);
		return () => {
			window.removeEventListener('resize', handleResize);
		};
	});

	let responsiveOuterRadius = $derived(windowWidth < 640 ? 90 : 120);
	let responsiveInnerRadius = $derived(windowWidth < 640 ? 70 : 100);
	let responsiveTextClass = $derived(
		windowWidth < 640
			? 'fill-foreground font-titillium text-3xl! tabular-nums'
			: 'fill-foreground font-titillium text-4xl!  tabular-nums'
	);
</script>

<Card.Root class="col-span-4 row-span-1 flex h-full flex-col border-none shadow-none">
	<Card.Header class="mt-3 items-center text-xl font-semibold uppercase">
		<Card.Title>{$t('dashboard.widgets.successRate')}</Card.Title>
	</Card.Header>
	<Card.Content class="flex-1">
		<div class="flex h-full w-full items-center justify-center">
			<Chart.Container config={chartConfig} class="h-[200px] w-[200px] sm:h-[250px] sm:w-[250px]">
				<ArcChart
					label="succession"
					value="percentage"
					outerRadius={responsiveOuterRadius}
					innerRadius={responsiveInnerRadius}
					cornerRadius={48}
					range={[-120, 120]}
					maxValue={100}
					series={chartData.map((d) => ({
						key: d.succession,
						data: [d]
					}))}
					props={{
						arc: {
							fill: colorConfig.fill,
							track: { fill: colorConfig.track, stroke: 'var(--border)' },
							motion: 'spring'
						},
						tooltip: { context: { hideDelay: 350 } }
					}}
					tooltip={false}
				>
					{#snippet belowMarks()}
						<defs>
							<!-- Dynamic gradient based on success rate -->
							{#if successRate >= 85}
								<linearGradient id="successGradientGreen" x1="0%" y1="0%" x2="100%" y2="100%">
									<stop offset="0%" stop-color={colorConfig.stopColor1} />
									<stop offset="100%" stop-color={colorConfig.stopColor2} />
								</linearGradient>
							{:else if successRate >= 70}
								<linearGradient id="successGradientOrange" x1="0%" y1="0%" x2="100%" y2="100%">
									<stop offset="0%" stop-color={colorConfig.stopColor1} />
									<stop offset="100%" stop-color={colorConfig.stopColor2} />
								</linearGradient>
							{:else}
								<linearGradient id="successGradientRed" x1="0%" y1="0%" x2="100%" y2="100%">
									<stop offset="0%" stop-color={colorConfig.stopColor1} />
									<stop offset="100%" stop-color={colorConfig.stopColor2} />
								</linearGradient>
							{/if}
						</defs>
						<circle cx="0" cy="0" r="80" class="fill-transparent" />
					{/snippet}
					{#snippet aboveMarks()}
						<Text
							value={String(chartData[0].percentage) + '%'}
							textAnchor="middle"
							verticalAnchor="middle"
							class={responsiveTextClass}
							dy={3}
						/>
					{/snippet}
				</ArcChart>
			</Chart.Container>
		</div>
	</Card.Content>
</Card.Root>

<style>
	:global(.success-gradient) {
		fill: url(#successGradientGreen);
	}
</style>
