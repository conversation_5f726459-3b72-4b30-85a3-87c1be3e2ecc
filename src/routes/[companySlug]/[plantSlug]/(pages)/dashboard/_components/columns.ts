import { renderComponent } from '$lib/components/ui/data-table';
import type { AuditListDTO } from '$lib/DTO/audits/audits';
import type { ColumnDef } from '@tanstack/table-core';
import { t, locale } from '$lib/translations';
import { get } from 'svelte/store';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import WorkplaceLink from '../../audits/[id]/_components/table-format/workplace-cell.svelte';
import CodeCell from '$lib/customComponents/tableFormat/code-cell.svelte';
import AuditorCell from '$lib/customComponents/tableFormat/auditor-cell.svelte';
import { formatDate } from '$lib/utils/date';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import DataTableActions from './data-table-actions.svelte';

export function createColumnsComingSoonOverdue(
	plantSlug: string,
	companySlug: string
): ColumnDef<AuditListDTO>[] {
	return [
		{
			id: 'code',
			accessorKey: 'code',
			header: ({ column }) => renderComponent(CustomHeader, { text: '#', column }),
			cell: ({ row }) => renderComponent(CodeCell, { code: row.getValue('code') as string }),
			enableSorting: true
		},
		{
			id: 'auditType',
			accessorKey: 'auditType',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.auditType'),
					column
				}),
			cell: ({ row }) =>
				renderComponent(Cell, { content: (row.getValue('auditType') as { name: string }).name }),
			enableSorting: true
		},
		{
			id: 'auditor',
			accessorFn: (row: AuditListDTO) => {
				const auditor = row.auditor;
				if (!auditor) return '';
				return `${auditor.firstName || ''} ${auditor.lastName || ''}`.trim();
			},
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.auditor'),
					column
				}),
			cell: ({ row }) => {
				const auditor = row.original.auditor;
				if (!auditor) return null;

				return renderComponent(AuditorCell, {
					firstName: auditor.firstName,
					lastName: auditor.lastName
				});
			},
			enableSorting: true
		},
		{
			id: 'workplace',
			accessorFn: (row: AuditListDTO) => row.workplace?.name ?? '',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.workplace'),
					column
				}),
			cell: ({ row }) => {
				const workplace = row.original.workplace;
				if (!workplace) return null;

				return renderComponent(WorkplaceLink, {
					workplace: workplace,
					plantSlug: plantSlug,
					companySlug: companySlug
				});
			},
			enableSorting: true
		},
		{
			id: 'plannedDate',
			accessorKey: 'plannedDate',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.plannedDate'),
					column
				}),
			cell: ({ row }) => {
				const date = new Date(row.getValue('plannedDate'));
				return renderComponent(Cell, {
					content: formatDate(date.toISOString(), get(locale))
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',
			enableSorting: false,
			enableHiding: false,
			meta: {
				headerClass: 'text-right',
				cellClass: 'text-right'
			},
			cell: ({ row }) => {
				return renderComponent(DataTableActions, {
					id: row.original.id,
					code: row.original.code,
					progress: row.original.progress,
					row: row.original
				});
			}
		}
	];
}

export function createColumnsLastFinished(
	plantSlug: string,
	companySlug: string
): ColumnDef<AuditListDTO>[] {
	return [
		{
			id: 'code',
			accessorKey: 'code',
			header: ({ column }) => renderComponent(CustomHeader, { text: '#', column }),
			cell: ({ row }) => renderComponent(CodeCell, { code: row.getValue('code') as string }),
			enableSorting: true
		},
		{
			id: 'auditType',
			accessorKey: 'auditType',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.auditType'),
					column
				}),
			cell: ({ row }) =>
				renderComponent(Cell, { content: (row.getValue('auditType') as { name: string }).name }),
			enableSorting: true
		},
		{
			id: 'auditor',
			accessorFn: (row: AuditListDTO) => {
				const auditor = row.auditor;
				if (!auditor) return '';
				return `${auditor.firstName || ''} ${auditor.lastName || ''}`.trim();
			},
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.auditor'),
					column
				}),
			cell: ({ row }) => {
				const auditor = row.original.auditor;
				if (!auditor) return null;

				return renderComponent(AuditorCell, {
					firstName: auditor.firstName,
					lastName: auditor.lastName
				});
			},
			enableSorting: true
		},
		{
			id: 'workplace',
			accessorFn: (row: AuditListDTO) => row.workplace?.name ?? '',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.workplace'),
					column
				}),
			cell: ({ row }) => {
				const workplace = row.original.workplace;
				if (!workplace) return null;

				return renderComponent(WorkplaceLink, {
					workplace: workplace,
					plantSlug: plantSlug,
					companySlug: companySlug
				});
			},
			enableSorting: true
		},
		{
			id: 'completionDate',
			accessorKey: 'completionDate',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('audits.auditList.completionDate'),
					column
				}),
			cell: ({ row }) => {
				if (row.getValue('completionDate') === null) {
					return '';
				}
				const date = new Date(row.getValue('completionDate'));
				return renderComponent(Cell, {
					content: formatDate(date.toISOString(), get(locale))
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',
			enableSorting: false,
			enableHiding: false,
			meta: {
				headerClass: 'text-right',
				cellClass: 'text-right'
			},
			cell: ({ row }) => {
				return renderComponent(DataTableActions, {
					id: row.original.id,
					code: row.original.code,
					progress: row.original.progress,
					row: row.original
				});
			}
		}
	];
}

export const columnsComingSoonOverdue = createColumnsComingSoonOverdue('', '');
export const columnsLastFinished = createColumnsLastFinished('', '');
