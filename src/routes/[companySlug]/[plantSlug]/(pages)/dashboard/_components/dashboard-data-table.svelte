<script lang="ts">
	import * as Card from '$lib/components/ui/card/index';
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import { t } from '$lib/translations';
	import { createColumnsComingSoonOverdue, createColumnsLastFinished } from './columns';
	import { page } from '$app/state';

	let { data } = $props();

	let chosenTab: string = $state('lastFinished');

	// Get plantSlug from page params
	const plantSlug = page.params.plantSlug;
	const companySlug = page.params.companySlug;

	// Create columns with plantSlug
	const columnsLastFinished = $derived(() => createColumnsLastFinished(plantSlug, companySlug));
	const columnsComingSoonOverdue = $derived(() =>
		createColumnsComingSoonOverdue(plantSlug, companySlug)
	);
</script>

<Card.Root class="col-span-8 row-span-1 flex h-full flex-col border-none shadow-none ">
	<Card.Content class="flex flex-1 flex-col">
		<div class="flex flex-wrap items-center justify-between gap-2 pb-4 sm:gap-4">
			<p class="shrink-0 text-lg font-semibold uppercase sm:text-xl">
				{$t('dashboard.widgets.auditTable.title')}
			</p>
			<div class="flex overflow-x-auto">
				<button
					class={`cursor-pointer border-b-2 px-2 py-2 uppercase sm:px-4 ${chosenTab === 'lastFinished' ? '  border-[#414E6B] text-[#2E384F]' : 'text-[#747C8A] hover:text-[#2E384F]/80'}`}
					onclick={() => (chosenTab = 'lastFinished')}
				>
					{$t('dashboard.widgets.auditTable.lastFinished')}
				</button>
				<button
					class={`cursor-pointer border-b-2 px-2 py-2 uppercase    sm:px-4 ${chosenTab === 'comingSoon' ? '  border-[#414E6B] text-[#2E384F]' : 'text-[#747C8A] hover:text-[#2E384F]/80'}`}
					onclick={() => (chosenTab = 'comingSoon')}
				>
					{$t('dashboard.widgets.auditTable.comingSoon')}
				</button>
				<button
					class={`cursor-pointer border-b-2 px-2 py-2 uppercase   sm:px-4 ${chosenTab === 'overdue' ? '  border-[#414E6B] text-[#2E384F]' : 'text-[#747C8A] hover:text-[#2E384F]/80'}`}
					onclick={() => (chosenTab = 'overdue')}
				>
					{$t('dashboard.widgets.auditTable.overdue')}
				</button>
			</div>
		</div>
		<!-- DataTable -->
		<div class="mt-0 min-h-0 w-full flex-1 text-sm">
			{#key chosenTab}
				<DataTable
					data={chosenTab === 'lastFinished'
						? (data.lastFinished || []).slice(0, 4)
						: chosenTab === 'comingSoon'
							? (data.comingSoon || []).slice(0, 4)
							: (data.overdue || []).slice(0, 4)}
					columns={chosenTab === 'lastFinished'
						? columnsLastFinished()
						: columnsComingSoonOverdue()}
					globalFilter={''}
					pagination={false}
					rowSize="compact"
				/>
			{/key}
		</div>
	</Card.Content>
</Card.Root>
