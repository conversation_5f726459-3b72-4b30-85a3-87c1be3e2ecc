<script lang="ts">
	import { locale, t } from '$lib/translations';

	let { auditStats } = $props();

	const formatNumber = (num: number) => {
		return num.toLocaleString($locale === 'cs' ? 'cs-CZ' : 'en-US');
	};
</script>

<div
	class="col-span-4 row-span-1 flex h-full flex-col rounded-2xl bg-gradient-to-tr from-[#536D9A] to-[#101826] p-4 shadow-none sm:p-6"
>
	<p class="mb-4 text-lg font-semibold tracking-wide text-white uppercase sm:mb-6 sm:text-xl">
		{$t('dashboard.widgets.overview')}
	</p>

	<div class="flex h-full items-center justify-center">
		<div class="grid h-full w-full grid-cols-2 grid-rows-2">
			<div
				class="flex flex-col items-center justify-center border-r border-b border-white/20 p-2 sm:p-4"
			>
				<div class="mb-0.5 text-2xl text-white sm:mb-1 sm:text-3xl">
					{formatNumber(auditStats?.closed || 0)}
				</div>
				<div class="text-center text-xs tracking-wide text-[#DAE1ED] uppercase">
					{$t('dashboard.widgets.auditStats.closed')}
				</div>
			</div>

			<div class="flex flex-col items-center justify-center border-b border-white/20 p-2 sm:p-4">
				<div class="mb-0.5 text-2xl text-white sm:mb-1 sm:text-3xl">
					{formatNumber(auditStats?.planned || 0)}
				</div>
				<div class="text-center text-xs tracking-wide text-[#DAE1ED] uppercase">
					{$t('dashboard.widgets.auditStats.planned')}
				</div>
			</div>

			<div class="flex flex-col items-center justify-center border-r border-white/20 p-2 sm:p-4">
				<div class="mb-0.5 text-2xl text-white sm:mb-1 sm:text-3xl">
					{formatNumber(auditStats?.inProgress || 0)}
				</div>
				<div class="text-center text-xs tracking-wide text-[#DAE1ED] uppercase">
					{$t('dashboard.widgets.auditStats.inProgress')}
				</div>
			</div>

			<div class="flex flex-col items-center justify-center p-2 sm:p-4">
				<div class="mb-0.5 text-2xl text-white sm:mb-1 sm:text-3xl">
					{formatNumber(auditStats?.overdue || 0)}
				</div>
				<div class="text-center text-xs tracking-wide text-[#DAE1ED] uppercase">
					{$t('dashboard.widgets.auditStats.overdue')}
				</div>
			</div>
		</div>
	</div>
</div>
