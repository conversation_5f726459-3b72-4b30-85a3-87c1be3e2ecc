<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Ellipsis, Eye, FileDown, Play, QrCode, LoaderCircle } from '@lucide/svelte';
	import { t, locale } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index';
	import { goto } from '$app/navigation';
	import type { AuditListDTO } from '$lib/DTO/audits/audits';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import QRCode from 'qrcode';
	import { page } from '$app/state';

	let {
		id,
		code,
		progress,
		row
	}: {
		id: string;
		code: string;
		progress: number;
		row: AuditListDTO;
	} = $props();

	let open = $state(false);
	let isDownloading = $state(false);
	let showQrDialog = $state(false);
	let qrCodeUrl = $state('');

	// Generate QR code when dialog opens
	$effect(() => {
		if (showQrDialog && page.url) {
			const auditUrl = `${page.url.origin}/${page.params.companySlug}/${page.params.plantSlug}/audits/${id}/evaluate`;
			QRCode.toDataURL(auditUrl, {
				width: 256,
				margin: 2,
				color: {
					dark: '#000000',
					light: '#ffffff'
				}
			})
				.then((url) => {
					qrCodeUrl = url;
				})
				.catch((err) => {
					console.error('Error generating QR code:', err);
				});
		}
	});

	async function downloadAuditPdf(auditId: string) {
		if (isDownloading) return;

		try {
			isDownloading = true;
			toast.info($t('audits.results.generatingPDF'));

			const response = await fetch(
				`/api/audits/${auditId}/export-pdf?lang=${$locale}&companySlug=${page.params.companySlug}&plantSlug=${page.params.plantSlug}`
			);
			if (!response.ok) throw new Error('Failed to download PDF');

			const blob = await response.blob();

			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `audit-${code || id}.pdf`;
			document.body.appendChild(a);
			a.click();

			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			toast.success($t('audits.results.pdfGenerated'));
		} catch (error) {
			console.error('Error downloading PDF:', error);
		} finally {
			isDownloading = false;
		}
	}

	function isAuditCompleted(): boolean {
		return !!row.completionDate;
	}

	function isAuditInProgress(): boolean {
		return progress > 0 && !row.completionDate;
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase">
			{#if isAuditCompleted()}
				<DropdownMenu.Item
					class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
					onclick={() => downloadAuditPdf(id)}
				>
					<FileDown class="text-white" />
					<span>{$t('audits.auditList.exportPdf')}</span>
				</DropdownMenu.Item>
			{:else if isAuditInProgress()}
				<DropdownMenu.Item
					class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
					onclick={() => {
						toast.info($t('audits.results.exportPdf'));
						downloadAuditPdf(id);
					}}
				>
					<FileDown class="text-white" />
					<span>{$t('audits.auditList.exportPdf')}</span>
				</DropdownMenu.Item>
			{/if}

			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() =>
					goto(
						`/${page.params.companySlug}/${page.params.plantSlug}/audits/${id}${row.completionDate ? '/results' : '/evaluate'}`
					)}
			>
				{#if row.completionDate}
					<Eye class="size-4 text-white" />
					<span>{$t('audits.auditList.viewResults')}</span>
				{:else}
					<Play class="size-4 text-white" />
					<span>{$t('audits.auditList.startAudit')}</span>
				{/if}
			</DropdownMenu.Item>

			{#if !row.completionDate}
				<DropdownMenu.Item
					class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
					onclick={() => (showQrDialog = true)}
				>
					<QrCode class="size-4 text-white" />
					<span>{$t('audits.auditList.showQrCode')}</span>
				</DropdownMenu.Item>
			{/if}
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<CustomDialog
	open={showQrDialog}
	subtitle={code}
	onClose={() => (showQrDialog = false)}
	title={$t('audits.dialogs.launchAudit')}
>
	<div class="flex flex-col items-center justify-center space-y-4 p-4">
		{#if qrCodeUrl}
			<img src={qrCodeUrl} alt="QR Code" class="rounded-lg" />
		{:else}
			<div class="flex h-64 w-64 items-center justify-center rounded-lg bg-gray-100">
				<LoaderCircle class="h-8 w-8 animate-spin text-gray-400" />
			</div>
		{/if}
	</div>
</CustomDialog>
