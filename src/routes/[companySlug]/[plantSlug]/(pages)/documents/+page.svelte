<script lang="ts">
	import { fade } from 'svelte/transition';
	import { t } from '$lib/translations';
	import type { PageData } from './$types';
	let { data }: { data: PageData } = $props();
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.documents')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9 shadow-md ring-2 ring-gray-50"
></div>
