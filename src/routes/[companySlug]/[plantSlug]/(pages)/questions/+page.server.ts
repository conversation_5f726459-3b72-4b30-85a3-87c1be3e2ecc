import { fail, type Actions } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { t } from '$lib/translations';
import { QuestionsService } from '$lib/server/services/questions';
import { TagsService } from '$lib/server/services/tags/index';
import { questionSchema } from '$lib/schemas/audits/questions';
import { createQuestionsBreadcrumbs } from './_utils/createBreadcrumbs';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';

export const load = async ({ parent }) => {
	const { context } = await parent();

	const [form, questions, tags, breadcrumbs] = await Promise.all([
		superValidate(zod(questionSchema)),
		QuestionsService.getQuestionsWithTags(context.plantId),
		TagsService.getTags(context.plantId),
		createQuestionsBreadcrumbs()
	]);

	return { form, questions, tags, breadcrumbs };
};

export const actions: Actions = {
	createQuestion: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(questionSchema));
		const context = await validatePlantCompanyAccessLight(
			params.companySlug!,
			params.plantSlug!,
			locals.user!
		);

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const { tagIds, tagNames, ...questionData } = form.data;
			await QuestionsService.createQuestionWithTagsAndNames(
				questionData,
				tagIds || [],
				tagNames || [],
				context.plantId
			);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to create question:', error);
			return fail(500, { form, message: t.get('errors.questions.failedCreating') });
		}
	},

	editQuestion: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(questionSchema));
		const context = await validatePlantCompanyAccessLight(
			params.companySlug!,
			params.plantSlug!,
			locals.user!
		);

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const { tagIds, tagNames, ...questionData } = form.data;
			await QuestionsService.editQuestionWithTagsAndNames(
				form.data.id!,
				questionData,
				tagIds || [],
				tagNames || [],
				context.plantId
			);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to update question:', error);
			return fail(500, { form, message: t.get('errors.questions.failedUpdating') });
		}
	},

	deleteQuestion: async ({ request }) => {
		const data = await request.formData();
		const id = data.get('id')?.toString();

		if (!id) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			const result = await QuestionsService.deleteQuestion(id);

			if (!result.success) {
				if (result.isUsed) {
					const auditTypeNames = result.usedInAuditTypes?.map((at) => at.name).join(', ') || '';
					return fail(409, {
						isUsed: true,
						auditTypeNames,
						usedInAuditTypes: result.usedInAuditTypes
					});
				} else {
					return fail(500, { message: t.get('errors.questions.failedDeleting') });
				}
			}

			return { success: true };
		} catch (error) {
			console.error('Failed to delete question:', error);
			return fail(500, { message: t.get('errors.questions.failedDeleting') });
		}
	}
};
