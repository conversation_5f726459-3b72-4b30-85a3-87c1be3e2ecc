<script lang="ts">
	import { t } from '$lib/translations';
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import { Funnel, FunnelX, Plus, X } from '@lucide/svelte';
	import { questionSchema } from '$lib/schemas/audits/questions';
	import { createColumns } from './_components/columns';
	import { fade, slide } from 'svelte/transition';
	import { useForm } from '$lib/hooks/superformValidation';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import FormQuestion from './_components/form-question.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import QuestionFilters from '$lib/customComponents/question-filters.svelte';
	import type { QuestionWithTagsDTO } from '$lib/DTO/questions/questions';

	let { data } = $props();

	let dialogOpen = $state(false);
	let globalFilter = $state('');
	let isEditMode = $state(false);
	let selectedQuestion = $state<QuestionWithTagsDTO | undefined>(undefined);
	let showFilters = $state(false);

	// Question filters state
	let questionFilters = $state({
		evaluationTypes: [] as string[],
		tags: [] as string[]
	});

	function handleSearch(value: string) {
		globalFilter = value;
	}

	function handleFiltersChange(filters: typeof questionFilters) {
		questionFilters = { ...filters };
	}

	// Filter questions based on selected filters
	const filteredQuestions = $derived(() => {
		let filtered = data.questions;

		// Filter by evaluation type
		if (questionFilters.evaluationTypes.length > 0) {
			filtered = filtered.filter((q) => questionFilters.evaluationTypes.includes(q.evaluationType));
		}

		// Filter by tags
		if (questionFilters.tags.length > 0) {
			filtered = filtered.filter((q) =>
				q.tags?.some((tag) => tag.name && questionFilters.tags.includes(tag.name))
			);
		}

		return filtered;
	});

	const form = useForm(data.form, questionSchema, 'form', () => {
		handleCloseDialog();
	});

	function handleEditQuestion(id: string) {
		isEditMode = true;
		selectedQuestion = data.questions.find((q: QuestionWithTagsDTO) => q.id === id) ?? undefined;
		dialogOpen = true;
	}

	const columnsWithCallbacks = createColumns({
		onEdit: handleEditQuestion
	});

	function handleCloseDialog() {
		dialogOpen = false;
		setTimeout(() => {
			form.reset();
			isEditMode = false;
			selectedQuestion = undefined;
		}, 200);
	}

	// Derived for active filters count and display
	const activeFiltersCount = $derived(() => {
		return questionFilters.evaluationTypes.length + questionFilters.tags.length;
	});

	function handleFiltersClear() {
		questionFilters = {
			evaluationTypes: [],
			tags: []
		};
		handleFiltersChange(questionFilters);
	}
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.questions')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9"
>
	<div class="mb-6 flex w-full items-center justify-between">
		<div class="max-w-md flex-1">
			<SearchInput value={globalFilter} onSearch={handleSearch} />
		</div>

		{#if activeFiltersCount() > 0}
			<div class="flex flex-1 items-center justify-center gap-4 px-4">
				{#if questionFilters.evaluationTypes.length > 0}
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-600">{$t('questions.evaluationType.label')}:</span>
						<span class="text-sm font-medium text-gray-800">
							{(() => {
								const text = questionFilters.evaluationTypes
									.map((type) => $t(`questions.evaluationType.${type}`))
									.join(', ');
								return text.length > 10 ? text.slice(0, 10) + '...' : text;
							})()}
						</span>
						<button
							type="button"
							onclick={() => {
								questionFilters.evaluationTypes = [];
								handleFiltersChange(questionFilters);
							}}
							class="text-red-500 hover:text-red-700"
							aria-label="Clear evaluation type filters"
						>
							<X class="h-4 w-4 cursor-pointer" />
						</button>
					</div>
				{/if}

				{#if questionFilters.tags.length > 0}
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-600">{$t('questions.tags.label')}:</span>
						<span class="text-sm font-medium text-gray-800">
							{(() => {
								const text = questionFilters.tags.join(', ');
								return text.length > 10 ? text.slice(0, 10) + '...' : text;
							})()}
						</span>
						<button
							type="button"
							onclick={() => {
								questionFilters.tags = [];
								handleFiltersChange(questionFilters);
							}}
							class="text-red-500 hover:text-red-700"
							aria-label="Clear tag filters"
						>
							<X class="h-4 w-4 cursor-pointer" />
						</button>
					</div>
				{/if}
			</div>
		{/if}

		<div class="ml-4 flex items-center gap-2">
			<ButtonIcon
				Icon={Funnel}
				onClickAction={() => (showFilters = !showFilters)}
				backgroundColor="#2E384F"
				hoverBackgroundColor="#2E384F/80"
			/>
			<ButtonIcon
				Icon={FunnelX}
				onClickAction={handleFiltersClear}
				backgroundColor="#D18385"
				hoverBackgroundColor="#D18385"
				hoverOpacity="80"
			/>
			<ButtonIcon onClickAction={() => (dialogOpen = true)} Icon={Plus} />
		</div>
	</div>

	{#if showFilters}
		<div class="mb-4 w-full shrink-0 overflow-hidden" transition:slide={{ duration: 300 }}>
			<QuestionFilters
				questions={data.questions}
				bind:filters={questionFilters}
				onFiltersChange={handleFiltersChange}
			/>
		</div>
	{/if}

	<div class="flex-1 overflow-hidden">
		<DataTable data={filteredQuestions()} columns={columnsWithCallbacks} {globalFilter} />
	</div>
</div>

<CustomDialog
	open={dialogOpen}
	title={isEditMode ? $t('questions.form.edit') : $t('questions.form.create')}
	subtitle={selectedQuestion
		? selectedQuestion.text.length > 20
			? selectedQuestion.text.substring(0, 20) + '...'
			: selectedQuestion.text
		: ''}
	onClose={handleCloseDialog}
	width="max-w-3xl"
>
	<FormQuestion
		{form}
		onClose={handleCloseDialog}
		{isEditMode}
		questionData={selectedQuestion}
		availableTags={data.tags}
	/>
</CustomDialog>
