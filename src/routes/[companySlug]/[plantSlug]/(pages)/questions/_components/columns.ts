import { renderComponent } from '$lib/components/ui/data-table';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import EvaluationTypeCell from '$lib/customComponents/tableFormat/evaluationType-cell.svelte';
import TagsCell from '$lib/customComponents/tableFormat/tags-cell.svelte';
import { t } from '$lib/translations';
import type { ColumnDef } from '@tanstack/table-core';
import DataTableActions from './data-table-actions.svelte';
import type { QuestionWithTagsDTO } from '$lib/DTO/questions/questions';

export function createColumns(callbacks: {
	onEdit: (id: string) => void;
}): ColumnDef<QuestionWithTagsDTO>[] {
	return [
		{
			accessorKey: 'text',
			header: ({ column }) =>
				renderComponent(CustomHeader, { text: t.get('questions.list.text'), column }),
			cell: ({ row }) => {
				const text = row.getValue('text') as string;
				return renderComponent(Cell, {
					content: text?.length > 100 ? text.substring(0, 100) + '...' : text,
					className: 'line-clamp-3 leading-tight'
				});
			},
			enableSorting: true,
			size: 400,
			minSize: 300
		},
		{
			accessorKey: 'subtext',
			header: ({ column }) =>
				renderComponent(CustomHeader, { text: t.get('questions.list.subtext'), column }),
			cell: ({ row }) => {
				const subtext = row.getValue('subtext') as string;
				return renderComponent(Cell, {
					content: subtext?.length > 50 ? subtext.substring(0, 50) + '...' : subtext,
					className: 'line-clamp-3 leading-tight'
				});
			},
			enableSorting: true,
			size: 350,
			minSize: 250
		},
		{
			accessorKey: 'evaluationType',
			header: ({ column }) =>
				renderComponent(CustomHeader, { text: t.get('questions.list.evaluationType'), column }),
			cell: ({ row }) =>
				renderComponent(EvaluationTypeCell, { content: row.getValue('evaluationType') }),
			enableSorting: true,
			size: 150,
			minSize: 120
		},
		{
			accessorKey: 'tags',
			header: ({ column }) =>
				renderComponent(CustomHeader, { text: t.get('questions.list.tags'), column }),
			cell: ({ row }) => {
				const tags = row.original.tags || [];
				return renderComponent(TagsCell, { tags });
			},
			enableSorting: true,
			size: 200,
			minSize: 150
		},
		{
			id: 'actions',
			enableSorting: false,
			enableHiding: false,
			meta: {
				headerClass: 'text-right mr-2',
				cellClass: 'text-right mr-2'
			},
			cell: ({ row }) => {
				return renderComponent(DataTableActions, {
					id: row.original.id ?? '',
					row: row.original,
					onEdit: () => (row.original.id ? callbacks.onEdit(row.original.id) : undefined)
				});
			},
			size: 80,
			minSize: 60
		}
	];
}
