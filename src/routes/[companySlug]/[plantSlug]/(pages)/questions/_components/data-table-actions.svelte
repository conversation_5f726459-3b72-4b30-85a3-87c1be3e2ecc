<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { <PERSON><PERSON>sis, Eye, FileDown, Play, Trash, Wrench, Edit } from '@lucide/svelte';
	import { t } from '$lib/translations';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index';
	import { goto } from '$app/navigation';

	let {
		id,
		row,
		onEdit
	}: {
		id: string;
		row: any;
		onEdit: (auditId: string) => void;
	} = $props();

	let open = $state(false);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase ">
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
				onclick={() => onEdit(id)}
			>
				<Edit class="text-white" />
				<span>{$t('common.buttons.edit')}</span>
			</DropdownMenu.Item>

			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
				onclick={() => (open = true)}
			>
				<Trash class="text-white" />
				<span>{$t('common.buttons.delete')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	{id}
	title={$t('questions.delete.deleteTitle')}
	description={$t('questions.delete.deleteDescription')}
	code={row.text && row.text.length > 20 ? row.text.substring(0, 20) + '...' : row.text}
	formAction="?/deleteQuestion"
	onDelete={() => toast.success($t('questions.delete.success'))}
	onClose={() => (open = false)}
/>
