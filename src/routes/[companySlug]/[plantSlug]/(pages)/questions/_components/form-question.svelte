<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as RadioGroup from '$lib/components/ui/radio-group/index';

	import { Button } from '$lib/components/ui/button';
	import { EvaluationMethods } from '$lib/enums/audits';
	import { t } from '$lib/translations';
	import type { QuestionsDTO, QuestionWithTagsDTO } from '$lib/DTO/questions/questions';
	import type { TagDTO } from '$lib/DTO/tags';
	import { Textarea } from '$lib/components/ui/textarea';
	import TagSelector from '$lib/customComponents/tags/tag-selector.svelte';

	let {
		form,
		onClose,
		isEditMode,
		questionData,
		availableTags = [],
		formAction = isEditMode ? '?/editQuestion' : '?/createQuestion'
	} = $props<{
		form: any;
		onClose: () => void;
		isEditMode: boolean;
		questionData?: QuestionsDTO | QuestionWithTagsDTO;
		availableTags?: TagDTO[];
		formAction?: string;
	}>();

	const { form: formData, enhance, errors } = form;

	// Initialize form data when in edit mode
	if (isEditMode && questionData) {
		$formData.id = questionData.id;
		$formData.text = questionData.text;
		$formData.subtext = questionData.subtext;
		$formData.evaluationType = questionData.evaluationType;
	}

	// Tags state - initialize immediately if we have questionData
	let selectedTags: TagDTO[] = $state(
		isEditMode && questionData && 'tags' in questionData ? questionData.tags || [] : []
	);

	// Initialize tagIds in form data
	if (isEditMode && questionData && 'tags' in questionData && questionData.tags) {
		$formData.tagIds = questionData.tags
			.map((tag: TagDTO) => tag.id || '')
			.filter((id: string) => id);
		$formData.tagNames = questionData.tags
			.map((tag: TagDTO) => tag.name)
			.filter((name: string) => name);
	}

	function handleTagsChange(tags: TagDTO[]) {
		selectedTags = tags;
		// Update form data with tag IDs and names
		$formData.tagIds = tags.map((tag) => tag.id || '').filter((id) => id);
		$formData.tagNames = tags.map((tag) => tag.name).filter((name) => name);
	}
</script>

<form
	id="question-form"
	method="POST"
	action={formAction}
	use:enhance
	class={`space-y-3 ${isEditMode ? '-mt-3' : 'mt-0'}`}
>
	{#if isEditMode && questionData}
		<input type="hidden" name="id" class="hidden" value={questionData.id} />
	{/if}

	<div class="relative pb-4">
		<Form.Field {form} name="text">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('questions.form.text')}</Form.Label>
					<Textarea
						{...props}
						bind:value={$formData.text}
						class="h-32 {$errors.text ? 'border-red-500' : ''}"
						placeholder={$t('questions.form.textPlaceholder')}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="text-destructive absolute right-0 -bottom-1 text-xs" />
		</Form.Field>
	</div>

	<div class="relative pb-4">
		<Form.Field {form} name="subtext">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('questions.form.subtext')}</Form.Label>
					<Textarea
						{...props}
						bind:value={$formData.subtext}
						class={$errors.subtext ? 'border-red-500' : ''}
						placeholder={$t('questions.form.subtextPlaceholder')}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="text-destructive absolute right-0 -bottom-1 text-xs" />
		</Form.Field>
	</div>

	<div class="relative pb-4">
		<Form.Fieldset {form} name="evaluationType" class="space-y-3">
			<Form.Legend class="text-sm font-medium">
				{$t('auditTypes.newQuestion.evaluationTypeLabel')}
			</Form.Legend>

			<div class="mx-4 grid grid-cols-1 gap-4 lg:grid-cols-2">
				<!-- Radio buttons column -->
				<div class="min-w-0">
					<RadioGroup.Root
						bind:value={$formData.evaluationType}
						class="flex flex-col "
						name="evaluationType"
					>
						{#each Object.entries(EvaluationMethods) as [key, value] (key)}
							<div class="flex items-center space-y-0 space-x-3">
								<Form.Control>
									{#snippet children({ props })}
										<RadioGroup.Item {value} {...props} />
										<Form.Label class="cursor-pointer text-sm font-normal">
											{$t(`audits.evaluationTypes.${key}`)}
										</Form.Label>
									{/snippet}
								</Form.Control>
							</div>
						{/each}
					</RadioGroup.Root>
				</div>

				<!-- Description column -->
				<div class="flex min-w-0 items-center">
					{#if $formData.evaluationType}
						{@const selectedKey = Object.entries(EvaluationMethods).find(
							([key, value]) => value === $formData.evaluationType
						)?.[0]}
						{#if selectedKey}
							<div class="bg-muted/30 border-primary w-full min-w-0 rounded-lg border-l-4 p-3">
								<div
									class="text-muted-foreground text-sm leading-relaxed break-words whitespace-pre-wrap"
								>
									{@html $t(`audits.evaluationTypes.description.${selectedKey}`)}
								</div>
							</div>
						{/if}
					{:else}
						<div class="bg-muted/50 w-full min-w-0 rounded-lg border border-dashed p-3">
							<p class="text-muted-foreground text-sm break-words italic">
								{$t('auditTypes.newQuestion.selectEvaluationType')}
							</p>
						</div>
					{/if}
				</div>
			</div>

			<Form.FieldErrors class="text-destructive text-xs" />
		</Form.Fieldset>
	</div>

	<!-- Tags section -->
	<div class="relative pb-4">
		<label
			class="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
			for="tags"
		>
			{$t('questions.form.tags')}
		</label>
		<div class="mt-2">
			<TagSelector
				{availableTags}
				{selectedTags}
				placeholder={$t('questions.form.tagsPlaceholder')}
				onTagsChange={handleTagsChange}
			/>
		</div>
	</div>

	<!-- Hidden inputs for tag IDs and names -->
	{#each selectedTags as tag}
		{#if tag.id}
			<input type="hidden" name="tagIds" value={tag.id} />
			<input type="hidden" name="tagNames" value={tag.name} />
		{/if}
	{/each}

	<div class="flex justify-end gap-4">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={onClose}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{isEditMode ? $t('common.buttons.save') : $t('common.buttons.create')}
		</Button>
	</div>
</form>
