import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail } from '@sveltejs/kit';
import { changePasswordSchema } from '$lib/schemas/auth.js';
import { AuthService } from '$lib/server/services/auth/index.js';
import { InvalidCurrentPasswordError } from '$lib/server/services/auth/changePassword.js';

export const actions = {
	changePassword: async ({ request, locals }) => {
		const form = await superValidate(request, zod(changePasswordSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await AuthService.changePassword(
				form.data.oldPassword,
				form.data.newPassword,
				locals.user!.id
			);

			return { form, success: true };
		} catch (error) {
			if (error instanceof InvalidCurrentPasswordError) {
				return fail(400, {
					form,
					error: 'current_password_incorrect'
				});
			}

			return fail(400, {
				form,
				error: 'password_change_failed'
			});
		}
	}
};
