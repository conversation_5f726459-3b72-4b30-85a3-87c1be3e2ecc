import { WorkplaceService } from '$lib/server/services/workplaces';
import { AuditService } from '$lib/server/services/audits';
import { fail, type Actions, error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms';
import { workplaceSchema } from '$lib/schemas/workplace';
import { zod } from 'sveltekit-superforms/adapters';
import { t } from '$lib/translations';
import { createWorkplaceBreadcrumbs } from './_utils/createBreadcrumbs';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
export const load: PageServerLoad = async ({ parent, locals }) => {
	const { context } = await parent();
	if (!locals.user) {
		throw error(401, 'Nepřihlášený uživatel');
	}
	const [workplaces, auditors, workplaceForm] = await Promise.all([
		WorkplaceService.getWorkplaces(context.plantId),
		AuditService.getAuditors(context.plantId),
		superValidate(zod(workplaceSchema))
	]);

	return {
		workplaces,
		auditors,
		workplaceForm,
		breadcrumbs: createWorkplaceBreadcrumbs(
			undefined,
			undefined,
			context.companySlug,
			context.plantSlug
		)
	};
};

export const actions: Actions = {
	createWorkplace: async ({ request, params, locals }) => {
		if (!locals.user?.id) {
			return fail(401, {
				success: false,
				message: t.get('errors.auth.notAuthenticated')
			});
		}

		const context = await validatePlantCompanyAccessLight(
			params.companySlug!,
			params.plantSlug!,
			locals!.user
		);
		const form = await superValidate(request, zod(workplaceSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const workplaceData = {
			...form.data,
			plantId: context.plantId
		};

		const result = await WorkplaceService.createWorkplace(workplaceData);

		if (!result) {
			return fail(422, {
				success: false,
				message: t.get('errors.workplaces.workplaceFailedCreate')
			});
		}
		return { form, success: true };
	},
	deleteWorkplace: async ({ request, locals }) => {
		if (!locals.user?.id) {
			return fail(401, {
				message: t.get('errors.auth.notAuthenticated')
			});
		}
		const formData = await request.formData();
		const id = formData.get('id') as string;

		try {
			await WorkplaceService.deleteWorkplace(id);
			return { success: true };
		} catch {
			return fail(422, {
				message: t.get('errors.workplaces.workplaceFailedDelete')
			});
		}
	}
};
