<script lang="ts">
	import { fade } from 'svelte/transition';
	import { Plus } from '@lucide/svelte';
	import { t } from '$lib/translations';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import type { WorkplaceDTO } from '$lib/DTO/workplaces/workplaces';
	import FormWorkplace from './_components/form-workplace.svelte';
	import { useForm } from '$lib/hooks/superformValidation';
	import { workplaceSchema } from '$lib/schemas/workplace';
	import { columns } from './_components/columns';
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';

	let { data } = $props();
	let dialogOpen = $state(false);
	let globalFilter = $state('');
	let selectedWorkplace = $state<WorkplaceDTO | null>(null);

	function handleSearch(value: string) {
		globalFilter = value;
	}

	function handleCreateWorkplace() {
		selectedWorkplace = null;
		dialogOpen = true;
	}

	function handleCloseDialog() {
		dialogOpen = false;
		setTimeout(() => {
			form.reset();
			selectedWorkplace = null;
		}, 300);
	}

	const form = useForm(data.workplaceForm, workplaceSchema, 'form', () => {
		dialogOpen = false;
	});
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.workplaces')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9 ring-2 ring-gray-50"
>
	<div class="mb-6 flex items-center justify-between">
		<div class="flex-1">
			<div class="relative flex max-w-sm">
				<SearchInput value={globalFilter} onSearch={handleSearch} />
			</div>
		</div>
		<div class="ml-4">
			<ButtonIcon onClickAction={handleCreateWorkplace} Icon={Plus} />
		</div>
	</div>

	<div class="flex-1 overflow-hidden">
		<DataTable data={data.workplaces} {columns} {globalFilter} />
	</div>
</div>

<CustomDialog open={dialogOpen} title={$t('workplaces.create.title')} onClose={handleCloseDialog}>
	<FormWorkplace
		{form}
		onClose={handleCloseDialog}
		workplace={selectedWorkplace}
		auditors={data.auditors}
	/>
</CustomDialog>
