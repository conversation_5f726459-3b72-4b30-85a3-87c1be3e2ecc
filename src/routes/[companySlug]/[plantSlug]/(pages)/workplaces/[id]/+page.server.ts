import { WorkplaceService } from '$lib/server/services/workplaces';
import { t } from '$lib/translations';
import { fail, type Actions, error } from '@sveltejs/kit';
import { createWorkplaceBreadcrumbs } from '../_utils/createBreadcrumbs';
import { workplaceInfoSchema } from '$lib/schemas/workplace';
import { zod } from 'sveltekit-superforms/adapters';
import { superValidate } from 'sveltekit-superforms';
import { AuditTypeService } from '$lib/server/services/auditTypes';
import { createAuditSchema } from '$lib/schemas/audits/audits';
import { mapAuditToListType } from '$lib/DTO/audits/audits';
import { AuditService, AuditSetupService } from '$lib/server/services/audits';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { PlantEvaluationConfigService } from '$lib/server/services/tenants';

export const load = async ({ params, parent }) => {
	const { context } = await parent();

	const [
		workplace,
		responsiblePeople,
		workplaceForm,
		audits,
		auditTypes,
		instanceForm,
		plantEvaluationConfig
	] = await Promise.all([
		WorkplaceService.getWorkplaceDetails(params.id),
		AuditService.getAuditors(context.plantId),
		superValidate(zod(workplaceInfoSchema)),

		//audits
		WorkplaceService.getAuditsForWorkplace(params.id),
		AuditTypeService.getAuditTypes(context.plantId, true),
		superValidate(zod(createAuditSchema)),

		PlantEvaluationConfigService.getPlantEvaluationConfig(context.plantId)
	]);

	if (!workplace) {
		throw error(404, t.get('errors.workplaces.workplaceNotFound'));
	}

	const auditsList = audits.map((audit) =>
		mapAuditToListType({
			...audit,
			workplace: {
				...audit.workplace,
				id: audit.workplace?.id || '',
				name: audit.workplace?.name || '',
				code: audit.workplace?.code || ''
			}
		})
	);

	return {
		workplace,
		responsiblePeople,
		workplaceForm,
		audits: auditsList,
		auditTypes,
		instanceForm,
		plantEvaluationConfig,
		breadcrumbs: createWorkplaceBreadcrumbs(
			workplace.name,
			undefined,
			context.companySlug,
			context.plantSlug
		)
	};
};

export const actions: Actions = {
	updateWorkplace: async ({ request, params }) => {
		const form = await superValidate(request, zod(workplaceInfoSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		if (!params.id) {
			throw error(400, t.get('errors.workplaces.invalidWorkplaceId'));
		}

		try {
			await WorkplaceService.editWorkplace(params.id, form.data);

			return { form, success: true };
		} catch (error) {
			console.error('Failed to update workplace:', error);
			return fail(422, {
				form,
				message: t.get('errors.workplaces.workplaceFailedUpdate')
			});
		}
	},

	createAudit: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(createAuditSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		if (!params.id) {
			throw error(400, t.get('errors.workplaces.invalidWorkplaceId'));
		}

		if (!locals.user?.id) {
			return fail(401, {
				form,
				message: t.get('errors.auth.notAuthenticated')
			});
		}

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user
			);
			const workplace = await WorkplaceService.getWorkplaceById(params.id);

			if (!workplace || workplace.plantId !== context.plantId) {
				return fail(403, { message: 'Unauthorized access' });
			}
			const result = await AuditService.createAuditInstance(
				form.data.auditTypeId,
				form.data,
				locals.user!.id
			);

			if (!result) {
				return fail(422, {
					form,
					message: t.get('errors.audits.auditSetup.failedCreatingInstance')
				});
			}

			return { form, success: true };
		} catch (error) {
			console.error('Failed to create audit:', error);
			return fail(422, {
				form,
				message: t.get('errors.audits.auditSetup.failedCreatingInstance')
			});
		}
	},
	updateAudit: async ({ request }) => {
		const form = await superValidate(request, zod(createAuditSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		if (!form.data.id) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			await AuditService.updateAuditInstance(form.data.id, form.data);

			return {
				form,
				success: true
			};
		} catch (error) {
			console.error('Failed to update audit:', error);
			return fail(422, {
				form,
				message: t.get('errors.audits.auditSetup.failedUpdate')
			});
		}
	},
	deleteAudit: async ({ request }) => {
		const formData = await request.formData();
		const auditId = formData.get('id') as string;

		try {
			await AuditSetupService.deleteAuditInstance(auditId);
			return { message: t.get('errors.audits.auditSetup.auditDeleted') };
		} catch (error) {
			console.error('Failed to delete audit:', error);
			return fail(422, {
				message: t.get('errors.audits.auditSetup.failedDeleteInst')
			});
		}
	}
};
