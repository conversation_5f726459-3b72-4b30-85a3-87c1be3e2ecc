<script lang="ts">
	import { t } from '$lib/translations/index.js';
	import { fade, fly } from 'svelte/transition';
	import { Ellipsis } from '@lucide/svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import WorkplaceInfo from './_components/workplace-info.svelte';
	import WorkplaceForm from './_components/workplace-form.svelte';
	import WorkplaceAudits from './_components/workplace-audits.svelte';

	let { data } = $props();

	let editInfo = $state(false);
	let workplaceFormRef = $state<any>(null);
</script>

<svelte:head>
	<title>LeanAudit - {data.workplace.name}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-4 mb-8 flex h-[calc(100vh-10rem)] w-full flex-col gap-4 overflow-hidden lg:flex-row lg:gap-4"
>
	<div
		class="flex h-full w-full flex-col rounded-2xl bg-white p-2 lg:flex-row lg:gap-4 lg:rounded-none lg:bg-transparent lg:shadow-none"
	>
		<!-- Info panel -->
		<div
			class="w-full overflow-y-auto px-4 py-6 sm:px-10 sm:py-8 lg:h-full lg:w-[25%] lg:shrink-0 lg:rounded-2xl lg:bg-white"
		>
			<div class="flex flex-row justify-between">
				{@render workplaceInfo($t('workplaces.info.name'), data.workplace.name)}
				<ButtonIcon
					onClickAction={() => {
						if (editInfo && workplaceFormRef) {
							workplaceFormRef.attemptCancelOrNavigate();
						} else {
							editInfo = true;
						}
					}}
					Icon={Ellipsis}
				/>
			</div>

			{#if !editInfo}
				<div class="" in:fly={{ duration: 300 }}>
					<WorkplaceInfo workplace={data.workplace} />
				</div>
			{:else}
				<div class="py-4" in:fly={{ duration: 300 }}>
					<WorkplaceForm
						bind:this={workplaceFormRef}
						workplace={data.workplace}
						responsiblePeople={data.responsiblePeople}
						workplaceForm={data.workplaceForm}
						onCancel={() => {
							editInfo = false;
						}}
						onSave={() => {
							editInfo = false;
						}}
					/>
				</div>
			{/if}
		</div>

		<!-- Audits panel  -->
		<div class="flex min-h-0 flex-1 flex-col overflow-hidden p-4 lg:rounded-2xl lg:bg-white lg:p-6">
			<WorkplaceAudits
				user={data.user}
				audits={data.audits}
				auditors={data.responsiblePeople}
				auditTypes={data.auditTypes}
				instanceForm={data.instanceForm}
				workplaceId={data.workplace.id}
				plantEvaluationConfig={data.plantEvaluationConfig}
			/>
		</div>
	</div>
</div>

{#snippet workplaceInfo(name: string, content: string, additionalContentClass: string = '')}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span
			class={`-mt-0.5 truncate text-sm text-[#4B505A] lg:-mt-1 lg:text-base ${additionalContentClass}`}
			>{content}</span
		>
	</div>
{/snippet}
