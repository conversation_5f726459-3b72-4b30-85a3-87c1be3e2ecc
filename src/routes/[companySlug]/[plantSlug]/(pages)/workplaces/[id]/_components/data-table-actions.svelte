<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { <PERSON><PERSON>sis, Eye, FileDown, Play, Trash, Wrench, Edit } from '@lucide/svelte';
	import { t, locale } from '$lib/translations';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index';
	import { goto } from '$app/navigation';
	import type { AuditListDTO } from '$lib/DTO/audits/audits';
	import { page } from '$app/state';

	let {
		id,
		code,
		progress,
		row,
		onEdit
	}: {
		id: string;
		code: string;
		progress: number;
		row: AuditListDTO;
		onEdit: (auditId: string) => void;
	} = $props();

	let open = $state(false);
	let isDownloading = $state(false);

	function isAuditCompleted(): boolean {
		return !!row.completionDate;
	}

	function isAuditInProgress(): boolean {
		return progress > 0 && !row.completionDate;
	}

	async function downloadAuditPdf(auditId: string) {
		if (isDownloading) return;

		try {
			isDownloading = true;
			toast.info($t('audits.results.generatingPDF'));

			const response = await fetch(
				`/api/audits/${auditId}/export-pdf?lang=${$locale}&companySlug=${page.params.companySlug}&plantSlug=${page.params.plantSlug}`
			);
			if (!response.ok) throw new Error('Failed to download PDF');

			const blob = await response.blob();

			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `audit-${code || id}.pdf`;
			document.body.appendChild(a);
			a.click();

			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			toast.success($t('audits.results.pdfGenerated'));
		} catch (error) {
			console.error('Error downloading PDF:', error);
			toast.error($t('audits.results.pdfGenerationFailed'));
		} finally {
			isDownloading = false;
		}
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase ">
			{#if isAuditCompleted()}
				<DropdownMenu.Item
					class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
					onclick={() => downloadAuditPdf(id)}
				>
					<FileDown class="text-white" />
					<span>{$t('audits.auditList.exportPdf')}</span>
				</DropdownMenu.Item>
			{:else if isAuditInProgress()}
				<DropdownMenu.Item
					class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
					onclick={() => {
						toast.info($t('audits.results.exportPdf'));
						downloadAuditPdf(id);
					}}
				>
					<FileDown class="text-white" />
					<span>{$t('audits.auditList.exportPdf')}</span>
				</DropdownMenu.Item>
			{/if}

			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
				onclick={() =>
					goto(
						`/${page.params.companySlug}/${page.params.plantSlug}/audits/${id}${row.completionDate ? '/results' : '/evaluate'}`
					)}
			>
				{#if row.completionDate}
					<Eye class="text-white" />
					<span>{$t('audits.auditList.viewResults')}</span>
				{:else}
					<Play class="text-white" />
					<span>{$t('audits.auditList.startAudit')}</span>
				{/if}
			</DropdownMenu.Item>
			<!-- Edit button showing only if audit is not yet finisher -->
			{#if !row.completionDate}
				<DropdownMenu.Item
					class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
					onclick={() => onEdit(id)}
				>
					<Edit class="text-white" />
					<span>{$t('audits.auditList.editAudit')}</span>
				</DropdownMenu.Item>
			{/if}

			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
				onclick={() => (open = true)}
			>
				<Trash class="text-white" />
				<span>{$t('audits.auditList.deleteAudit')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	{id}
	title={$t('audits.alertMessages.deleteTitle')}
	description={$t('audits.alertMessages.deleteDescription')}
	{code}
	formAction="?/deleteAudit"
	onDelete={() => toast.success($t('audits.actionMessages.auditDeleted'))}
	onClose={() => (open = false)}
/>
