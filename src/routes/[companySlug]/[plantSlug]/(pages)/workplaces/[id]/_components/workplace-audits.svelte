<script lang="ts">
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import { CalendarFold, Download, List, Plus } from '@lucide/svelte';
	import { createColumns } from './columns';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import AuditCalendarView from '../../../audits/[id]/_components/audit-calendar-view.svelte';
	import { fade } from 'svelte/transition';
	import { useForm } from '$lib/hooks/superformValidation';
	import { createAuditSchema } from '$lib/schemas/audits/audits';
	import { t } from '$lib/translations';
	import FormAuditNoWorkplace from './form-audit-no-workplace.svelte';
	import { toast } from 'svelte-sonner';

	let { user, audits, auditors, auditTypes, instanceForm, workplaceId, plantEvaluationConfig } =
		$props();
	let dialogOpen = $state(false);
	let globalFilter = $state('');
	let viewMode: 'table' | 'calendar' = $state('table');

	const columnsWithCallbacks = createColumns(
		{
			onEdit: handleEditAudit
		},
		user!.id,
		plantEvaluationConfig
	);

	function handleSearch(value: string) {
		globalFilter = value;
	}

	let isEditMode = $state(false);
	let selectedAuditId = $state<string | null>(null);

	function handleCreateAudit() {
		isEditMode = false;
		selectedAuditId = null;
		form.reset();
		dialogOpen = true;
	}
	function handleEditAudit(id: string) {
		selectedAuditId = id;
		isEditMode = true;
		form.reset();
		dialogOpen = true;
	}
	function handleCloseDialog() {
		dialogOpen = false;
		if (!isEditMode) {
			form.reset();
		}
	}
	let form = useForm(instanceForm, createAuditSchema, 'form', () => {
		dialogOpen = false;
		toast.success($t('audits.actionMessages.auditCreated'));
	});
</script>

<div class="my-4 flex h-full flex-col">
	<div
		class="flex shrink-0 flex-col items-start justify-between gap-4 pb-6 sm:flex-row sm:items-center sm:gap-0"
	>
		<div class="w-full flex-1 sm:w-auto">
			<div class="relative flex max-w-sm">
				<SearchInput value={globalFilter} onSearch={handleSearch} />
			</div>
		</div>
		<div class="flex w-full items-center gap-2 overflow-x-auto pb-2 sm:w-auto sm:pb-0">
			<div class="bg-secondary flex rounded-xl">
				<ButtonIcon
					Icon={CalendarFold}
					onClickAction={() => (viewMode = 'calendar')}
					backgroundColor={viewMode === 'calendar' ? '#2E384F' : '#B1B7C3'}
					hoverBackgroundColor="#2E384F"
				/>

				<ButtonIcon
					Icon={List}
					onClickAction={() => (viewMode = 'table')}
					backgroundColor={viewMode === 'table' ? '#2E384F' : '#B1B7C3'}
					hoverBackgroundColor="#2E384F"
				/>
			</div>

			<ButtonIcon
				Icon={Download}
				onClickAction={() => {
					/*call handle download*/
				}}
				backgroundColor="#B1B7C3"
				hoverBackgroundColor="#697691"
			/>
			<ButtonIcon Icon={Plus} onClickAction={handleCreateAudit} />
		</div>
	</div>

	<div class="relative min-h-0 flex-1 overflow-hidden p-2 sm:p-0">
		{#if viewMode === 'table'}
			<div class="transition-container" transition:fade={{ duration: 300 }}>
				<div class="flex h-full flex-col">
					<div class="flex-1 overflow-auto">
						<DataTable data={audits} columns={columnsWithCallbacks} {globalFilter} pageSize={7} />
					</div>
				</div>
			</div>
		{:else}
			<div class="transition-container" transition:fade={{ duration: 300 }}>
				<AuditCalendarView {audits} />
			</div>
		{/if}
	</div>
</div>

<CustomDialog
	open={dialogOpen}
	title={isEditMode
		? $t('workplaces.audits.editInstanceTitle')
		: $t('workplaces.audits.newInstanceTitle')}
	onClose={() => (dialogOpen = false)}
>
	<FormAuditNoWorkplace
		{form}
		{audits}
		{auditors}
		{auditTypes}
		{workplaceId}
		onClose={handleCloseDialog}
		{isEditMode}
		{selectedAuditId}
	/>
</CustomDialog>

<style>
	/* animated container keeps the size */
	.transition-container {
		position: absolute;
		inset: 0;
		width: 100%;
		height: 100%;
	}
</style>
