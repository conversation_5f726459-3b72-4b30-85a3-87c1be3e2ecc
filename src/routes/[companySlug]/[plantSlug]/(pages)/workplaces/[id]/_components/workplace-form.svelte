<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as Command from '$lib/components/ui/command/index';
	import * as Popover from '$lib/components/ui/popover/index';
	import { Input } from '$lib/components/ui/input';
	import Textarea from '$lib/components/ui/textarea/textarea.svelte';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import { cn } from '$lib/utils';
	import { ChevronsUpDown } from '@lucide/svelte';
	import { Check } from '@lucide/svelte';
	import { Switch } from '$lib/components/ui/switch';
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { useForm } from '$lib/hooks/superformValidation';
	import { workplaceInfoSchema, type WorkplaceInfoForm } from '$lib/schemas/workplace';
	import ConfirmDialog from '$lib/customComponents/confirm-dialog.svelte';

	let { workplace, responsiblePeople, workplaceForm, onCancel, onSave } = $props();
	let responsiblePersonOpen = $state(false);
	let isSaveChangesDialogOpen = $state(false);

	let originalData: WorkplaceInfoForm = $state({} as WorkplaceInfoForm);

	let form = useForm<WorkplaceInfoForm>(
		workplaceForm,
		workplaceInfoSchema,
		'form',
		() => {
			toast.success($t('workplaces.info.infoSaved'));
			originalData = { ...$formData };
			onSave();
		},
		() => {
			toast.error($t('workplaces.info.SaveFailed'));
		}
	);

	const { form: formData, errors, enhance } = form;

	function hasChanges(): boolean {
		if (!originalData.name) return false;

		const changes =
			$formData.name !== originalData.name ||
			$formData.code !== originalData.code ||
			$formData.responsiblePersonId !== originalData.responsiblePersonId ||
			$formData.eKaizenWorkstationId !== originalData.eKaizenWorkstationId ||
			$formData.active !== originalData.active ||
			$formData.description !== originalData.description;

		return changes;
	}

	function submitForm() {
		const form = document.querySelector('form[action="?/updateWorkplace"]') as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	function handleAttemptCancelOrNavigate() {
		const changes = hasChanges();

		if (changes) {
			isSaveChangesDialogOpen = true;
		} else {
			onCancel();
		}
	}

	export function attemptCancelOrNavigate() {
		handleAttemptCancelOrNavigate();
	}

	onMount(() => {
		$formData = {
			...workplace,
			description: workplace.description || '',
			responsiblePersonId: workplace.responsiblePerson?.id || '',
			eKaizenWorkstationId: workplace.eKaizenWorkstationId || ''
		};

		originalData = {
			name: workplace.name,
			code: workplace.code,
			responsiblePersonId: workplace.responsiblePerson?.id || '',
			eKaizenWorkstationId: workplace.eKaizenWorkstationId || '',
			active: workplace.active,
			description: workplace.description || ''
		};
	});
</script>

<form method="POST" action="?/updateWorkplace" use:enhance>
	<input type="hidden" name="workplaceId" value={workplace.id} />
	<div class="space-y-1">
		<div class="relative pb-4">
			<Form.Field {form} name="name">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('workplaces.form.name')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.name}
							class={$errors.name ? 'border-red-500' : ''}
							placeholder={$t('workplaces.form.namePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-4">
			<Form.Field {form} name="code">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('workplaces.form.code')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.code}
							class={$errors.code ? 'border-red-500' : ''}
							placeholder={$t('workplaces.form.codePlaceholder')}
							maxlength={10}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-4">
			<Form.Field {form} name="responsiblePersonId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('workplaces.info.responsiblePerson')}</Form.Label>
						<Popover.Root bind:open={responsiblePersonOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'justify-between',
									!$formData.responsiblePersonId && 'text-muted-foreground',
									$errors.responsiblePersonId && 'border-red-500'
								)}
								role="combobox"
								{...props}
							>
								{#if $formData.responsiblePersonId && responsiblePeople}
									{(() => {
										const responsiblePerson = responsiblePeople.find(
											(a: { id: string }) => a.id === $formData.responsiblePersonId
										);
										return responsiblePerson
											? `${responsiblePerson.name.firstName || ''} ${responsiblePerson.name.lastName || ''}`
											: $t('workplaces.info.responsiblePersonPlaceholder');
									})()}
								{:else}
									{$t('workplaces.info.responsiblePersonPlaceholder')}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.responsiblePersonId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input
										autofocus
										placeholder={$t('workplaces.info.responsiblePersonPlaceholder')}
										class="h-9 truncate"
									/>
									<Command.List>
										<Command.Empty>{$t('workplaces.errors.noAuditorFound')}</Command.Empty>
										<Command.Group>
											{#each responsiblePeople as responsiblePerson, index (index)}
												<Command.Item
													value={`${responsiblePerson.name.firstName || ''} ${responsiblePerson.name.lastName || ''}`}
													onSelect={() => {
														$formData.responsiblePersonId = responsiblePerson.id;
														responsiblePersonOpen = false;
													}}
												>
													<span
														>{responsiblePerson.name.firstName || ''}
														{responsiblePerson.name.lastName || ''}</span
													>
													{#if responsiblePerson.id === $formData.responsiblePersonId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-4">
			<Form.Field {form} name="eKaizenWorkstationId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('workplaces.form.eKaizenId')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.eKaizenWorkstationId}
							class={$errors.eKaizenWorkstationId ? 'border-red-500' : ''}
							placeholder={$t('workplaces.form.eKaizenIdPlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-4">
			<Form.Field {form} name="active">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="flex items-start gap-4">
							<div class="flex flex-col items-center gap-2">
								<Switch
									bind:checked={$formData.active}
									{...props}
									class={cn(
										'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out',
										'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
										'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
										'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
										'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
										'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
										'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
										'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
										$errors.active ? 'ring-2 ring-red-500' : ''
									)}
								/>

								<span
									class={cn(
										'rounded-lg px-4 py-1 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
										'mt-2 inline-block min-w-[105px]',
										$formData.active
											? 'bg-[#414E6B] text-white shadow-sm'
											: 'bg-gray-100 text-gray-600'
									)}
								>
									{$formData.active ? $t('workplaces.info.active') : $t('workplaces.info.inactive')}
								</span>
							</div>

							<span class="mt-1 flex-1 text-xs text-gray-600">
								{$t('workplaces.info.statusDescription')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-4">
			<Form.Field {form} name="description">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('workplaces.info.description')}</Form.Label>
						<Textarea
							{...props}
							bind:value={$formData.description}
							class={$errors.description ? 'border-red-500' : ''}
							placeholder={$t('workplaces.info.descriptionPlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>

			<div class="mt-4 flex justify-center gap-4">
				<Button
					type="button"
					class="font-titillium min-w-[120px] rounded-xl bg-[#B1B7C3] p-7 text-sm font-semibold text-white uppercase hover:bg-[#B1B7C3]/80"
					onclick={handleAttemptCancelOrNavigate}
				>
					{$t('common.buttons.back')}
				</Button>

				<Form.Button
					type="submit"
					class="bg-primary font-titillium font-titillium hover:bg-primary/80 min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				>
					{$t('common.buttons.save')}
				</Form.Button>
			</div>
		</div>
	</div>
</form>

<ConfirmDialog
	open={isSaveChangesDialogOpen}
	title={$t('common.dialogs.confirm.title')}
	description={$t('common.dialogs.confirm.text')}
	confirmButtonText={$t('common.buttons.save')}
	onClose={() => {
		isSaveChangesDialogOpen = false;
		onCancel();
	}}
	onConfirm={() => {
		isSaveChangesDialogOpen = false;
		submitForm();
	}}
/>
