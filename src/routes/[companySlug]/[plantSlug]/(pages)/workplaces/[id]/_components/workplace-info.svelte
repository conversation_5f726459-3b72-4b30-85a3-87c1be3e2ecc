<script lang="ts">
	import Badge from '$lib/components/ui/badge/badge.svelte';
	import type { WorkplaceDTO } from '$lib/DTO/workplaces/workplaces';
	import { t } from '$lib/translations';

	let { workplace }: { workplace: WorkplaceDTO } = $props();

	const responsiblePersonName = $state(() => {
		return workplace.responsiblePerson?.firstName + ' ' + workplace.responsiblePerson?.lastName;
	});
</script>

<div class="my-6 grid grid-cols-2 gap-4 text-base sm:flex sm:flex-col sm:gap-10">
	{@render workplaceInfo($t('workplaces.info.code'), workplace.code)}
	{@render workplaceInfo($t('workplaces.info.responsiblePerson'), responsiblePersonName())}
	{@render workplaceInfo(
		$t('workplaces.info.eKaizenWorkstationId'),
		workplace.eKaizenWorkstationId ?? 'N/A'
	)}

	<div class="flex flex-col overflow-hidden text-left">
		<span class="-py-0.5 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
			>{$t('workplaces.info.status')}</span
		>
		<span class="truncate text-sm lg:text-lg">
			<Badge
				class={`font-titillium rounded-md px-2 py-0.5 text-xs font-light text-white uppercase lg:px-6 lg:py-1 lg:text-xs ${
					workplace.active ? 'bg-[#414E6B] ' : 'bg-secondary '
				}`}
			>
				{workplace.active ? $t('workplaces.info.active') : $t('workplaces.info.inactive')}
			</Badge>
		</span>
	</div>
</div>

<hr class="my-2 mt-6 border-2 border-[#F3F3F3]" />

{#if workplace.description != null}
	<div class="py-2">
		<div class="flex flex-col overflow-hidden text-left">
			<span class="my-2 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
				>{$t('workplaces.info.description')}</span
			>
			<span class="text-sm break-words text-[#4B505A] lg:text-lg">{workplace.description}</span>
		</div>
	</div>
{/if}

{#snippet workplaceInfo(name: string, content: string, additionalContentClass: string = '')}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span
			class={`-mt-0.5 truncate text-sm text-[#4B505A] lg:-mt-1 lg:text-base ${additionalContentClass}`}
			>{content}</span
		>
	</div>
{/snippet}
