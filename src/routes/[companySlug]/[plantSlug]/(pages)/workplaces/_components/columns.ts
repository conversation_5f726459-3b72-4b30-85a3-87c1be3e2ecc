import { renderComponent } from '$lib/components/ui/data-table';
import { t } from '$lib/translations';
import { get } from 'svelte/store';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import DataTableActions from './data-table-actions.svelte';
import AuditorCell from '$lib/customComponents/tableFormat/auditor-cell.svelte';
import type { ColumnDef } from '@tanstack/table-core';
import type { WorkplaceDTO } from '$lib/DTO/workplaces/workplaces';

export const columns: ColumnDef<WorkplaceDTO>[] = [
	{
		accessorKey: 'name',
		header: ({ column }) =>
			renderComponent(CustomHeader, {
				text: get(t)('workplaces.list.name'),
				column
			}),
		cell: ({ row }) => {
			const name = row.getValue('name') as string;
			return renderComponent(Cell, {
				content: name
			});
		},
		enableSorting: true
	},
	{
		accessorKey: 'eKaizenWorkstationId',
		header: ({ column }) =>
			renderComponent(CustomHeader, {
				text: get(t)('workplaces.list.eKaizenId'),
				column
			}),
		cell: ({ row }) => {
			const id = row.getValue('eKaizenWorkstationId') as string;
			return renderComponent(Cell, {
				content: id
			});
		},
		enableSorting: true
	},
	{
		id: 'responsiblePerson',
		accessorFn: (row) => {
			const person = row.responsiblePerson;
			if (!person) return '';
			return `${person.firstName || ''} ${person.lastName || ''}`.trim();
		},
		header: ({ column }) =>
			renderComponent(CustomHeader, {
				text: get(t)('workplaces.list.responsiblePerson'),
				column
			}),
		cell: ({ row }) => {
			const responsiblePerson = row.original.responsiblePerson;
			if (!responsiblePerson) return null;

			return renderComponent(AuditorCell, {
				firstName: responsiblePerson.firstName,
				lastName: responsiblePerson.lastName
			});
		},
		enableSorting: true
	},
	{
		id: 'actions',
		enableSorting: false,
		enableHiding: false,
		meta: {
			headerClass: 'text-right',
			cellClass: 'text-right'
		},
		cell: ({ row }) =>
			renderComponent(DataTableActions, {
				id: row.original.id,
				name: row.original.name
			})
	}
];
