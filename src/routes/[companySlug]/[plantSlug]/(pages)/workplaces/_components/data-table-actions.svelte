<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { <PERSON><PERSON>sis, <PERSON>, Pencil, Trash } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import { goto } from '$app/navigation';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/state';

	let { id, name } = $props<{
		id: string;
		name: string;
	}>();

	let open = $state(false);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase">
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() =>
					goto(`/${page.params.companySlug}/${page.params.plantSlug}/workplaces/${id}`)}
			>
				<Eye class="size-4 text-white" />
				<span>{$t('common.buttons.details')}</span>
			</DropdownMenu.Item>

			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => (open = true)}
			>
				<Trash class="size-4 text-white" />
				<span>{$t('common.buttons.delete')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	{id}
	title={$t('workplaces.delete.deleteWorkplaceTitle')}
	description={$t('workplaces.delete.deleteWorkplaceDescription')}
	code={name}
	formAction="?/deleteWorkplace"
	onDelete={() => toast.success($t('workplaces.messages.workplaceDeleted'))}
	onClose={() => (open = false)}
/>
