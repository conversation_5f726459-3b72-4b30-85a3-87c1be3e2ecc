<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import type { WorkplaceDTO } from '$lib/DTO/workplaces/workplaces';
	import type { AuditorDTO } from '$lib/DTO/auditor';

	let {
		form,
		onClose,
		workplace,
		auditors
	}: {
		form: any;
		onClose: () => void;
		workplace: WorkplaceDTO | null;
		auditors: AuditorDTO[];
	} = $props();

	const { form: formData, enhance, errors } = form;
	let responsiblePersonOpen = $state(false);
</script>

<form method="POST" action={'?/createWorkplace'} use:enhance class={`space-y-3`}>
	{#if workplace}
		<input type="hidden" name="id" value={workplace.id} class="hidden" />
	{/if}

	<div class="relative pb-4">
		<Form.Field {form} name="name">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('workplaces.form.name')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.name}
						class={$errors.name ? 'border-red-500' : ''}
						placeholder={$t('workplaces.form.namePlaceholder')}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="absolute right-0 -bottom-1 text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="relative pb-4">
		<Form.Field {form} name="code">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('workplaces.form.code')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.code}
						class={$errors.code ? 'border-red-500' : ''}
						placeholder={$t('workplaces.form.codePlaceholder')}
						maxlength={10}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="absolute right-0 -bottom-1 text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="relative pb-4">
		<Form.Field {form} name="eKaizenWorkstationId">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('workplaces.form.eKaizenId')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.eKaizenWorkstationId}
						class={$errors.eKaizenWorkstationId ? 'border-red-500' : ''}
						placeholder={$t('workplaces.form.eKaizenIdPlaceholder')}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="absolute right-0 -bottom-1 text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="relative pb-4">
		<Form.Field {form} name="responsiblePersonId">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('audits.newInstance.responsiblePersonLabel')}</Form.Label>
					<Popover.Root bind:open={responsiblePersonOpen}>
						<Popover.Trigger
							class={cn(
								'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
								'items-center justify-between',
								!$formData.responsiblePersonId && 'text-muted-foreground',
								$errors.responsiblePersonId && 'border-red-500'
							)}
							role="combobox"
							{...props}
						>
							{#if $formData.responsiblePersonId && auditors}
								{(() => {
									const auditor = auditors.find(
										(a: AuditorDTO) => a.id === $formData.responsiblePersonId
									);
									return auditor
										? `${auditor.name.firstName || ''} ${auditor.name.lastName || ''}`
										: $t('audits.newInstance.responsiblePersonPlaceholder');
								})()}
							{:else}
								{$t('audits.newInstance.responsiblePersonPlaceholder')}
							{/if}
							<ChevronsUpDown class="h-4 w-4 opacity-50" />
						</Popover.Trigger>
						<input hidden value={$formData.responsiblePersonId} name={props.name} class="hidden" />
						<Popover.Content
							class="w-(--bits-popover-anchor-width) p-0"
							align="start"
							sideOffset={5}
						>
							<Command.Root class="w-full">
								<Command.Input
									autofocus
									placeholder={$t('audits.newInstance.responsiblePersonPlaceholder')}
									class="h-9"
								/>
								<Command.List>
									<Command.Empty>{$t('audits.newInstance.noAuditorFound')}</Command.Empty>
									<Command.Group>
										{#each auditors as auditor (auditor.id)}
											<Command.Item
												value={`${auditor.name.firstName || ''} ${auditor.name.lastName || ''}`}
												onSelect={() => {
													$formData.responsiblePersonId = auditor.id;
													responsiblePersonOpen = false;
												}}
											>
												<span>{auditor.name.firstName || ''} {auditor.name.lastName || ''}</span>
												{#if auditor.id === $formData.responsiblePersonId}
													<Check class="ml-auto h-4 w-4" />
												{:else}
													<div class="ml-auto h-4 w-4"></div>
												{/if}
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.List>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="absolute right-0 -bottom-1 text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="flex justify-end gap-4 pt-1">
		<Button
			type="button"
			class="font-titillium min-w-[120px] rounded-xl bg-[#B1B7C3] p-7 text-sm font-semibold text-white uppercase hover:bg-[#B1B7C3]/80"
			onclick={onClose}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{$t('common.buttons.create')}
		</Button>
	</div>
</form>
