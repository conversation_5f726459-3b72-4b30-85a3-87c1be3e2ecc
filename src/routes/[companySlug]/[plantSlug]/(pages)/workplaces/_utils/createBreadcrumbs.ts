import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createWorkplaceBreadcrumbs(
	workplaceId?: string,
	workplaceName?: string,
	companySlug?: string,
	plantSlug?: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		}
	];

	if (workplaceId) {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'common.navigation.workplaces',
				href: `/${companySlug}/${plantSlug}/workplaces`
			},
			{
				kind: 'dynamic',
				label: workplaceName || workplaceId
			}
		);
	} else {
		breadcrumbs.push({
			kind: 'static',
			label: 'common.navigation.workplaces'
		});
	}

	return breadcrumbs;
}
