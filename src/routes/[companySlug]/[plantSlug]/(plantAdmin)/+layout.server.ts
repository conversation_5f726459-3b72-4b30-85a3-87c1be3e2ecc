import { superValidate } from 'sveltekit-superforms';
import type { LayoutServerLoad } from './$types';
import { createAuditSchema } from '$lib/schemas/audits/audits';
import { zod } from 'sveltekit-superforms/adapters';
import { changePasswordSchema } from '$lib/schemas/auth';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { loadLayoutData } from '$lib/server/services/layout/layoutData';
import { redirect } from '@sveltejs/kit';

export const load: LayoutServerLoad = async ({ locals, params, parent }) => {
	const { role } = await parent();

	const isAuthorized =
		locals.authorization?.isSuperAdmin(role!.name) ||
		locals.authorization?.can('plantSettings', '*') ||
		locals.authorization?.can('plantSettings', 'viewPlantSettings');

	if (!locals.user || !isAuthorized) {
		throw redirect(302, `/${params.companySlug}/${params.plantSlug}/dashboard`);
	}
	const { plantId, companyId } = await validatePlantCompanyAccessLight(
		params.companySlug!,
		params.plantSlug!,
		locals.user!
	);

	const [layoutData, instanceForm, changePasswordForm] = await Promise.all([
		loadLayoutData(plantId),
		superValidate(zod(createAuditSchema)),
		superValidate(zod(changePasswordSchema))
	]);

	return {
		user: locals.user,
		role,
		context: { plantId, companyId, plantSlug: params.plantSlug!, companySlug: params.companySlug! },
		createAuditData: {
			auditTypes: layoutData.auditTypes,
			workplaces: layoutData.workplaces,
			auditors: layoutData.auditors,
			instanceForm
		},
		availableLanguages: layoutData.supportedLanguages,
		plantDefaultLanguage: locals.plantDefaultLanguage,
		changePasswordForm
	};
};
