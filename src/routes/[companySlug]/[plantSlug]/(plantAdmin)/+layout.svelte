<script lang="ts">
	import MainContainer from '$lib/customComponents/main-container.svelte';
	import TopBar from '$lib/customComponents/top-bar.svelte';
	import AdminSidebar from './_components/admin-sidebar.svelte';

	import '../../../../app.css';
	import AdminMobileMenu from './_components/admin-mobile-menu.svelte';
	let { data, children } = $props();
</script>

<div class="flex h-screen bg-[#101826] md:bg-transparent">
	<div class="hidden md:block">
		<AdminSidebar />
	</div>

	<MainContainer class="flex w-full flex-col">
		<div class="hidden md:block">
			<TopBar
				createAuditData={data.createAuditData}
				changePasswordForm={data.changePasswordForm}
				languages={data.availableLanguages}
			/>
		</div>

		<AdminMobileMenu />

		{@render children()}
	</MainContainer>
</div>
