<script lang="ts">
	import * as Sheet from '$lib/components/ui/sheet';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import { page } from '$app/state';
	import {
		ShieldCheck,
		Menu,
		Languages,
		LogOut,
		SlidersHorizontal,
		Tags,
		Settings,
		Undo
	} from '@lucide/svelte';
	import { locale, locales } from '$lib/translations';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { loadTranslations } from '$lib/translations';
	import logo from '$lib/assets/logo.png';
	import logo_mobile from '$lib/assets/Logo_mobile.png';

	const pages = $derived([
		{
			name: $t('plantAdmin.navigation.returnToApp'),
			icon: Undo,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/dashboard`
		},
		{
			name: $t('plantAdmin.navigation.general'),
			icon: Settings,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/general`
		},
		{
			name: $t('plantAdmin.navigation.evaluationTypes'),
			icon: SlidersHorizontal,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/evaluationTypes`
		},
		{
			name: $t('plantAdmin.navigation.auditTypes'),
			icon: ShieldCheck,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/auditTypes`
		},
		{
			name: $t('plantAdmin.navigation.tags'),
			icon: Tags,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/tags`
		}
	]);

	let path = $derived(page.url.pathname);

	function isActive(itemPath: string): boolean {
		return path === itemPath || path.startsWith(itemPath + '/');
	}

	const switchLanguage = async (newLocale: string) => {
		if (browser) {
			// Set cookie for server-side detection
			document.cookie = `language=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}`;
			// Keep localStorage for backward compatibility
			localStorage.setItem('language', newLocale);
			await goto(page.url.pathname, {
				invalidateAll: true,
				replaceState: true
			});
		}
	};

	let isOpen = $state(false);
</script>

<div class="flex items-center justify-between p-4 md:hidden">
	<div class="w-10"></div>

	<div class="flex justify-center">
		<img src={logo_mobile} alt="Lean Audit" class="h-16" />
	</div>

	<Sheet.Root bind:open={isOpen}>
		<Sheet.Trigger
			class="flex h-12 w-12 items-center justify-center rounded-lg bg-[#7D9AD3]/15 text-white shadow-md hover:bg-[#7D9AD3]/20"
		>
			<Menu class="h-6 w-6" />
		</Sheet.Trigger>

		<Sheet.Content
			side="bottom"
			class="mx-auto flex h-auto min-h-[60vh] w-full max-w-md flex-col rounded-t-2xl bg-[#101826] p-0"
		>
			<!-- Logo -->
			<div class="flex h-16 items-center justify-center border-b border-white/10">
				<img src={logo} alt="Logo" class="h-8" />
			</div>

			<!-- Main Navigation -->
			<nav class="flex-1 overflow-y-auto px-4 py-8">
				<div class="space-y-2">
					{#each pages as item}
						<a
							href={item.path}
							class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-white transition-colors hover:bg-white/10 {isActive(
								item.path
							)
								? 'bg-white/20'
								: ''}"
							onclick={() => (isOpen = false)}
						>
							<item.icon size={20} />
							<span>{item.name}</span>
						</a>
					{/each}
				</div>
			</nav>

			<!-- Bottom Actions -->
			<div class="border-t border-white/10 p-4">
				<!-- Language Selector -->
				<DropdownMenu.Root>
					<DropdownMenu.Trigger>
						<Button
							variant="ghost"
							class="flex w-full items-center justify-between gap-2 px-3 py-2 text-sm text-white hover:bg-white/10"
						>
							<div class="flex items-center gap-3">
								<Languages size={20} />
								<span>
									{$locale === 'cs' ? $t('common.languages.cs') : $t('common.languages.en')}
								</span>
							</div>
						</Button>
					</DropdownMenu.Trigger>
					<DropdownMenu.Content>
						{#each $locales as lang}
							<DropdownMenu.Item class="cursor-pointer" onclick={() => switchLanguage(lang)}>
								<span class:font-bold={$locale === lang}>
									{lang === 'cs' ? $t('common.languages.cs') : $t('common.languages.en')}
								</span>
								{#if $locale === lang}
									<DropdownMenu.Shortcut>✓</DropdownMenu.Shortcut>
								{/if}
							</DropdownMenu.Item>
						{/each}
					</DropdownMenu.Content>
				</DropdownMenu.Root>

				<!-- User Info & Logout -->
				<div class="mt-2 space-y-2">
					<div class="px-3 py-2 text-sm text-white">
						{page.data.user?.firstName}
						{page.data.user?.lastName}
					</div>
					<form action="/logout" method="POST">
						<Button
							type="submit"
							variant="ghost"
							class="flex w-full items-center gap-3 px-3 py-2 text-sm text-white hover:bg-white/10"
						>
							<LogOut size={20} />
							<span>{$t('common.logout')}</span>
						</Button>
					</form>
				</div>
			</div>
		</Sheet.Content>
	</Sheet.Root>
</div>
