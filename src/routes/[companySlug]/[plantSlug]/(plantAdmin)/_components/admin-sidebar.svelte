<script lang="ts">
	import { t } from '$lib/translations';
	import { page } from '$app/state';
	import { Settings, ShieldCheck, SlidersHorizontal, Tags, Undo } from '@lucide/svelte';
	import { Menu } from '@lucide/svelte';
	import logo from '$lib/assets/logo.png';

	const adminPages = $derived([
		{
			name: $t('plantAdmin.navigation.returnToApp'),
			icon: Undo,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/dashboard`
		},
		{
			name: $t('plantAdmin.navigation.general'),
			icon: Settings,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/general`
		},
		{
			name: $t('plantAdmin.navigation.evaluationTypes'),
			icon: SlidersHorizontal,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/evaluationTypes`
		},
		{
			name: $t('plantAdmin.navigation.auditTypes'),
			icon: Shield<PERSON>he<PERSON>,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/auditTypes`
		},
		{
			name: $t('plantAdmin.navigation.tags'),
			icon: Tags,
			path: `/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/tags`
		}
	]);

	let menuOpen = $state(true);
	let path = $derived(page.url.pathname);

	function isActive(itemPath: string): boolean {
		return path === itemPath || path.startsWith(itemPath + '/');
	}
</script>

<div class="flex h-screen flex-col">
	<aside
		class={`sticky top-0 flex h-full flex-col overflow-y-auto bg-[#101826] px-[15px] transition-all duration-300 ${
			menuOpen ? 'w-[120px]' : 'w-[75px]'
		}`}
	>
		<div class="mt-3 flex justify-center">
			<img src={logo} alt="LeanAudit.app logo" />
		</div>

		<div class="flex flex-1 flex-col justify-center">
			<div class="font-exo2">
				{#each adminPages as item (item.name)}
					<a
						href={item.path}
						class="my-3 flex aspect-square w-full flex-col items-center justify-center rounded-xl p-1 text-[0.7rem] text-white transition-all {isActive(
							item.path
						)
							? ' bg-[#7D9AD3]/25'
							: 'bg-opacity-25 hover:bg-gray-800'}"
					>
						<div class="flex h-full w-full flex-col items-center justify-center py-2">
							{#if item.icon}
								<item.icon
									size={32}
									class={`${isActive(item.path) ? 'text-white' : 'text-gray-300'}`}
								/>
							{/if}
							<div
								class="mt-1 text-center leading-tight break-words transition-all duration-300 {menuOpen
									? 'max-h-[40px] opacity-100'
									: 'max-h-0 opacity-0'}"
							>
								{item.name.toUpperCase()}
							</div>
						</div>
					</a>
				{/each}
			</div>
		</div>

		<div class="mt-auto pb-4">
			<button
				class="flex w-full cursor-pointer items-center justify-center rounded-xl p-2 text-white"
				onclick={() => (menuOpen = !menuOpen)}
			>
				<Menu size={32} />
			</button>
		</div>
	</aside>
</div>
