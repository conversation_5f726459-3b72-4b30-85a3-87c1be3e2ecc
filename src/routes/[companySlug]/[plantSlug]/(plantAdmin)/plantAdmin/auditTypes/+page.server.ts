import { fail, type Actions } from '@sveltejs/kit';
import { AuditTypeService } from '$lib/server/services/auditTypes';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { createAuditTypeSchema, duplicateAuditTypeSchema } from '$lib/schemas/auditTypes';
import { t } from '$lib/translations';
import { AuditService } from '$lib/server/services/audits';
import { createAuditTypesBreadcrumbs } from './_utils/createBreadcrumbs';
import type { PageServerLoad } from './$types';
import { validatePlantCompanyAccess } from '$lib/server/utils/plantValidation';

export const load: PageServerLoad = async ({ parent, params }) => {
	const form = await superValidate(zod(createAuditTypeSchema));

	// Get context from parent layout
	const { context } = await parent();

	const [auditTypes, auditors] = await Promise.all([
		AuditTypeService.getAuditTypes(context.plantId, false),
		AuditService.getAuditors(context.plantId)
	]);

	return {
		auditTypes,
		auditors,
		form,
		breadcrumbs: createAuditTypesBreadcrumbs(
			undefined,
			undefined,
			params.companySlug,
			params.plantSlug
		)
	};
};

export const actions: Actions = {
	createAuditType: async ({ params, request, locals }) => {
		const form = await superValidate(request, zod(createAuditTypeSchema));

		const context = await validatePlantCompanyAccess(
			params.companySlug!,
			params.plantSlug!,
			locals.user!,
			locals.plantRole
		);

		if (!form.valid) {
			return fail(400, { form });
		}

		if (!locals.user?.id) {
			return fail(401, {
				form,
				message: t.get('errors.auth.notAuthenticated')
			});
		}

		try {
			await AuditTypeService.createAuditType(
				{
					name: form.data.name,
					code: form.data.code,
					responsiblePersonId: form.data.responsiblePersonId,
					repetetionPlan: form.data.repetetionPlan
				},
				context.plantId
			);

			return { form };
		} catch (error) {
			const message =
				error instanceof Error && error.message.includes('audit_types_code_unique')
					? t.get('errors.auditTypes.codeExists')
					: error instanceof Error
						? error.message
						: t.get('errors.common.unknownErr');

			return fail(500, { form, message });
		}
	},

	deleteAuditType: async ({ request }) => {
		const formData = await request.formData();
		const id = formData.get('id')?.toString();

		if (!id) {
			return fail(400, {
				message: t.get('errors.audits.auditSetup.noAuditSelected')
			});
		}
		try {
			await AuditTypeService.deleteAuditType(id);
			return { message: t.get('errors.audits.auditSetup.auditDeleted') };
		} catch {
			return fail(500, {
				message: t.get('errors.audits.auditSetup.failedDeleteInst')
			});
		}
	},

	duplicateAuditType: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(duplicateAuditTypeSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const context = await validatePlantCompanyAccess(
			params.companySlug!,
			params.plantSlug!,
			locals.user!,
			locals.plantRole
		);

		try {
			await AuditTypeService.duplicateAuditType(
				form.data,
				form.data.originalAuditTypeId,
				context.plantId
			);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to duplicate audit type:', error);
			return fail(500, {
				form,
				message: t.get('errors.auditTypes.failedToDuplicate')
			});
		}
	}
};
