<script lang="ts">
	import { fade } from 'svelte/transition';
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import { createColumns } from './_components/columns';
	import { Plus } from '@lucide/svelte';
	import { t } from '$lib/translations';
	import { type SuperValidated } from 'sveltekit-superforms';
	import {
		createAuditTypeSchema,
		duplicateAuditTypeSchema,
		type AuditTypeForm,
		type CreateAuditTypeForm
	} from '$lib/schemas/auditTypes';

	import { toast } from 'svelte-sonner';
	import { useForm } from '$lib/hooks/superformValidation';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import type { AuditorDTO } from '$lib/DTO/auditor';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import FormTemplateNew from './_components/form-template-new.svelte';
	import type { AuditTypeDTO } from '$lib/DTO/auditTypes/auditTypes';
	import FormTemplateDuplicate from './_components/form-template-duplicate.svelte';

	let {
		data
	}: {
		data: {
			form: SuperValidated<CreateAuditTypeForm>;
			auditTypes: AuditTypeDTO[];
			auditors: AuditorDTO[];
		};
	} = $props();

	let dialogOpen = $state(false);
	let dialogDuplicateOpen = $state(false);
	let globalFilter = $state('');
	let formTemplateNewRef = $state<{ attemptCancelOrNavigate?: () => void }>();

	let selectedTemplate = $state<AuditTypeDTO | undefined>(undefined);

	function handleSearch(value: string) {
		globalFilter = value;
	}

	function closeDialog() {
		dialogOpen = false;
		form.reset();
	}

	function closeDuplicateDialog() {
		dialogDuplicateOpen = false;
		setTimeout(() => {
			selectedTemplate = undefined;
			form.reset();
		}, 200);
	}

	function openDialog() {
		form.reset();
		dialogOpen = true;
	}

	const form = useForm(data.form, createAuditTypeSchema, 'form', () => {
		if (dialogOpen) {
			dialogOpen = false;
		} else if (dialogDuplicateOpen) {
			dialogDuplicateOpen = false;
			selectedTemplate = undefined;
		}
		toast.success($t('auditTypes.newTemplate.success'));
	});

	const duplicateForm = useForm(data.form, duplicateAuditTypeSchema, 'form', () => {
		if (dialogOpen) {
			dialogOpen = false;
		} else if (dialogDuplicateOpen) {
			dialogDuplicateOpen = false;
			selectedTemplate = undefined;
		}
		toast.success($t('auditTypes.newTemplate.success'));
	});

	function handleDuplicateQuestion(id: string) {
		selectedTemplate = data.auditTypes.find((at: AuditTypeDTO) => at.id === id) ?? undefined;
		dialogDuplicateOpen = true;
	}

	const columnsWithCallbacks = createColumns({
		onDuplicate: handleDuplicateQuestion
	});
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.auditTypes')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9"
>
	<div class="mb-6 flex items-center justify-between">
		<div class="flex-1">
			<div class="relative flex max-w-sm">
				<SearchInput value={globalFilter} onSearch={handleSearch} />
			</div>
		</div>
		<div class="ml-4">
			<ButtonIcon onClickAction={openDialog} Icon={Plus} />
		</div>
	</div>

	<div class="flex-1 overflow-hidden">
		<DataTable data={data.auditTypes} columns={columnsWithCallbacks} {globalFilter} />
	</div>
</div>

<CustomDialog open={dialogOpen} title={$t('auditTypes.newTemplate.name')} onClose={closeDialog}>
	<FormTemplateNew
		bind:this={formTemplateNewRef}
		{form}
		auditors={data.auditors}
		onClose={closeDialog}
	/>
</CustomDialog>

<CustomDialog
	open={dialogDuplicateOpen}
	title={$t('auditTypes.duplicateTemplate.name')}
	onClose={closeDuplicateDialog}
>
	<FormTemplateDuplicate
		form={duplicateForm}
		{selectedTemplate}
		auditors={data.auditors}
		onClose={closeDuplicateDialog}
	/>
</CustomDialog>
