import { error, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { AuditTypeService } from '$lib/server/services/auditTypes';
import { fail, superValidate } from 'sveltekit-superforms';
import { auditTypeCategorySchema, templateInfoSchema } from '$lib/schemas/auditTypes';
import { evaluationConfigSchema } from '$lib/schemas/evaluationConfig';
import { zod } from 'sveltekit-superforms/adapters';
import { t } from '$lib/translations';
import { AuditService } from '$lib/server/services/audits';
import {
	templateQuestionSchema,
	type TemplateQuestion,
	type TemplateQuestions
} from '$lib/schemas/audits/auditQuestions';
import { QuestionsService } from '$lib/server/services/questions';
import { minutesToHHMM } from '$lib/utils/time';
import { AuditRepetition } from '$lib/enums/audits';
import { createAuditTypesBreadcrumbs } from '../_utils/createBreadcrumbs';
import { questionSchema } from '$lib/schemas/audits/questions';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { SettingsAuditTypeEvalConfig } from '$lib/server/services/settings';

export const load: PageServerLoad = async ({ params, parent }) => {
	try {
		const { context } = await parent();

		const [auditType, auditors, questions, questionForm, categoryForm, evaluationConfig] =
			await Promise.all([
				AuditTypeService.getAuditTypeById(params.id),
				AuditService.getAuditors(context.plantId),
				QuestionsService.getQuestions(context.plantId),
				superValidate(zod(templateQuestionSchema)),
				superValidate(zod(auditTypeCategorySchema)),
				SettingsAuditTypeEvalConfig.getAuditTypeEvaluationConfig(params.id, context.plantId)
			]);

		if (!auditType) {
			throw error(404, 'Audit type not found');
		}

		const categories = Object.keys(auditType.questions || {}).sort((a, b) => {
			const orderA = auditType.questions![a]?.order ?? Number.MAX_SAFE_INTEGER;
			const orderB = auditType.questions![b]?.order ?? Number.MAX_SAFE_INTEGER;
			return orderA - orderB;
		});

		const templateInfoData = {
			id: auditType.id,
			name: auditType.name,
			code: auditType.code,
			specification: auditType.specification,
			evaluationMode: evaluationConfig?.evaluationMode || 'percentage',
			responsiblePersonId: auditType.responsiblePerson?.id || '',
			expectedDuration: minutesToHHMM(auditType.expectedDuration),
			repetetionPlan: auditType.repetetionPlan as keyof typeof AuditRepetition,
			active: auditType.active ?? true
		};
		const templateInfoForm = await superValidate(templateInfoData, zod(templateInfoSchema));

		const evaluationConfigForm = await superValidate(evaluationConfig, zod(evaluationConfigSchema));

		return {
			auditType,
			categories,
			auditors,
			questions,
			questionForm,
			templateInfoForm,
			categoryForm,
			evaluationConfigForm,
			breadcrumbs: createAuditTypesBreadcrumbs(
				auditType.code,
				auditType.name,
				context.companySlug,
				context.plantSlug
			)
		};
	} catch (e) {
		console.error('Failed to load audit type:', e);
		throw error(500, 'Failed to load audit type');
	}
};

export const actions: Actions = {
	updateTemplateInfo: async ({ request, params }) => {
		const form = await superValidate(request, zod(templateInfoSchema));
		if (!form.valid) {
			return fail(400, { form });
		}
		try {
			await AuditTypeService.updateAuditType(params.id!, form.data);
			return { form, success: true };
		} catch (error) {
			if (error instanceof Error) {
				console.error('Failed to update template info: ', error.message);
			} else {
				console.error('Failed to update template info: ', error);
			}
			return fail(500, {
				form,
				message: t.get('errors.audits.auditSetup.failedUpdateTemplateInfo')
			});
		}
	},

	createCategory: async ({ request, params }) => {
		const form = await superValidate(request, zod(auditTypeCategorySchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const categoryKey = form.data.subtitle
			? `${form.data.title}: ${form.data.subtitle}`
			: form.data.title;

		try {
			await AuditTypeService.addCategory(params.id!, categoryKey);
			return { form, success: true };
		} catch (error) {
			if (error instanceof Error) {
				console.error('Failed to add category: ', error.message);
			} else {
				console.error('Failed to add category: ', error);
			}
			return fail(500, { form, message: t.get('errors.audits.auditSetup.failedAddCategory') });
		}
	},

	editCategory: async ({ request, params }) => {
		const formData = await request.formData();
		const categoryId = formData.get('categoryId')?.toString();

		if (!categoryId) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		const form = await superValidate(formData, zod(auditTypeCategorySchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const categoryName = form.data.subtitle
			? `${form.data.title}: ${form.data.subtitle}`
			: form.data.title;

		try {
			await AuditTypeService.editCategory(params.id!, categoryId, categoryName);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to edit category:', error);
			return fail(422, { message: t.get('errors.audits.auditSetup.failedEditCategory') });
		}
	},

	deleteCategory: async ({ request, params }) => {
		const formData = await request.formData();
		const categoryId = formData.get('id')?.toString();

		if (!categoryId) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			await AuditTypeService.deleteCategory(params.id!, categoryId);
			return { success: true, message: t.get('errors.audits.auditSetup.categoryDeleted') };
		} catch (error) {
			console.error('Failed to delete category:', error);
			return fail(422, { message: t.get('errors.audits.auditSetup.failedDeleteCategory') });
		}
	},

	//Questions
	addQuestion: async ({ request, params }) => {
		const formData = await request.formData();
		const category = formData.get('category')?.toString();

		if (!category) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		const form = await superValidate(formData, zod(templateQuestionSchema));

		if (!form.valid || !form.data.questionId) {
			return fail(400, { form });
		}

		try {
			await AuditTypeService.addQuestion(params.id!, category, {
				...form.data,
				questionId: form.data.questionId
			});
			return { form };
		} catch (error) {
			console.error('Failed to add question:', error);
			return fail(422, { form, message: t.get('errors.audits.auditSetup.failedAddQ') });
		}
	},

	editQuestion: async ({ request, params }) => {
		const formData = await request.formData();
		const questionId = formData.get('id')?.toString();

		if (!questionId) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		const form = await superValidate(formData, zod(templateQuestionSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await AuditTypeService.editQuestion(params.id!, questionId, form.data);
			return { form };
		} catch (error) {
			console.error('Failed to edit question:', error);
			return fail(422, { form, message: t.get('errors.audits.auditSetup.failedEditQ') });
		}
	},

	deleteQuestion: async ({ request, params }) => {
		const data = await request.formData();
		const questionId = data.get('id')?.toString();
		if (!questionId) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			await AuditTypeService.deleteQuestion(params.id!, questionId);
			return { message: t.get('errors.audits.auditSetup.questionDeleted') };
		} catch (error) {
			console.error('Failed to delete question:', error);
			return fail(422, { message: t.get('errors.audits.auditSetup.failedDeleteQ') });
		}
	},

	createAndAddQuestion: async ({ request, params, locals }) => {
		const formData = await request.formData();
		const category = formData.get('category')?.toString();
		const context = await validatePlantCompanyAccessLight(
			params.companySlug!,
			params.plantSlug!,
			locals.user!
		);

		if (!category) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		const form = await superValidate(formData, zod(questionSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const { tagIds, tagNames, ...questionData } = form.data;
			const question = await QuestionsService.createQuestionWithTagsAndNames(
				questionData,
				tagIds || [],
				tagNames || [],
				context.plantId
			);

			if (!question || !question.id) {
				return fail(422, { form, message: t.get('errors.audits.auditSetup.failedAddQ') });
			}

			await AuditTypeService.addQuestion(params.id!, category, {
				questionId: question.id,
				required: true
			});
			return { success: true, form };
		} catch (error) {
			console.error('Failed to create and add question:', error);
			return fail(422, { form, message: t.get('errors.audits.auditSetup.failedAddQ') });
		}
	},

	toggleQuestionRequired: async ({ request, params }) => {
		try {
			const formData = await request.formData();
			const categoryId = formData.get('categoryId')?.toString();
			const questionId = formData.get('questionId')?.toString();
			const required = formData.get('required') === 'true';

			if (!categoryId || !questionId) {
				return fail(400, { error: 'Missing required fields' });
			}

			if (!params.id) {
				throw error(400, 'Missing audit type ID');
			}
			const auditType = await AuditTypeService.getAuditTypeById(params.id);

			if (!auditType || !auditType.questions) {
				return fail(404, { error: 'Audit type not found' });
			}

			const questions = auditType.questions as TemplateQuestions;

			if (!questions[categoryId] || !questions[categoryId].questions) {
				return fail(404, { error: 'Category not found' });
			}

			const updatedQuestions = questions[categoryId].questions.map((q: TemplateQuestion) =>
				q.questionId === questionId ? { ...q, required } : q
			);

			questions[categoryId].questions = updatedQuestions;

			await AuditTypeService.editQuestion(params.id, questionId, { required });

			return { success: true };
		} catch (error) {
			console.error('Error toggling question required status:', error);
			return fail(500, {
				error: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	},

	reorderQuestions: async ({ request, params }) => {
		const formData = await request.formData();
		const categoryId = formData.get('categoryId')?.toString();
		const oldIndex = parseInt(formData.get('oldIndex')?.toString() || '-1');
		const newIndex = parseInt(formData.get('newIndex')?.toString() || '-1');

		if (!categoryId || oldIndex === -1 || newIndex === -1) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			await AuditTypeService.reorderQuestions(params.id!, categoryId, oldIndex, newIndex);
			return { success: true };
		} catch (error) {
			console.error('Failed to reorder questions:', error);
			return fail(422, { message: t.get('errors.audits.auditSetup.failedReorderQ') });
		}
	},

	reorderCategories: async ({ request, params }) => {
		const formData = await request.formData();
		const oldIndex = parseInt(formData.get('oldIndex')?.toString() || '-1');
		const newIndex = parseInt(formData.get('newIndex')?.toString() || '-1');

		if (oldIndex === -1 || newIndex === -1) {
			return fail(400, { message: t.get('errors.form.missingFields') });
		}

		try {
			await AuditTypeService.reorderCategories(params.id!, oldIndex, newIndex);
			return { success: true };
		} catch (error) {
			console.error('Failed to reorder categories:', error);
			return fail(422, { message: t.get('errors.audits.auditSetup.failedReorderCategories') });
		}
	},

	updateEvaluationConfig: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(evaluationConfigSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);
			await SettingsAuditTypeEvalConfig.saveAuditTypeEvaluationConfig(
				params.id!,
				context.plantId,
				form.data
			);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to update evaluation config:', error);
			return fail(500, {
				form,
				message: t.get('errors.evaluationConfig.failedToSave')
			});
		}
	}
};
