<script lang="ts">
	import { t } from '$lib/translations/index.js';
	import { fade, fly } from 'svelte/transition';

	import Button from '$lib/components/ui/button/button.svelte';
	import { auditTypeCategorySchema, type AuditTypeCategoryForm } from '$lib/schemas/auditTypes';
	import { evaluationConfigSchema, type EvaluationConfigForm } from '$lib/schemas/evaluationConfig';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import { Ellipsis } from '@lucide/svelte';
	import AuditTemplateInfo from '../_components/audit-template-info.svelte';
	import AuditQuestions from '../_components/audit-questions.svelte';
	import AuditTemplateForm from '../_components/audit-template-form.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import { useForm } from '$lib/hooks/superformValidation';
	import FormCategory from './_components/form-category.svelte';
	import emptyCategory from '$lib/assets/emptyCategory.png';

	import * as Tabs from '$lib/components/ui/tabs/index';
	import { toast } from 'svelte-sonner';
	import ThresholdsTab from './_components/thresholds-tab.svelte';
	import WeightsTab from './_components/weights-tab.svelte';
	import RulesTab from './_components/rules-tab.svelte';
	import AuditResultTab from './_components/audit-result-tab.svelte';

	let { data } = $props();
	let dialogOpen = $state(false);
	let settingsDialogOpen = $state(false);
	let editInfo = $state(false);
	let auditTemplateFormRef = $state<{ attemptCancelOrNavigate?: () => void }>();
	let currentEvaluationMode = $state(data.evaluationConfigForm.data.evaluationMode || 'percentage');

	const form = useForm<AuditTypeCategoryForm>(
		data.categoryForm,
		auditTypeCategorySchema,
		'form',
		() => {
			dialogOpen = false;
		}
	);

	const evaluationForm = useForm<EvaluationConfigForm>(
		data.evaluationConfigForm,
		evaluationConfigSchema,
		'form',
		() => {
			toast.success($t('evaluationConfig.saved'));
			settingsDialogOpen = false;
		},
		() => {
			toast.error($t('evaluationConfig.saveFailed'));
		},
		undefined,
		false
	);

	const { form: evalFormStore, enhance: evalEnhance } = evaluationForm;

	function handleEditInfoToggle() {
		if (editInfo && auditTemplateFormRef?.attemptCancelOrNavigate) {
			auditTemplateFormRef.attemptCancelOrNavigate();
		} else {
			editInfo = !editInfo;
		}
	}
</script>

<svelte:head>
	<title>LeanAudit - {data.auditType.name}</title>
</svelte:head>

<div in:fade={{ duration: 300 }} class="mt-4 mb-8 flex h-[calc(100vh-10rem)] w-full gap-4">
	<div class="flex h-full w-full flex-col lg:flex-row lg:gap-4">
		<div
			class="flex h-full w-full flex-col rounded-2xl bg-white p-2 lg:flex-row lg:gap-4 lg:rounded-none lg:bg-transparent"
		>
			<!-- Info panel -->
			<div
				class="w-full overflow-y-auto px-4 py-6 sm:px-10 sm:py-8 lg:h-full lg:w-[40%] lg:shrink-0 lg:rounded-2xl lg:bg-white"
			>
				<div class="flex flex-row justify-between">
					{@render auditTypeInfo($t('auditTypes.info.name'), data.auditType.name)}
					<ButtonIcon onClickAction={handleEditInfoToggle} Icon={Ellipsis} />
				</div>

				{#if !editInfo}
					<div class="" in:fly={{ duration: 300 }}>
						<AuditTemplateInfo auditType={data.auditType} />
					</div>
				{:else}
					<div class="py-4" in:fly={{ duration: 300 }}>
						<AuditTemplateForm
							bind:this={auditTemplateFormRef}
							auditType={data.auditType}
							auditors={data.auditors}
							templateInfoForm={data.templateInfoForm}
							onCancel={() => {
								editInfo = false;
							}}
							onSave={() => {
								editInfo = false;
							}}
							onSettingsOpen={(evaluationMode: string) => {
								currentEvaluationMode = evaluationMode;
								$evalFormStore.evaluationMode = evaluationMode;
								settingsDialogOpen = true;
							}}
						/>
					</div>
				{/if}
			</div>

			<!-- Questions panel  -->
			<div class="flex-1 overflow-auto p-2 sm:p-4 lg:rounded-2xl lg:bg-white lg:p-6">
				<div class="flex h-full flex-col">
					<div class="flex justify-end">
						<Button
							class="font-titillium rounded-xl bg-[#7D9AD3] p-8 text-sm font-semibold text-white uppercase hover:bg-[#7D9AD3]/80 hover:text-white [&_svg]:size-6!"
							onclick={() => {
								dialogOpen = true;
							}}
						>
							{$t('auditTypes.createNewCategory')}
						</Button>
					</div>
					<div class="flex-1">
						{#if !data.auditType.questions || Object.keys(data.auditType.questions).length === 0}
							<div class="flex h-full flex-col items-center justify-center">
								<img
									src={emptyCategory}
									alt={$t('auditTypes.noQuestionsOrCategories')}
									class="mb-4 h-24 w-24 opacity-60"
								/>
								<p class="text-center text-base leading-tight whitespace-pre-line text-[#101826]">
									{$t('auditTypes.noQuestionsOrCategories')}
								</p>
							</div>
						{:else}
							<AuditQuestions
								templateQuestions={data.auditType.questions}
								availableQuestions={data.questions
									.filter((q) => q.id !== undefined)
									.map((q) => ({
										id: q.id as string,
										text: q.text,
										subtext: q.subtext || undefined,
										evaluationType: q.evaluationType
									}))}
								categories={Object.keys(data.auditType.questions)}
								form={data.questionForm}
								auditTypeId={data.auditType.id}
							/>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

{#snippet auditTypeInfo(name: string, content: string)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span class="text-sm break-words text-[#4B505A] lg:text-lg">{content}</span>
	</div>
{/snippet}

<CustomDialog
	open={dialogOpen}
	title={$t('auditTypes.newCategory.title')}
	onClose={() => (dialogOpen = false)}
>
	<FormCategory {form} auditType={data.auditType} onClose={() => (dialogOpen = false)} />
</CustomDialog>

<CustomDialog
	open={settingsDialogOpen}
	title={$t('questions.evaluationSettings.title')}
	onClose={() => (settingsDialogOpen = false)}
	width="max-w-4xl"
>
	<form method="POST" action="?/updateEvaluationConfig" use:evalEnhance>
		<input type="hidden" name="auditTypeId" value={data.auditType.id} />
		<input type="hidden" name="evaluationMode" bind:value={$evalFormStore.evaluationMode} />

		<Tabs.Root value="thresholds">
			<Tabs.List class="flex w-full justify-between border-b">
				<Tabs.Trigger value="thresholds" class="hover:text-black">
					{$t('evaluationConfig.thresholds.title')}
				</Tabs.Trigger>
				<Tabs.Trigger value="weights" class="hover:text-black">
					{$t('evaluationConfig.weights.title')}
				</Tabs.Trigger>
				{#if currentEvaluationMode === 'rules'}
					<Tabs.Trigger value="rules" class="hover:text-black">
						{$t('evaluationConfig.rules.title')}
					</Tabs.Trigger>
				{:else if currentEvaluationMode === 'percentage'}
					<Tabs.Trigger value="auditResult" class="hover:text-black">
						{$t('evaluationConfig.auditThresholds.title')}
					</Tabs.Trigger>
				{/if}
			</Tabs.List>

			<!-- Thresholds Tab -->
			<Tabs.Content value="thresholds">
				<ThresholdsTab {evaluationForm} />
			</Tabs.Content>

			<!-- Weights Tab -->
			<Tabs.Content value="weights">
				<WeightsTab {evaluationForm} {currentEvaluationMode} />
			</Tabs.Content>

			<!-- Rules Tab  -->
			{#if currentEvaluationMode === 'rules'}
				<Tabs.Content value="rules">
					<RulesTab {evaluationForm} />
				</Tabs.Content>
			{/if}

			<!-- Audit Results Tab -->
			<Tabs.Content value="auditResult">
				<AuditResultTab {evaluationForm} />
			</Tabs.Content>
		</Tabs.Root>

		<div class="mt-6 flex justify-end gap-4 border-t pt-4">
			<Button
				type="button"
				variant="outline"
				class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				onclick={() => (settingsDialogOpen = false)}
			>
				{$t('common.buttons.cancel')}
			</Button>

			<Button
				type="submit"
				class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			>
				{$t('common.buttons.save')}
			</Button>
		</div>
	</form>
</CustomDialog>
