import { json } from '@sveltejs/kit';
import { db } from '$lib/db/db.server';
import { auditTypesTable } from '$lib/db/schema/audits';
import { eq } from 'drizzle-orm';
import { t } from '$lib/translations';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export async function PATCH({ request, params, locals }) {
	if (!locals.user?.id) {
		return json(
			{ success: false, message: t.get('errors.auth.notAuthenticated') },
			{ status: 401 }
		);
	}

	try {
		const { categoryId, questions } = await request.json();
		const auditTypeId = params.id;

		if (!categoryId || !questions || !Array.isArray(questions)) {
			return json({ success: false, message: t.get('errors.form.missingFields') }, { status: 400 });
		}

		const [auditType] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!auditType) {
			return json(
				{ success: false, message: t.get('errors.audits.auditSetup.templateNotFound') },
				{ status: 404 }
			);
		}

		const currentQuestions = { ...auditType.questions } as TemplateQuestions;

		if (!currentQuestions[categoryId]) {
			return json(
				{ success: false, message: t.get('errors.audits.auditSetup.categoryNotFound') },
				{ status: 404 }
			);
		}

		const questionsWithOrder = questions.map((question, index) => ({
			...question,
			order: index
		}));

		currentQuestions[categoryId] = {
			...currentQuestions[categoryId],
			questions: questionsWithOrder
		};

		await db
			.update(auditTypesTable)
			.set({
				questions: currentQuestions,
				updatedAt: new Date()
			})
			.where(eq(auditTypesTable.id, auditTypeId));

		return json({ success: true });
	} catch (error) {
		console.error('Error updating question order:', error);
		return json(
			{ success: false, message: t.get('errors.common.internalServerError') },
			{ status: 500 }
		);
	}
}

export async function PUT({ request, params, locals }) {
	if (!locals.user?.id) {
		return json(
			{ success: false, message: t.get('errors.auth.notAuthenticated') },
			{ status: 401 }
		);
	}

	try {
		const data = await request.json();
		const auditTypeId = params.id;
		const categories = data.categories;

		if (!categories || !Array.isArray(categories)) {
			return json({ success: false, message: t.get('errors.form.missingFields') }, { status: 400 });
		}

		const [auditType] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!auditType) {
			return json(
				{ success: false, message: t.get('errors.audits.auditSetup.templateNotFound') },
				{ status: 404 }
			);
		}

		const currentQuestions = { ...auditType.questions } as TemplateQuestions;

		categories.forEach((categoryId, index) => {
			if (currentQuestions[categoryId]) {
				currentQuestions[categoryId].order = index;
			}
		});

		await db
			.update(auditTypesTable)
			.set({
				questions: currentQuestions,
				updatedAt: new Date()
			})
			.where(eq(auditTypesTable.id, auditTypeId));

		return json({ success: true });
	} catch (error) {
		console.error('Error updating category order:', error);
		return json(
			{ success: false, message: t.get('errors.common.internalServerError') },
			{ status: 500 }
		);
	}
}
