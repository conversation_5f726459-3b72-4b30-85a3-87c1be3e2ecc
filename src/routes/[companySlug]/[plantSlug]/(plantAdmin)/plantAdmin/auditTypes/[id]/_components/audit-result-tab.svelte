<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';

	let { evaluationForm } = $props();

	const { form: evalFormStore, errors: evalErrors, enhance } = evaluationForm;
</script>

<div class="space-y-6 p-4">
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">{$t('evaluationConfig.auditThresholds.title')}</h3>
		<p class="text-sm text-gray-600">
			{$t('evaluationConfig.auditThresholds.description')}
		</p>

		<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
			<Form.Field form={evaluationForm} name="auditThreshold_average">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('evaluationConfig.auditThresholds.average')}</Form.Label>
						<div class="relative">
							<Input
								{...props}
								type="number"
								min="0"
								max={$evalFormStore.auditThreshold_success
									? $evalFormStore.auditThreshold_success - 1
									: 99}
								bind:value={$evalFormStore.auditThreshold_average}
								class={$evalErrors.auditThreshold_average ? 'border-red-500 pr-8' : 'pr-8'}
								placeholder={$t('evaluationConfig.auditThresholds.averagePlaceholder')}
							/>
							<span class="absolute top-1/2 right-3 -translate-y-1/2 transform text-gray-500"
								>%</span
							>
						</div>
						{#if $evalErrors.auditThreshold_average}
							<div class="mt-1 text-xs text-red-500">
								{$evalErrors.auditThreshold_average}
							</div>
						{/if}
						<Form.Description class="mt-1 text-xs text-gray-500">
							{$t('evaluationConfig.auditThresholds.averageDescription')}
						</Form.Description>
					{/snippet}
				</Form.Control>
			</Form.Field>
			<Form.Field form={evaluationForm} name="auditThreshold_success">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('evaluationConfig.auditThresholds.success')}</Form.Label>
						<div class="relative">
							<Input
								{...props}
								type="number"
								min={$evalFormStore.auditThreshold_average
									? $evalFormStore.auditThreshold_average + 1
									: 1}
								max="100"
								bind:value={$evalFormStore.auditThreshold_success}
								class={$evalErrors.auditThreshold_success ? 'border-red-500 pr-8' : 'pr-8'}
								placeholder={$t('evaluationConfig.auditThresholds.successPlaceholder')}
							/>
							<span class="absolute top-1/2 right-3 -translate-y-1/2 transform text-gray-500"
								>%</span
							>
						</div>
						{#if $evalErrors.auditThreshold_success}
							<div class="mt-1 text-xs text-red-500">
								{$evalErrors.auditThreshold_success}
							</div>
						{/if}
						<Form.Description class="mt-1 text-xs text-gray-500">
							{$t('evaluationConfig.auditThresholds.successDescription')}
						</Form.Description>
					{/snippet}
				</Form.Control>
			</Form.Field>
		</div>

		<div class="mt-6 rounded-lg bg-gray-50 p-4">
			<h4 class="mb-3 text-sm font-medium">
				{$t('evaluationConfig.auditThresholds.preview')}
			</h4>
			<div class="flex items-center space-x-2 text-xs">
				<div
					class="h-4 flex-1 rounded bg-gradient-to-r from-[#D18385] via-[#EFDF66] to-[#90DAB4]"
				></div>
			</div>
			<div class="mt-1 flex justify-between text-xs text-gray-600">
				<span>0%</span>
				<span class="text-[#d4c65b]">{$evalFormStore.auditThreshold_average || 70}%</span>
				<span class="text-[#90DAB4]">{$evalFormStore.auditThreshold_success || 90}%</span>
				<span>100%</span>
			</div>
			<div class="mt-1 flex justify-between text-xs">
				<span class="text-[#D18385]">{$t('evaluationConfig.auditThresholds.unsuccessful')}</span>
				<span class="text-[#d4c65b]">{$t('evaluationConfig.auditThresholds.averageLabel')}</span>
				<span class="text-[#90DAB4]">{$t('evaluationConfig.auditThresholds.successLabel')}</span>
			</div>
		</div>
	</div>
</div>
