<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';

	let { form, auditType, onClose } = $props();

	const { form: formData, enhance, errors } = form;
</script>

<form method="POST" action="?/createCategory" use:enhance>
	<input type="hidden" name="auditTypeId" value={auditType.id} />
	<div class="space-y-2">
		<div class="relative pb-6">
			<Form.Field {form} name="title">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.newCategory.titleLabel')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.title}
							class={$errors.title ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.newCategory.titlePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="absolute bottom-0 right-0 text-xs text-destructive" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="subtitle">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.newCategory.subtitleLabel')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.subtitle}
							class={$errors.subtitle ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.newCategory.subtitlePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="absolute bottom-0 right-0 text-xs text-destructive" />
			</Form.Field>
		</div>

		<div class="flex justify-end gap-4">
			<Button
				type="button"
				variant="outline"
				class="min-w-[120px] rounded-xl bg-secondary p-7 font-titillium text-sm font-semibold uppercase"
				onclick={() => onClose()}
			>
				{$t('common.buttons.cancel')}
			</Button>

			<Form.Button
				type="submit"
				class="min-w-[120px] rounded-xl bg-primary p-7 font-titillium text-sm font-semibold uppercase"
			>
				{$t('common.buttons.create')}
			</Form.Button>
		</div>
	</div>
</form>
