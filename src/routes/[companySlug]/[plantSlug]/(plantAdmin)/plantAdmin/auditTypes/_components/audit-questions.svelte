<script lang="ts">
	import * as Accordion from '$lib/components/ui/accordion';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import { t } from '$lib/translations';
	import { Plus, Trash, Pencil, Star, StarOff, GripVertical, Ellipsis, Edit } from '@lucide/svelte';
	import { auditTypeCategorySchema, type AuditTypeCategoryForm } from '$lib/schemas/auditTypes';
	import {
		templateQuestionSchema,
		type TemplateQuestion,
		type TemplateQuestionForm,
		type TemplateQuestions
	} from '$lib/schemas/audits/auditQuestions';
	import ButtonDataAction from '$lib/customComponents/button-data-action.svelte';
	import { useForm } from '$lib/hooks/superformValidation';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { enhance } from '$app/forms';
	import FormTemplateQuestion from './forms/form-template-question.svelte';
	import FormTemplateCategory from './forms/form-template-category.svelte';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import Sortable from 'sortablejs';
	import { onMount } from 'svelte';
	import emptyCategory from '$lib/assets/emptyCategory.png';

	// Props
	let {
		templateQuestions,
		availableQuestions,
		categories = Object.keys(templateQuestions),
		form,
		auditTypeId
	} = $props<{
		templateQuestions: TemplateQuestions;
		availableQuestions: { id: string; text: string; subtext?: string }[];
		categories?: string[];
		form: any;
		auditTypeId: string;
	}>();

	// State
	let dialogOpen = $state(false);
	let deleteDialogOpen = $state(false);
	let categoryDialogOpen = $state(false);
	let deleteCategoryDialogOpen = $state(false);
	let expandedCategories = $state<string[]>(categories[0] ? [categories[0]] : []);
	let selectedCategory = $state<string>(categories[0]);
	let editingQuestion = $state<TemplateQuestion | null>(null);
	let deletingQuestion = $state<TemplateQuestion | null>(null);
	let editingCategory = $state<{ id: string; name: string } | null>(null);
	let deletingCategory = $state<string | null>(null);

	// Form refs pro každou otázku
	let formRefs: Record<string, HTMLFormElement> = {};

	// Helpers
	function getCategoryName(categoryId: string): string {
		return templateQuestions[categoryId]?.name || categoryId;
	}

	// Forms
	const questionForm = useForm<TemplateQuestionForm>(form, templateQuestionSchema, 'form', () => {
		dialogOpen = false;
		setTimeout(() => (editingQuestion = null), 300);
	});

	const { form: questionFormData } = questionForm;

	const categoryForm = superForm<AuditTypeCategoryForm>(
		{ title: '', subtitle: '' },
		{
			validators: zodClient(auditTypeCategorySchema),
			resetForm: true,
			onResult: ({ result }) => {
				if (result.type === 'success') {
					categoryDialogOpen = false;
					editingCategory = null;
				}
			}
		}
	);

	const { form: categoryFormData, enhance: categoryEnhance, errors: categoryErrors } = categoryForm;

	// Handlers
	function handleDeleteQuestion(question: TemplateQuestion) {
		deletingQuestion = question;
		deleteDialogOpen = true;
	}

	function handleEditCategory(categoryId: string) {
		const categoryName = getCategoryName(categoryId);
		const parts = categoryName.split(': ');

		$categoryFormData = {
			title: parts[0] || '',
			subtitle: parts.length > 1 ? parts.slice(1).join(': ') : ''
		};

		editingCategory = { id: categoryId, name: categoryName };
		categoryDialogOpen = true;
	}

	function handleDeleteCategory(categoryId: string) {
		deletingCategory = categoryId;
		deleteCategoryDialogOpen = true;
	}

	function resetQuestionForm() {
		setTimeout(() => {
			editingQuestion = null;
			$questionFormData = { questionId: '', required: true };
		}, 300);
	}

	function initCategoriesSortable() {
		const categoriesContainer = document.querySelector('#categories-container');
		if (!categoriesContainer || !(categoriesContainer instanceof HTMLElement)) return;

		Sortable.create(categoriesContainer, {
			animation: 150,
			handle: '.category-handle',
			ghostClass: 'sortable-ghost',
			onEnd: async (evt) => {
				const { oldIndex, newIndex } = evt;
				if (oldIndex === undefined || newIndex === undefined || oldIndex === newIndex) return;

				try {
					const categoryElements = Array.from(categoriesContainer.children);
					const categoryOrder = categoryElements
						.map((el) => el.getAttribute('data-category-id'))
						.filter(Boolean) as string[];

					const response = await fetch(`/api/auditTypes/${auditTypeId}/reorder-categories`, {
						method: 'PUT',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({
							categoryOrder
						})
					});

					if (!response.ok) {
						toast.error($t('auditTypes.category.reorderFailed'));
					} else {
						categoryOrder.forEach((categoryId, index) => {
							if (templateQuestions[categoryId]) {
								templateQuestions[categoryId].order = categoryOrder.length - 1 - index;
							}
						});
						toast.success($t('auditTypes.category.reordered'));
					}
				} catch (error) {
					console.error('Failed to reorder categories:', error);
					toast.error($t('auditTypes.category.reorderFailed'));
				}
			}
		});
	}

	function initSortableWithGroups() {
		categories.forEach((categoryId: string) => {
			const questionsContainer = document.querySelector(`#questions-${categoryId}`);
			if (!questionsContainer || !(questionsContainer instanceof HTMLElement)) return;

			Sortable.create(questionsContainer, {
				animation: 150,
				ghostClass: 'sortable-ghost',
				group: 'questions',
				onEnd: async (evt) => {
					if (evt.from !== evt.to) {
						const { item, from, to } = evt;
						const questionId = item.getAttribute('data-question-id');
						const sourceCategoryId = from.getAttribute('data-category-id');
						const targetCategoryId = to.getAttribute('data-category-id');

						if (!questionId || !sourceCategoryId || !targetCategoryId) return;

						try {
							const response = await fetch(`/api/auditTypes/${auditTypeId}/move-question`, {
								method: 'POST',
								headers: {
									'Content-Type': 'application/json'
								},
								body: JSON.stringify({
									questionId,
									sourceCategoryId,
									targetCategoryId
								})
							});

							if (!response.ok) {
								toast.error($t('auditTypes.question.moveFailed'));
								from.appendChild(item);
							} else {
								toast.success($t('auditTypes.question.moved'));
							}
						} catch (error) {
							console.error('Failed to move question:', error);
							toast.error($t('auditTypes.question.moveFailed'));
							from.appendChild(item);
						}
					} else {
						const { oldIndex, newIndex } = evt;
						if (oldIndex === undefined || newIndex === undefined || oldIndex === newIndex) return;

						try {
							const categoryId = evt.from.getAttribute('data-category-id');
							if (!categoryId) return;

							const questions = templateQuestions[categoryId]?.questions || [];

							const updatedQuestions = [...questions];

							const [movedItem] = updatedQuestions.splice(oldIndex, 1);
							updatedQuestions.splice(newIndex, 0, movedItem);

							const response = await fetch(`/api/auditTypes/${auditTypeId}/reorder-questions`, {
								method: 'POST',
								headers: {
									'Content-Type': 'application/json'
								},
								body: JSON.stringify({
									questions: updatedQuestions,
									categoryId: categoryId
								})
							});

							if (!response.ok) {
								toast.error($t('auditTypes.question.reorderFailed'));
							} else {
								toast.success($t('auditTypes.question.reordered'));
							}
						} catch (error) {
							console.error('Failed to reorder questions:', error);
							toast.error($t('auditTypes.question.reorderFailed'));
						}
					}
				}
			});
		});
	}

	onMount(() => {
		initCategoriesSortable();
		initSortableWithGroups();
	});
</script>

<div class="mt-5 space-y-4">
	<Accordion.Root type="multiple" bind:value={expandedCategories} id="categories-container">
		{#each categories as categoryId (categoryId)}
			<Accordion.Item value={categoryId} class="my-2" data-category-id={categoryId}>
				<Accordion.Trigger
					class="group category-handle rounded-lg bg-[#EAEDF5] p-4 text-lg font-semibold"
				>
					<div class="flex w-full items-center justify-between">
						<div class="flex flex-row items-center gap-4">
							<GripVertical class="text-secondary size-5" />
							<span>{getCategoryName(categoryId)}</span>
						</div>
						<div class="invisible flex gap-2 group-hover:visible">
							<ButtonDataAction
								Icon={Pencil}
								onClickAction={(e: MouseEvent) => {
									e.stopPropagation();
									handleEditCategory(categoryId);
								}}
							/>

							<ButtonDataAction
								Icon={Trash}
								bgColor="bg-[#C01F44]"
								hoverBgColor="hover:bg-[#C01F44]/80"
								onClickAction={(e: MouseEvent) => {
									e.stopPropagation();
									handleDeleteCategory(categoryId);
								}}
							/>
						</div>
					</div>
				</Accordion.Trigger>

				<Accordion.Content>
					<div class="space-y-2 p-4">
						{#if !templateQuestions[categoryId]?.questions || templateQuestions[categoryId]?.questions.length === 0}
							<div class="flex flex-col items-center justify-center py-4">
								<img
									src={emptyCategory}
									alt={$t('auditTypes.noQuestions')}
									class="mb-4 h-24 w-24 opacity-60"
								/>
								<p class="text-center text-base leading-tight whitespace-pre-line text-[#101826]">
									{$t('auditTypes.noQuestions')}
								</p>
							</div>
						{:else}
							<div id="questions-{categoryId}" class="space-y-2" data-category-id={categoryId}>
								{#each templateQuestions[categoryId]?.questions || [] as question (question.questionId)}
									<div
										class="group cursor-move rounded-lg border border-transparent px-4 py-2 hover:border-dashed hover:border-gray-300"
										data-question-id={question.questionId}
									>
										<div
											class="flex h-10 items-center justify-between gap-2 text-sm text-[#747C8A]"
										>
											<GripVertical class="text-secondary size-3" />

											<div class="flex-1 overflow-hidden">
												<p class="line-clamp-2 leading-tight font-medium">
													{availableQuestions.find(
														(q: { id: string }) => q.id === question.questionId
													)?.text || $t('auditTypes.newQuestion.noQuestionFound')}
												</p>
												<div class="text-muted-foreground flex items-center gap-2 text-xs">
													{#if !question.required}
														<span class="text-muted-foreground"
															>• {$t('auditTypes.newQuestion.optional')}</span
														>
													{/if}
												</div>
											</div>
											<div class="ml-4 flex shrink-0 gap-2">
												<form
													method="POST"
													action="?/toggleQuestionRequired"
													bind:this={formRefs[question.questionId]}
													use:enhance={() => {
														return ({ result, update }) => {
															update({ reset: false });

															if (result.type === 'success') {
																toast.info(
																	!question.required
																		? $t('auditTypes.question.markedRequired')
																		: $t('auditTypes.question.markedOptional')
																);
															} else {
																toast.error($t('auditTypes.question.updateFailed'));
															}
														};
													}}
												>
													<input type="hidden" name="categoryId" value={categoryId} />
													<input type="hidden" name="questionId" value={question.questionId} />
													<input
														type="hidden"
														name="required"
														value={(!question.required).toString()}
													/>

													<Button
														size="icon"
														type="submit"
														class="bg-primary hover:bg-primary/80 hidden h-8 w-8 rounded-lg p-1.5 text-white hover:text-white xl:invisible xl:inline-flex xl:group-hover:visible"
														onclick={(e) => e.stopPropagation()}
													>
														{#if question.required}
															<StarOff class="h-4 w-4" />
														{:else}
															<Star class="h-4 w-4" />
														{/if}
													</Button>

													<div class="block xl:hidden">
														<DropdownMenu.Root>
															<DropdownMenu.Trigger>
																<Button class="bg-secondary size-8">
																	<Ellipsis />
																</Button>
															</DropdownMenu.Trigger>
															<DropdownMenu.Content align="end" class="bg-secondary">
																<DropdownMenu.Group class="text-white uppercase ">
																	<DropdownMenu.Item
																		class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
																		onclick={() => {
																			if (formRefs[question.questionId]) {
																				formRefs[question.questionId].requestSubmit();
																			}
																		}}
																	>
																		{#if question.required}
																			<StarOff class="text-white" />
																			<span>{$t('common.buttons.required.unrequired')}</span>
																		{:else}
																			<Star class="text-white" />
																			<span>{$t('common.buttons.required.required')}</span>
																		{/if}
																	</DropdownMenu.Item>

																	<DropdownMenu.Item
																		class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white "
																		onclick={() => handleDeleteQuestion(question)}
																	>
																		<Trash class="text-white" />
																		<span>{$t('common.buttons.delete')}</span>
																	</DropdownMenu.Item>
																</DropdownMenu.Group>
															</DropdownMenu.Content>
														</DropdownMenu.Root>
													</div>
												</form>

												<Button
													size="icon"
													class="hidden h-8 w-8 rounded-lg bg-[#C01F44] p-1.5 text-white hover:bg-[#C01F44]/80 hover:text-white xl:invisible xl:inline-flex xl:group-hover:visible"
													onclick={() => handleDeleteQuestion(question)}
												>
													<Trash class="h-4 w-4" />
												</Button>
											</div>
										</div>
									</div>
								{/each}
							</div>
						{/if}
						<div class="flex justify-center">
							<Button
								class="white flex size-12 items-center
										justify-center rounded-xl
										bg-[#7D9AD3] text-white
										hover:bg-[#7D9AD3]/80 hover:text-white [&_svg]:size-6!"
								onclick={() => {
									selectedCategory = categoryId;
									$questionFormData.required = true;
									dialogOpen = true;
								}}
							>
								<Plus class="h-4 w-4" />
							</Button>
						</div>
					</div>
				</Accordion.Content>
			</Accordion.Item>
		{/each}
	</Accordion.Root>
</div>

<!-- Dialog add/edit  -->
<CustomDialog
	open={dialogOpen}
	title={editingQuestion ? $t('auditTypes.editQuestion.title') : $t('auditTypes.newQuestion.title')}
	onClose={() => {
		dialogOpen = false;
		resetQuestionForm();
	}}
	width="max-w-2xl"
>
	<FormTemplateQuestion
		{availableQuestions}
		{selectedCategory}
		existingQuestionsInCategory={templateQuestions[selectedCategory]?.questions.map(
			(q: { questionId: string }) => q.questionId
		) || []}
		allExistingQuestions={Object.values(templateQuestions).flatMap((category) =>
			(category as { questions: { questionId: string }[] }).questions.map((q) => q.questionId)
		)}
		onCloseDialog={() => (dialogOpen = false)}
	/>
</CustomDialog>

<!-- Dialog categ edit -->
<CustomDialog
	open={categoryDialogOpen}
	title={$t('auditTypes.editCategory.title')}
	onClose={() => (categoryDialogOpen = false)}
>
	<FormTemplateCategory
		form={categoryForm}
		{editingCategory}
		onClose={() => {
			categoryDialogOpen = false;
			editingCategory = null;
		}}
	/>
</CustomDialog>

<!-- Dialog question deletion -->
<DeleteDialog
	open={deleteDialogOpen}
	id={deletingQuestion?.questionId || ''}
	title={$t('auditTypes.deleteQuestion.title')}
	description={$t('auditTypes.deleteQuestion.description')}
	formAction="?/deleteQuestion"
	onDelete={() => {
		deleteDialogOpen = false;
		toast.success($t('questions.delete.removedFromAuditType'));
		deletingQuestion = null;
	}}
	onClose={() => {
		deleteDialogOpen = false;
		deletingQuestion = null;
	}}
/>

<!-- Dialog categ deletion -->
<DeleteDialog
	open={deleteCategoryDialogOpen}
	id={deletingCategory || ''}
	title={$t('auditTypes.deleteCategory.title')}
	description={$t('auditTypes.deleteCategory.description')}
	formAction="?/deleteCategory"
	onDelete={() => {
		deleteCategoryDialogOpen = false;
		deletingCategory = null;
	}}
	onClose={() => {
		deleteCategoryDialogOpen = false;
		deletingCategory = null;
	}}
/>

<style>
	:global(.sortable-ghost) {
		opacity: 0.5;
		background: #f0f0f0;
	}

	:global(.category-handle) {
		cursor: move;
	}
</style>
