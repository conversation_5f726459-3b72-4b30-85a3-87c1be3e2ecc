<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as Command from '$lib/components/ui/command/index';
	import * as Popover from '$lib/components/ui/popover/index';
	import * as Select from '$lib/components/ui/select/index';
	import * as RadioGroup from '$lib/components/ui/radio-group/index';
	import * as Tooltip from '$lib/components/ui/tooltip/index';
	import { Input } from '$lib/components/ui/input';
	import Textarea from '$lib/components/ui/textarea/textarea.svelte';
	import { type TemplateInfoForm, templateInfoSchema } from '$lib/schemas/auditTypes';
	import { t } from '$lib/translations';
	import { toast } from 'svelte-sonner';
	import { cn } from '$lib/utils';
	import { ChevronsUpDown, Settings2 } from '@lucide/svelte';
	import { Check } from '@lucide/svelte';
	import { Switch } from '$lib/components/ui/switch';
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { AuditRepetition, EvaluationModes } from '$lib/enums/audits';
	import { formatTimeInput, minutesToHHMM } from '$lib/utils/time';
	import { useForm } from '$lib/hooks/superformValidation';
	import ConfirmDialog from '$lib/customComponents/confirm-dialog.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';

	let { auditType, auditors, templateInfoForm, onCancel, onSave, onSettingsOpen } = $props();
	let responsiblePersonOpen = $state(false);

	let originalData = $state<{
		name: string;
		code: string;
		specification: string;
		evaluationMode: string;
		responsiblePersonId: string;
		expectedDuration: string;
		repetetionPlan: string;
		active: boolean;
	}>({
		name: '',
		code: '',
		specification: '',
		evaluationMode: 'percentage',
		responsiblePersonId: '',
		expectedDuration: '',
		repetetionPlan: '',
		active: true
	});
	let showConfirmDialog = $state(false);

	let form = useForm<TemplateInfoForm>(
		templateInfoForm,
		// @ts-ignore
		templateInfoSchema,
		'form',
		() => {
			toast.success($t('auditTypes.info.templateSaved'));
			onSave();
		},
		() => {
			toast.error($t('auditTypes.info.templateSaveFailed'));
		}
	);

	const { form: formData, errors, enhance } = form;

	function hasChanges() {
		if (!originalData || !$formData) return false;

		return (
			JSON.stringify({
				name: $formData.name || '',
				code: $formData.code || '',
				specification: $formData.specification || '',
				evaluationMode: $formData.evaluationMode || 'percentage',
				responsiblePersonId: $formData.responsiblePersonId || '',
				expectedDuration: $formData.expectedDuration || '',
				repetetionPlan: $formData.repetetionPlan || '',
				active: $formData.active ?? true
			}) !==
			JSON.stringify({
				name: originalData.name || '',
				code: originalData.code || '',
				specification: originalData.specification || '',
				evaluationMode: originalData.evaluationMode || 'percentage',
				responsiblePersonId: originalData.responsiblePersonId || '',
				expectedDuration: originalData.expectedDuration || '',
				repetetionPlan: originalData.repetetionPlan || '',
				active: originalData.active ?? true
			})
		);
	}

	function handleAttemptCancelOrNavigate() {
		if (hasChanges()) {
			showConfirmDialog = true;
		} else {
			onCancel();
		}
	}

	function handleConfirmSave() {
		showConfirmDialog = false;
		const formElement = document.querySelector(
			'form[action="?/updateTemplateInfo"]'
		) as HTMLFormElement;
		if (formElement) {
			formElement.requestSubmit();
		}
	}

	export function attemptCancelOrNavigate() {
		handleAttemptCancelOrNavigate();
	}

	onMount(() => {
		$formData.specification = auditType.specification;
		$formData.responsiblePersonId = auditType.responsiblePerson?.id;
		$formData.expectedDuration = minutesToHHMM(auditType.expectedDuration);
		$formData.active = auditType.active ?? true;

		originalData = {
			name: auditType.name || '',
			code: auditType.code || '',
			specification: auditType.specification || '',
			evaluationMode: templateInfoForm.data.evaluationMode || 'percentage',
			responsiblePersonId: auditType.responsiblePerson?.id || '',
			expectedDuration: minutesToHHMM(auditType.expectedDuration) || '',
			repetetionPlan: auditType.repetetionPlan || '',
			active: auditType.active ?? true
		};
	});
</script>

<form method="POST" action="?/updateTemplateInfo" use:enhance>
	<input type="hidden" name="auditTypeId" value={auditType.id} />
	<div class="space-y-2">
		<div class="relative pb-6">
			<Form.Field {form} name="name">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.name')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.name}
							class={$errors.name ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.info.namePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="code">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.code')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.code}
							class={$errors.code ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.info.codePlaceholder')}
							maxlength={10}
							oninput={(e) => {
								const target = e.target as HTMLInputElement;
								$formData.code = target.value.toUpperCase();
							}}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="responsiblePersonId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.responsiblePerson')}</Form.Label>
						<Popover.Root bind:open={responsiblePersonOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'justify-between',
									!$formData.responsiblePersonId && 'text-muted-foreground',
									$errors.responsiblePersonId && 'border-red-500'
								)}
								role="combobox"
								{...props}
							>
								{#if $formData.responsiblePersonId && auditors}
									{(() => {
										const auditor = auditors.find(
											(a: { id: string }) => a.id === $formData.responsiblePersonId
										);
										return auditor
											? `${auditor.name.firstName || ''} ${auditor.name.lastName || ''}`
											: $t('auditTypes.info.responsiblePersonPlaceholder');
									})()}
								{:else}
									{$t('auditTypes.info.responsiblePersonPlaceholder')}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.responsiblePersonId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input
										autofocus
										placeholder={$t('auditTypes.info.responsiblePersonPlaceholder')}
										class="h-9"
									/>
									<Command.List>
										<Command.Empty>{$t('auditTypes.errors.noAuditorFound')}</Command.Empty>
										<Command.Group>
											{#each auditors as auditor, index (index)}
												<Command.Item
													value={`${auditor.name.firstName || ''} ${auditor.name.lastName || ''}`}
													onSelect={() => {
														$formData.responsiblePersonId = auditor.id;
														responsiblePersonOpen = false;
													}}
												>
													<span>{auditor.name.firstName || ''} {auditor.name.lastName || ''}</span>
													{#if auditor.id === $formData.responsiblePersonId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Fieldset {form} name="evaluationMode" class="space-y-3">
				<Form.Legend class="text-sm font-medium">
					{$t('questions.evaluationMode')}
				</Form.Legend>

				<div class="mx-4 grid grid-cols-1 gap-4 lg:grid-cols-2">
					<div class="min-w-0">
						<RadioGroup.Root
							bind:value={$formData.evaluationMode}
							class="flex flex-col "
							name="evaluationMode"
						>
							{#each Object.entries(EvaluationModes) as [key, value] (key)}
								<div class="flex items-center space-y-0 space-x-3">
									<Form.Control>
										{#snippet children({ props })}
											<RadioGroup.Item {value} {...props} />
											<Form.Label class="cursor-pointer text-sm font-normal">
												{$t(`questions.evaluationModes.${key}`)}
											</Form.Label>
										{/snippet}
									</Form.Control>
								</div>
							{/each}
						</RadioGroup.Root>
					</div>

					<div class="flex min-w-0 items-center">
						<Tooltip.Provider>
							<Tooltip.Root>
								<Tooltip.Trigger>
									<Button
										class="font-titillium min-w-[120px] rounded-xl bg-[#B1B7C3] px-10 py-6 text-sm font-semibold uppercase hover:bg-[#B1B7C3]/80"
										onclick={() => onSettingsOpen($formData.evaluationMode)}
									>
										<div class="flex flex-row items-center justify-center gap-2">
											<span class="text-base uppercase">{$t('common.settings')}</span>
											<Settings2 class="size-6" />
										</div>
									</Button>
								</Tooltip.Trigger>
								<Tooltip.Content>
									{$t('questions.evaluationModeSettingsTooltip')}
								</Tooltip.Content>
							</Tooltip.Root>
						</Tooltip.Provider>
					</div>
				</div>

				<Form.FieldErrors class="text-destructive text-xs" />
			</Form.Fieldset>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="active">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.status')}</Form.Label>
						<div class="flex items-center gap-4">
							<Switch
								bind:checked={$formData.active}
								{...props}
								class={cn(
									'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out',
									'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
									'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
									'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
									'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
									'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
									'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
									$errors.active ? 'ring-2 ring-red-500' : ''
								)}
							/>

							<span
								class={cn(
									'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
									'inline-block min-w-[100px]',
									$formData.active
										? 'bg-[#414E6B] text-white shadow-sm'
										: 'bg-gray-100 text-gray-600'
								)}
							>
								{$formData.active
									? $t('auditTypes.list.activeStates.true')
									: $t('auditTypes.list.activeStates.false')}
							</span>

							<span class="text-xs text-gray-600">
								{$t('auditTypes.info.statusDescription')}
							</span>

							<input type="hidden" name={props.name} value={$formData.active.toString()} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="expectedDuration">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.expectedDuration')}</Form.Label>
						<Input
							{...props}
							type="text"
							bind:value={$formData.expectedDuration}
							class={$errors.expectedDuration ? 'border-red-500' : ''}
							placeholder="HH:MM"
							pattern="^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
							oninput={(e) => {
								const formatted = formatTimeInput(e.currentTarget.value);
								e.currentTarget.value = formatted;
								$formData.expectedDuration = formatted;
							}}
							onblur={(e) => {
								const formatted = formatTimeInput(e.currentTarget.value, true);
								e.currentTarget.value = formatted;
								$formData.expectedDuration = formatted;
							}}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="repetetionPlan">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.repetetionPlan')}</Form.Label>
						<Select.Root type="single" bind:value={$formData.repetetionPlan} name={props.name}>
							<Select.Trigger
								{...props}
								class="w-full {$errors.repetetionPlan ? 'border-red-500' : ''}"
							>
								{$t(`audits.repetetions.${$formData.repetetionPlan}`)}
							</Select.Trigger>
							<Select.Content>
								{#each Object.entries(AuditRepetition) as [key, value] (key)}
									<Select.Item {value}>{$t(`audits.repetetions.${key}`)}</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="specification">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.info.specification')}</Form.Label>
						<Textarea
							{...props}
							bind:value={$formData.specification}
							class={$errors.specification ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.info.specificationPlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>

			<div class="mt-4 flex justify-end gap-4">
				<Button
					type="button"
					class="font-titillium min-w-[120px] rounded-xl bg-[#B1B7C3] p-7 text-sm font-semibold text-white uppercase hover:bg-[#B1B7C3]/80"
					onclick={handleAttemptCancelOrNavigate}
				>
					{$t('common.buttons.back')}
				</Button>

				<Form.Button
					type="submit"
					class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				>
					{$t('common.buttons.save')}
				</Form.Button>
			</div>
		</div>
	</div>
</form>

<ConfirmDialog
	bind:open={showConfirmDialog}
	title={$t('common.dialogs.confirm.title')}
	description={$t('common.dialogs.confirm.text')}
	confirmButtonText={$t('common.buttons.save')}
	onClose={() => {
		showConfirmDialog = false;
		onCancel();
	}}
	onConfirm={handleConfirmSave}
/>
