<script lang="ts">
	import Badge from '$lib/components/ui/badge/badge.svelte';
	import { locale, t } from '$lib/translations';
	import { formatDate } from '$lib/utils/date';
	import { formatMinutesDuration } from '$lib/utils/time';

	let { auditType } = $props();

	const responsiblePersonName = $state(() => {
		return auditType.responsiblePerson?.firstName + ' ' + auditType.responsiblePerson?.lastName;
	});
</script>

<div class="my-8 grid grid-cols-2 gap-2 gap-y-6 text-base lg:gap-4 lg:gap-y-6">
	{@render auditTypeInfo($t('auditTypes.info.code'), auditType.code)}
	{@render auditTypeInfo($t('auditTypes.info.responsiblePerson'), responsiblePersonName())}
	{@render auditTypeInfo(
		$t('auditTypes.info.repetetionPlan'),
		$t(`audits.repetetions.${auditType.repetetionPlan}`)
	)}
	{@render auditTypeInfo(
		$t('auditTypes.info.expectedDuration'),
		formatMinutesDuration(auditType.expectedDuration, $locale)
	)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="-py-0.5 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
			>{$t('auditTypes.info.status')}</span
		>
		<span class="truncate text-sm lg:text-lg">
			<Badge
				class={`font-titillium rounded-md px-2 py-0.5 text-xs font-light text-white uppercase lg:px-6 lg:py-1 lg:text-xs ${
					auditType.active ? 'bg-[#414E6B] ' : 'bg-secondary '
				}`}
			>
				{auditType.active ? $t('auditTypes.info.active') : $t('auditTypes.info.inactive')}
			</Badge>
		</span>
	</div>

	{@render auditTypeInfo(
		$t('auditTypes.info.updatedAt'),
		formatDate(auditType.updatedAt.toISOString(), $locale)
	)}
</div>

<hr class="hidden border-2 border-[#F3F3F3] lg:block" />

<div class="hidden py-2 lg:block">
	<div class="flex flex-col overflow-hidden text-left">
		<span class="truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
			>{$t('auditTypes.list.evaluationMode')}</span
		>
		<span class="text-sm break-words text-[#4B505A] lg:text-lg">
			{$t(`auditTypes.list.evaluationModes.${auditType.evaluationMode}`)}
		</span>
		{#if auditType.evaluationMode === 'percentage'}
			<div class="mt-2 flex w-fit flex-row gap-x-12 text-[13px]">
				<div class="flex min-w-[180px] flex-col items-start">
					<span class="text-[13px] leading-tight text-[#747C8A]"
						>{$t('auditTypes.info.thresholdAverage')}</span
					>
					<span class="font-mono text-[20px] font-semibold text-[#8A97A8]">
						{#if auditType.evaluation.successThreshold.auditThreshold_average != null}
							{auditType.evaluation.successThreshold.auditThreshold_average}
						{:else}
							<span title={$t('auditTypes.info.fallbackPlantValue')} class="text-xs text-[#A0AEC0]">
								{auditType.evaluation.successThreshold.plant_auditThreshold_average}
							</span>
						{/if}
					</span>
				</div>
				<div class="flex min-w-[180px] flex-col items-start">
					<span class="text-[13px] leading-tight text-[#747C8A]"
						>{$t('auditTypes.info.thresholdSuccess')}</span
					>
					<span class="font-mono text-[20px] font-semibold text-[#8A97A8]">
						{#if auditType.evaluation.successThreshold.auditThreshold_success != null}
							{auditType.evaluation.successThreshold.auditThreshold_success}
						{:else}
							<span title={$t('auditTypes.info.fallbackPlantValue')} class="text-xs text-[#A0AEC0]">
								{auditType.evaluation.successThreshold.plant_auditThreshold_success}
							</span>
						{/if}
					</span>
				</div>
			</div>
		{:else if auditType.evaluationMode === 'rules'}
			<div class="mt-2 grid w-fit grid-cols-2 gap-x-8 gap-y-1 text-[13px]">
				<div class="flex min-w-[120px] flex-col items-start">
					<span class="text-[11px] leading-tight text-[#747C8A]"
						>{$t('auditTypes.info.ruleAverage')}</span
					>
					<span class="text-sm text-[#414E6B]">
						{#if auditType.evaluation.rules.average_averageAuditAnswers != null}
							{auditType.evaluation.rules.average_averageAuditAnswers}
						{:else}
							<span title={$t('auditTypes.info.fallbackPlantValue')} class="text-xs text-[#A0AEC0]">
								{auditType.evaluation.rules.plant_average_averageAuditAnswers}
							</span>
						{/if}
					</span>
				</div>
				<div class="flex min-w-[120px] flex-col items-start">
					<span class="text-[11px] leading-tight text-[#747C8A]"
						>{$t('auditTypes.info.ruleAverageBad')}</span
					>
					<span class="font-mono text-[15px] font-semibold text-[#414E6B]">
						{#if auditType.evaluation.rules.average_badAuditAnswers != null}
							{auditType.evaluation.rules.average_badAuditAnswers}
						{:else}
							<span title={$t('auditTypes.info.fallbackPlantValue')} class="text-xs text-[#A0AEC0]">
								{auditType.evaluation.rules.plant_average_badAuditAnswers}
							</span>
						{/if}
					</span>
				</div>
				<div class="flex min-w-[120px] flex-col items-start">
					<span class="text-[11px] leading-tight text-[#747C8A]"
						>{$t('auditTypes.info.ruleNotSuccessfulAverage')}</span
					>
					<span class="font-mono text-[15px] font-semibold text-[#414E6B]">
						{#if auditType.evaluation.rules.notSuccessful_averageAnswers != null}
							{auditType.evaluation.rules.notSuccessful_averageAnswers}
						{:else}
							<span title={$t('auditTypes.info.fallbackPlantValue')} class="text-xs text-[#A0AEC0]">
								{auditType.evaluation.rules.plant_notSuccessful_averageAnswers}
							</span>
						{/if}
					</span>
				</div>
				<div class="flex min-w-[120px] flex-col items-start">
					<span class="text-[11px] leading-tight text-[#747C8A]"
						>{$t('auditTypes.info.ruleNotSuccessfulBad')}</span
					>
					<span class="font-mono text-[15px] font-semibold text-[#414E6B]">
						{#if auditType.evaluation.rules.notSuccessful_badAnswers != null}
							{auditType.evaluation.rules.notSuccessful_badAnswers}
						{:else}
							<span title={$t('auditTypes.info.fallbackPlantValue')} class="text-xs text-[#A0AEC0]">
								{auditType.evaluation.rules.plant_notSuccessful_badAnswers}
							</span>
						{/if}
					</span>
				</div>
			</div>
		{/if}
	</div>
</div>

<hr class="border-2 border-[#F3F3F3]" />

{#if auditType.specification != null}
	<div class="py-2">
		<div class="flex flex-col overflow-hidden text-left">
			<span class="my-2 truncate pt-2 text-xs text-[#747C8A] lg:text-sm"
				>{$t('auditTypes.info.specification')}</span
			>
			<span class="text-sm break-words text-[#4B505A] lg:text-lg">{auditType.specification}</span>
		</div>
	</div>
{/if}

{#snippet auditTypeInfo(name: string, content: string)}
	<div class="flex flex-col overflow-hidden text-left">
		<span class="-py-0.5 truncate pt-2 text-xs text-[#747C8A] lg:text-sm">{name}</span>
		<span class="text-sm break-words text-[#4B505A] lg:text-lg">{content}</span>
	</div>
{/snippet}
