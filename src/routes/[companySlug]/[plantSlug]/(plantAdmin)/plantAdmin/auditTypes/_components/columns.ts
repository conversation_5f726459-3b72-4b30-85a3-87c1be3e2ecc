import { renderComponent } from '$lib/components/ui/data-table';
import type { ColumnDef } from '@tanstack/table-core';
import { t } from '$lib/translations';
import { get } from 'svelte/store';
import CustomHeader from '$lib/customComponents/custom-header.svelte';
import DataTableActions from './data-table-actions.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import AuditorCell from '$lib/customComponents/tableFormat/auditor-cell.svelte';
import type { AuditTypeDTO } from '$lib/DTO/auditTypes/auditTypes';

export function createColumns(callbacks: {
	onDuplicate: (id: string) => void;
}): ColumnDef<AuditTypeDTO>[] {
	return [
		{
			id: 'name',
			accessorKey: 'name',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.name'),
					column
				}),
			cell: ({ row }) => {
				const name = row.getValue('name') as string;
				return renderComponent(Cell, {
					content: name
				});
			},
			enableSorting: true
		},
		{
			id: 'code',
			accessorKey: 'code',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.code'),
					column
				}),
			cell: ({ row }) => {
				const code = row.getValue('code') as string;
				return renderComponent(Cell, {
					content: code
				});
			},
			enableSorting: true
		},

		{
			id: 'evaluationMode',
			accessorKey: 'evaluationMode',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.evaluationMode'),
					column
				}),
			cell: ({ row }) => {
				const evaluationMode = row.getValue('evaluationMode') as string;
				return renderComponent(Cell, {
					content:
						evaluationMode === 'percentage' || evaluationMode === null
							? t.get('auditTypes.list.evaluationModes.percentage')
							: t.get('auditTypes.list.evaluationModes.rules')
				});
			},
			enableSorting: true
		},
		{
			id: 'active',
			accessorKey: 'active',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.active'),
					column
				}),
			cell: ({ row }) => {
				const active = row.getValue('active') as boolean;
				return renderComponent(Cell, {
					content: active
						? t.get('auditTypes.list.activeStates.true')
						: t.get('auditTypes.list.activeStates.false')
				});
			},
			enableSorting: true
		},
		{
			id: 'responsiblePerson',
			accessorFn: (row) => {
				const person = row.responsiblePerson;
				if (!person) return '';
				return `${person.firstName || ''} ${person.lastName || ''}`.trim();
			},
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('auditTypes.list.responsiblePerson'),
					column
				}),
			cell: ({ row }) => {
				const auditor = row.original.responsiblePerson;
				if (!auditor) return null;

				return renderComponent(AuditorCell, {
					firstName: auditor.firstName,
					lastName: auditor.lastName
				});
			},
			enableSorting: true
		},
		{
			id: 'actions',
			enableSorting: false,
			enableHiding: false,
			meta: {
				headerClass: 'text-right',
				cellClass: 'text-right'
			},
			cell: ({ row }) =>
				renderComponent(DataTableActions, {
					id: row.original.id,
					row: row.original,
					onDuplicate: () => (row.original.id ? callbacks.onDuplicate(row.original.id) : undefined)
				})
		}
	];
}
