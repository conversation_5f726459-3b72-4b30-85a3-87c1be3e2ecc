<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { <PERSON><PERSON>, Ellip<PERSON>, Eye, Trash } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import type { AuditTypeDTO } from '$lib/DTO/auditTypes/auditTypes';
	import { page } from '$app/state';

	let { id, onDuplicate }: { id: string; onDuplicate: (id: string) => void } = $props<{
		row: AuditTypeDTO;
	}>();
	let open = $state(false);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase">
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() =>
					goto(`/${page.params.companySlug}/${page.params.plantSlug}/plantAdmin/auditTypes/${id}`)}
			>
				<Eye class="size-4 text-white" />
				<span>{$t('common.buttons.details')}</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => onDuplicate(id)}
			>
				<Copy class="size-4 text-white" />
				<span>{$t('common.buttons.duplicate')}</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => (open = true)}
			>
				<Trash class="size-4 text-white" />
				<span>{$t('common.buttons.delete')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	{id}
	title={$t('auditTypes.deleteDialog.title')}
	description={$t('auditTypes.deleteDialog.description')}
	formAction="?/deleteAuditType"
	onDelete={() => toast.success($t('auditTypes.deleteDialog.success'))}
	onClose={() => (open = false)}
/>
