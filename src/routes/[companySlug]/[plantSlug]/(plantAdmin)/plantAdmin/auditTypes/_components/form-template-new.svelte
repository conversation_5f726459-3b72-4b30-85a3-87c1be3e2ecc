<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as Command from '$lib/components/ui/command/index';
	import * as Popover from '$lib/components/ui/popover/index';
	import * as Select from '$lib/components/ui/select/index';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { AuditRepetition } from '$lib/enums/audits';
	import { Button } from '$lib/components/ui/button';
	import { onMount } from 'svelte';

	let { form, auditors, onClose } = $props();

	const { form: formData, enhance, errors } = form;

	let responsiblePersonOpen = $state(false);

	let originalData = $state<{
		name: string;
		code: string;
		repetetionPlan: string;
		responsiblePersonId: string;
	}>({
		name: '',
		code: '',
		repetetionPlan: '',
		responsiblePersonId: ''
	});
	let showConfirmDialog = $state(false);

	function hasChanges() {
		if (!originalData || !$formData) return false;

		return (
			JSON.stringify({
				name: $formData.name || '',
				code: $formData.code || '',
				repetetionPlan: $formData.repetetionPlan || '',
				responsiblePersonId: $formData.responsiblePersonId || ''
			}) !==
			JSON.stringify({
				name: originalData.name || '',
				code: originalData.code || '',
				repetetionPlan: originalData.repetetionPlan || '',
				responsiblePersonId: originalData.responsiblePersonId || ''
			})
		);
	}

	function handleAttemptCancelOrNavigate() {
		if (hasChanges()) {
			showConfirmDialog = true;
		} else {
			onClose();
		}
	}

	export function attemptCancelOrNavigate() {
		handleAttemptCancelOrNavigate();
	}

	onMount(() => {
		originalData = {
			name: '',
			code: '',
			repetetionPlan: '',
			responsiblePersonId: ''
		};
	});
</script>

<form method="POST" action="?/createAuditType" use:enhance class="space-y-3">
	<div class="h-[70px]">
		<Form.Field {form} name="name">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('auditTypes.info.name')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.name}
						class={$errors.name ? 'border-red-500' : ''}
						placeholder={$t('auditTypes.info.namePlaceholder')}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="float-right text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="h-[70px]">
		<Form.Field {form} name="code">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('auditTypes.info.code')}</Form.Label>
					<Input
						{...props}
						bind:value={$formData.code}
						class={$errors.code ? 'border-red-500' : ''}
						placeholder={$t('auditTypes.info.codePlaceholder')}
						maxlength={10}
						oninput={(e) => {
							const target = e.target as HTMLInputElement;
							$formData.code = target.value.toUpperCase();
						}}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="float-right text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="h-[70px]">
		<Form.Field {form} name="responsiblePersonId">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('auditTypes.info.responsiblePerson')}</Form.Label>
					<Popover.Root bind:open={responsiblePersonOpen}>
						<Popover.Trigger
							class={cn(
								'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
								'justify-between',
								!$formData.responsiblePersonId && 'text-muted-foreground',
								$errors.responsiblePersonId && 'border-red-500'
							)}
							role="combobox"
							{...props}
						>
							{#if $formData.responsiblePersonId && auditors}
								{(() => {
									const auditor = auditors.find(
										(a: { id: string }) => a.id === $formData.responsiblePersonId
									);
									return auditor
										? `${auditor.name.firstName || ''} ${auditor.name.lastName || ''}`
										: $t('auditTypes.info.responsiblePersonPlaceholder');
								})()}
							{:else}
								{$t('auditTypes.info.responsiblePersonPlaceholder')}
							{/if}
							<ChevronsUpDown class="h-4 w-4 opacity-50" />
						</Popover.Trigger>
						<input hidden value={$formData.responsiblePersonId} name={props.name} />
						<Popover.Content
							class="w-(--bits-popover-anchor-width) p-0"
							align="start"
							sideOffset={5}
						>
							<Command.Root class="w-full">
								<Command.Input
									autofocus
									placeholder={$t('auditTypes.info.responsiblePersonSearch')}
									class="h-9"
								/>
								<Command.List>
									<Command.Empty>{$t('auditTypes.errors.noAuditorFound')}</Command.Empty>
									<Command.Group>
										{#each auditors as auditor (auditor.id)}
											<Command.Item
												value={`${auditor.name.firstName || ''} ${auditor.name.lastName || ''}`}
												onSelect={() => {
													$formData.responsiblePersonId = auditor.id;
													responsiblePersonOpen = false;
												}}
											>
												<span>{auditor.name.firstName || ''} {auditor.name.lastName || ''}</span>
												{#if auditor.id === $formData.responsiblePersonId}
													<Check class="ml-auto h-4 w-4" />
												{:else}
													<div class="ml-auto h-4 w-4"></div>
												{/if}
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.List>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="float-right text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="h-[70px]">
		<Form.Field {form} name="repetetionPlan">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>{$t('auditTypes.info.repetetionPlan')}</Form.Label>
					<Select.Root type="single" {...props} bind:value={$formData.repetetionPlan}>
						<Select.Trigger {...props} class="w-full">
							{$t(`audits.repetetions.${$formData.repetetionPlan}`)}</Select.Trigger
						>
						<Select.Content>
							{#each Object.entries(AuditRepetition) as [key, value] (key)}
								<Select.Item {value}>{$t(`audits.repetetions.${key}`)}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="float-right text-xs text-red-500" />
		</Form.Field>
	</div>

	<div class="mt-4 flex justify-end gap-4 pt-2">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={onClose}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Form.Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{$t('common.buttons.create')}
		</Form.Button>
	</div>
</form>
