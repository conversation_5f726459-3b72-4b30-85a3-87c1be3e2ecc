<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Form from '$lib/components/ui/form/index';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';

	let { form, editingCategory, onClose } = $props();

	const { form: formData, enhance, errors } = form;
</script>

<form method="POST" action="?/editCategory" use:enhance>
	{#if editingCategory}
		<input type="hidden" name="categoryId" value={editingCategory.id} />
	{/if}

	<div class="space-y-2">
		<div class="relative pb-6">
			<Form.Field {form} name="title">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.newCategory.titleLabel')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.title}
							class={$errors.title ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.newCategory.titlePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="relative pb-6">
			<Form.Field {form} name="subtitle">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('auditTypes.newCategory.subtitleLabel')}</Form.Label>
						<Input
							{...props}
							bind:value={$formData.subtitle}
							class={$errors.subtitle ? 'border-red-500' : ''}
							placeholder={$t('auditTypes.newCategory.subtitlePlaceholder')}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="flex justify-end gap-4">
			<Button
				type="button"
				variant="outline"
				class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				onclick={() => {
					onClose();
				}}
			>
				{$t('common.buttons.cancel')}
			</Button>

			<Button
				type="submit"
				class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			>
				{$t('common.buttons.save')}
			</Button>
		</div>
	</div>
</form>
