<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { t } from '$lib/translations';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { questionSchema, type QuestionForm } from '$lib/schemas/audits/questions';
	import { toast } from 'svelte-sonner';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import FormQuestion from '../../../../../(pages)/questions/_components/form-question.svelte';
	import FormTemplateQuestionsList from './form-template-questions-list.svelte';

	// Props
	let {
		availableQuestions,
		selectedCategory,
		onCloseDialog,
		existingQuestionsInCategory = [],
		allExistingQuestions = []
	}: {
		availableQuestions: { id: string; text: string; subtext?: string }[];
		selectedCategory: string;
		onCloseDialog: () => void;
		existingQuestionsInCategory?: string[];
		allExistingQuestions?: string[];
	} = $props();

	// State
	let searchQuery = $state('');
	let searchResults = $state<typeof availableQuestions>([]);
	let createQuestionDialogOpen = $state(false);
	let activeDialog = $state('search');

	const newQuestionForm = superForm<QuestionForm>(
		// svelte-ignore state_referenced_locally
		{ text: searchQuery, subtext: '', evaluationType: 'oknok', tagIds: [], tagNames: [] },
		{
			validators: zodClient(questionSchema),
			resetForm: true,
			onResult: ({ result }) => {
				if (result.type === 'success') {
					createQuestionDialogOpen = false;
					onCloseDialog();
					toast.success($t('auditTypes.newQuestion.questionCreatedAndAdded'));
				}
			}
		}
	);

	const { form: newQuestionFormData } = newQuestionForm;

	// Handlers
	function handleSearch() {
		searchResults =
			searchQuery.length < 2
				? []
				: availableQuestions
						.filter(
							(q: { id: string; text: string }) =>
								q.text.toLowerCase().includes(searchQuery.toLowerCase()) &&
								!allExistingQuestions.includes(q.id)
						)
						.slice(0, 5);
	}

	function handleSearchInput(value: string) {
		searchQuery = value;
		handleSearch();
	}

	function switchToCreateQuestionDialog() {
		$newQuestionFormData.text = searchQuery;
		activeDialog = 'create';
	}
</script>

{#if activeDialog === 'search'}
	<div class="space-y-4 p-2">
		<div class="flex justify-center pt-2 pb-4 text-center text-[#747C8A]">
			{$t('auditTypes.newQuestion.searchOrCreatePrompt')}
		</div>
		<div class="flex flex-row items-center justify-between gap-4 pb-4">
			<div class="w-[60%]">
				<SearchInput value={searchQuery} onSearch={handleSearchInput} />
			</div>
			<div class="w-[40%]">
				<Button
					variant="default"
					class="font-titillium h-12 w-full rounded-xl bg-[#7D9AD3] font-semibold text-white uppercase hover:bg-[#7D9AD3]/80 hover:text-white"
					onclick={switchToCreateQuestionDialog}
				>
					{$t('auditTypes.newQuestion.createNew')}
				</Button>
			</div>
		</div>

		{#if searchResults.length > 0}
			<FormTemplateQuestionsList {searchResults} {selectedCategory} {onCloseDialog} />
		{:else if searchQuery && searchQuery.length > 2}
			<hr class="border-2" />
			<div class="flex flex-col items-center justify-center gap-4 py-6">
				<p class="text-gray-500">
					{$t('auditTypes.newQuestion.noResults')}
				</p>
			</div>
		{/if}
	</div>
{:else if activeDialog === 'create'}
	<FormQuestion
		form={newQuestionForm}
		onClose={() => {
			activeDialog = 'search';
			createQuestionDialogOpen = false;
		}}
		isEditMode={false}
		formAction="?/createAndAddQuestion"
	/>
	<input type="hidden" name="category" value={selectedCategory} form="question-form" />
{/if}
