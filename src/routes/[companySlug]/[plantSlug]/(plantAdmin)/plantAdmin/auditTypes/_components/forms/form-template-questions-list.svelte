<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import { Link } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';

	let { searchResults, selectedCategory, onCloseDialog } = $props();

	let limitedResults = searchResults.slice(0, 5);
</script>

<hr class="border-2" />
<div class="flex flex-col gap-4 pt-4">
	<div>
		{$t('auditTypes.newQuestion.clickToAdd')}
	</div>
	<div class="flex max-h-60 flex-col gap-4 overflow-y-auto px-4">
		{#each limitedResults as question (question.id)}
			<div
				class="group rounded-lg border border-transparent px-4 py-2 hover:border-dashed hover:border-gray-300"
			>
				<div class="flex h-10 items-center justify-between text-sm text-[#747C8A]">
					<div class="flex-1 overflow-hidden">
						<h4 class="line-clamp-2 leading-tight font-medium">
							{question.text}
						</h4>
					</div>
					<div class="ml-4 flex shrink-0 gap-2">
						<form
							method="POST"
							action="?/addQuestion"
							use:enhance={() => {
								return ({ result, update }) => {
									update({ reset: false });
									if (result.type === 'success') {
										toast.success($t('auditTypes.newQuestion.questionAdded'));
										onCloseDialog();
									} else {
										toast.error($t('auditTypes.newQuestion.questionAddFailed'));
									}
								};
							}}
						>
							<input type="hidden" name="category" value={selectedCategory} />
							<input type="hidden" name="questionId" value={question.id} />
							<input type="hidden" name="required" value="true" />

							<Button
								variant="default"
								size="icon"
								type="submit"
								class="bg-primary hover:bg-primary/80 h-8 w-8 rounded-lg p-1.5 text-white hover:text-white"
							>
								<Link class="h-4 w-4" />
							</Button>
						</form>
					</div>
				</div>
			</div>
		{/each}
	</div>
</div>
