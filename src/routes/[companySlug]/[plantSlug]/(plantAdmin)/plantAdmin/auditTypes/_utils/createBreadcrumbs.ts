import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createAuditTypesBreadcrumbs(
	auditTypeCode?: string,
	auditTypeName?: string,
	companySlug?: string,
	plantSlug?: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		}
	];

	if (auditTypeCode || auditTypeName) {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'common.navigation.plantAdmin',
				href: `/${companySlug}/${plantSlug}/general`
			},
			{
				kind: 'static',
				label: 'common.navigation.auditTypes',
				href: `/${companySlug}/${plantSlug}/auditTypes`
			},
			{
				kind: 'dynamic',
				label: auditTypeName! || auditTypeCode!
			}
		);
	} else {
		breadcrumbs.push(
			{
				kind: 'static',
				label: 'common.navigation.plantAdmin',
				href: `/${companySlug}/${plantSlug}/dashboard`
			},
			{
				kind: 'static',
				label: 'common.navigation.auditTypes'
			}
		);
	}

	return breadcrumbs;
}
