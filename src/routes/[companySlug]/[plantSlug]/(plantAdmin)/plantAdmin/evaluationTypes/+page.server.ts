import { SettingsPlantEvalConfig } from '$lib/server/services/settings';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageServerLoad } from './$types';
import { createPlantAdminEvalTypesBreadcrumbs } from './_utils/createBreadcrumbs';
import { plantEvaluationConfigSchema } from '$lib/schemas/evaluationConfig';
import { fail } from '@sveltejs/kit';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { t } from '$lib/translations';

export const load: PageServerLoad = async ({ parent }) => {
	const { context } = await parent();

	const [evaluationConfig] = await Promise.all([
		SettingsPlantEvalConfig.getPlantEvaluationConfig(context.plantId)
	]);

	const evaluationConfigForm = await superValidate(
		{
			...evaluationConfig,
			evalWeight_wReservations: Number(evaluationConfig.evalWeight_wReservations)
		},
		zod(plantEvaluationConfigSchema)
	);

	return {
		evaluationConfigForm,
		breadcrumbs: createPlantAdminEvalTypesBreadcrumbs(context.companySlug, context.plantSlug)
	};
};

export const actions = {
	updatePlantEvaluationConfig: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(plantEvaluationConfigSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);
			await SettingsPlantEvalConfig.savePlantEvaluationConfig(context.plantId, form.data);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to update evaluation config:', error);
			return fail(500, {
				form,
				message: t.get('errors.evaluationConfig.failedToSave')
			});
		}
	}
};
