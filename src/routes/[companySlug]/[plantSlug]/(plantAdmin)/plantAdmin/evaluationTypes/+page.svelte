<script lang="ts">
	import { t } from '$lib/translations/index.js';
	import { fade } from 'svelte/transition';
	import * as Tabs from '$lib/components/ui/tabs/index';
	import Button from '$lib/components/ui/button/button.svelte';
	import { useForm } from '$lib/hooks/superformValidation';
	import {
		plantEvaluationConfigSchema,
		type PlantEvaluationConfigForm
	} from '$lib/schemas/evaluationConfig';
	import { toast } from 'svelte-sonner';
	import WeightsPlantadminTab from './_components/weights-plantadmin-tab.svelte';
	import ThresholdsPlantadminTab from './_components/thresholds-plantadmin-tab.svelte';
	import RulesPlantadminTab from './_components/rules-plantadmin-tab.svelte';
	import AdminResultsPlantadminTab from './_components/admin-result-plantadmin-tab.svelte';

	let { data } = $props();

	const evaluationForm = useForm<PlantEvaluationConfigForm>(
		data.evaluationConfigForm,
		plantEvaluationConfigSchema,
		'form',
		() => {
			toast.success($t('evaluationConfig.saved'));
		},
		() => {
			toast.error($t('evaluationConfig.saveFailed'));
		},
		undefined,
		false
	);

	const { form, enhance } = evaluationForm;
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.plantAdmin')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9"
>
	<div class="flex items-center justify-between">
		<h2 class="text-xl font-semibold">Typy hodnocení</h2>
	</div>
	<div class="flex min-h-0 flex-1 flex-col">
		<form
			method="POST"
			action="?/updatePlantEvaluationConfig"
			use:enhance
			class="flex flex-1 flex-col overflow-auto px-8 py-6"
		>
			<div class="h-full min-h-0 flex-1">
				<Tabs.Root value="thresholds">
					<Tabs.List class="flex w-full justify-between border-b">
						<Tabs.Trigger value="thresholds" class="hover:text-black">
							{$t('evaluationConfig.thresholds.title')}
						</Tabs.Trigger>
						<Tabs.Trigger value="weights" class="hover:text-black">
							{$t('evaluationConfig.weights.title')}
						</Tabs.Trigger>
						<Tabs.Trigger value="rules" class="hover:text-black">
							{$t('evaluationConfig.rules.title')}
						</Tabs.Trigger>
						<Tabs.Trigger value="auditResult" class="hover:text-black">
							{$t('evaluationConfig.auditThresholds.title')}
						</Tabs.Trigger>
					</Tabs.List>

					<!-- Thresholds Tab -->
					<Tabs.Content value="thresholds" class="overflow-auto">
						<ThresholdsPlantadminTab {evaluationForm} />
					</Tabs.Content>

					<!-- Weights Tab -->
					<Tabs.Content value="weights">
						<WeightsPlantadminTab {evaluationForm} />
					</Tabs.Content>

					<!-- Rules Tab  -->
					<Tabs.Content value="rules">
						<RulesPlantadminTab {evaluationForm} />
					</Tabs.Content>

					<!-- Audit Results Tab -->
					<Tabs.Content value="auditResult">
						<AdminResultsPlantadminTab {evaluationForm} />
					</Tabs.Content>
				</Tabs.Root>
			</div>
			<div
				class="sticky right-0 bottom-0 left-0 z-10 mt-auto flex justify-end gap-4 border-t bg-white pt-4"
			>
				<Button
					type="submit"
					class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				>
					{$t('common.buttons.save')}
				</Button>
			</div>
		</form>
	</div>
</div>
