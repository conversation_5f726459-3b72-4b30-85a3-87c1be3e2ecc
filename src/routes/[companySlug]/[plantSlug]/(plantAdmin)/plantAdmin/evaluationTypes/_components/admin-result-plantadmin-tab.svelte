<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';

	let { evaluationForm } = $props();

	const { form: evalFormStore, errors: evalErrors, enhance } = evaluationForm;
</script>

<div class="flex h-full flex-col p-6">
	<div class="mx-auto flex w-full flex-1 flex-col space-y-8">
		<div class="space-y-3">
			<h3 class="text-xl font-semibold text-gray-900">
				{$t('evaluationConfig.auditThresholds.title')}
			</h3>
			<p class="text-base text-gray-600">
				{$t('evaluationConfig.auditThresholds.description')}
			</p>
		</div>

		<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
			<div class="space-y-3">
				<Form.Field form={evaluationForm} name="auditThreshold_average">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium text-gray-700"
								>{$t('evaluationConfig.auditThresholds.average')}</Form.Label
							>
							<div class="relative">
								<Input
									{...props}
									type="number"
									min="0"
									max={$evalFormStore.auditThreshold_success
										? $evalFormStore.auditThreshold_success - 1
										: 99}
									bind:value={$evalFormStore.auditThreshold_average}
									class="h-12 pr-10 text-lg {$evalErrors.auditThreshold_average
										? 'border-red-500'
										: 'border-gray-200'}"
									placeholder={$t('evaluationConfig.auditThresholds.averagePlaceholder')}
								/>
								<span
									class="absolute top-1/2 right-4 -translate-y-1/2 text-lg font-medium text-gray-500"
									>%</span
								>
							</div>
							{#if $evalErrors.auditThreshold_average}
								<div class="mt-2 text-sm text-red-500">
									{$evalErrors.auditThreshold_average}
								</div>
							{/if}
							<Form.Description class="mt-2 text-sm text-gray-600">
								{$t('evaluationConfig.auditThresholds.averageDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>

			<div class="space-y-3">
				<Form.Field form={evaluationForm} name="auditThreshold_success">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label class="text-sm font-medium text-gray-700"
								>{$t('evaluationConfig.auditThresholds.success')}</Form.Label
							>
							<div class="relative">
								<Input
									{...props}
									type="number"
									min={$evalFormStore.auditThreshold_average
										? $evalFormStore.auditThreshold_average + 1
										: 1}
									max="100"
									bind:value={$evalFormStore.auditThreshold_success}
									class="h-12 pr-10 text-lg {$evalErrors.auditThreshold_success
										? 'border-red-500'
										: 'border-gray-200'}"
									placeholder={$t('evaluationConfig.auditThresholds.successPlaceholder')}
								/>
								<span
									class="absolute top-1/2 right-4 -translate-y-1/2 text-lg font-medium text-gray-500"
									>%</span
								>
							</div>
							{#if $evalErrors.auditThreshold_success}
								<div class="mt-2 text-sm text-red-500">
									{$evalErrors.auditThreshold_success}
								</div>
							{/if}
							<Form.Description class="mt-2 text-sm text-gray-600">
								{$t('evaluationConfig.auditThresholds.successDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>
		</div>

		<div class="flex flex-1">
			<div class="flex min-h-[300px] w-full flex-1 items-center justify-center">
				<div class="mt-8 w-[95%] rounded-xl border border-gray-200 bg-gray-50 p-6">
					<h4 class="mb-4 text-lg font-medium text-gray-900">
						{$t('evaluationConfig.auditThresholds.preview')}
					</h4>
					<div class="flex space-x-2">
						<div
							class="h-6 flex-1 rounded-lg bg-gradient-to-r from-[#D18385] via-[#EFDF66] to-[#90DAB4] shadow-sm"
						></div>
					</div>
					<div class="mt-3 flex justify-between text-sm font-medium text-gray-700">
						<span>0%</span>
						<span class="text-[#d4c65b]">{$evalFormStore.auditThreshold_average || 70}%</span>
						<span class="text-[#90DAB4]">{$evalFormStore.auditThreshold_success || 90}%</span>
						<span>100%</span>
					</div>
					<div class="mt-2 flex justify-between text-sm">
						<span class="font-medium text-[#D18385]"
							>{$t('evaluationConfig.auditThresholds.unsuccessful')}</span
						>
						<span class="font-medium text-[#d4c65b]"
							>{$t('evaluationConfig.auditThresholds.averageLabel')}</span
						>
						<span class="font-medium text-[#90DAB4]"
							>{$t('evaluationConfig.auditThresholds.successLabel')}</span
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
