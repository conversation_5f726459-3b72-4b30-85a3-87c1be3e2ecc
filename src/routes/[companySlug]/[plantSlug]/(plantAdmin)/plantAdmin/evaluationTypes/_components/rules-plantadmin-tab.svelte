<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';

	let { evaluationForm } = $props();

	const { form: evalFormStore, errors: evalErrors } = evaluationForm;
</script>

<div class="space-y-6 p-4">
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">{$t('evaluationConfig.rules.title')}</h3>
		<p class="text-sm text-gray-600">{$t('evaluationConfig.rules.description')}</p>

		<div class="flex flex-1 flex-col space-y-6">
			<div class="space-y-4">
				<h4 class="text-md text-[#d4c65b]">
					{$t('evaluationConfig.rules.average.title')}
				</h4>

				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<Form.Field form={evaluationForm} name="average_averageAuditAnswers">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>{$t('evaluationConfig.rules.average.count')}</Form.Label>
								<div class="relative">
									<Input
										{...props}
										type="number"
										min="0"
										max={$evalFormStore.notSuccessful_averageAnswers &&
										$evalFormStore.average_averageAuditAnswers !== 0
											? $evalFormStore.notSuccessful_averageAnswers - 1
											: undefined}
										bind:value={$evalFormStore.average_averageAuditAnswers}
										class={$evalErrors.average_averageAuditAnswers ? 'border-red-500' : ''}
										placeholder={$t('evaluationConfig.rules.average.placeholder')}
									/>
								</div>
								{#if $evalErrors.average_averageAuditAnswers}
									<div class="mt-1 text-xs text-red-500">
										{$evalErrors.average_averageAuditAnswers}
									</div>
								{/if}
								<Form.Description class="mt-1 text-xs text-gray-500">
									{$t('evaluationConfig.rules.average.description')}
								</Form.Description>
							{/snippet}
						</Form.Control>
					</Form.Field>

					<Form.Field form={evaluationForm} name="average_badAuditAnswers">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>{$t('evaluationConfig.rules.average.badCount')}</Form.Label>
								<div class="relative">
									<Input
										{...props}
										type="number"
										min="0"
										max={$evalFormStore.notSuccessful_badAnswers &&
										$evalFormStore.average_badAuditAnswers !== 0
											? $evalFormStore.notSuccessful_badAnswers - 1
											: undefined}
										bind:value={$evalFormStore.average_badAuditAnswers}
										class={$evalErrors.average_badAuditAnswers ? 'border-red-500' : ''}
										placeholder={$t('evaluationConfig.rules.average.badPlaceholder')}
									/>
								</div>
								{#if $evalErrors.average_badAuditAnswers}
									<div class="mt-1 text-xs text-red-500">
										{$evalErrors.average_badAuditAnswers}
									</div>
								{/if}
								<Form.Description class="mt-1 text-xs text-gray-500">
									{$t('evaluationConfig.rules.average.badDescription')}
								</Form.Description>
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>
			</div>

			<div class="space-y-4">
				<h4 class="text-md text-[#D18385]">
					{$t('evaluationConfig.rules.unsuccessful.title')}
				</h4>

				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<Form.Field form={evaluationForm} name="notSuccessful_averageAnswers">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>{$t('evaluationConfig.rules.unsuccessful.averageCount')}</Form.Label>
								<div class="relative">
									<Input
										{...props}
										type="number"
										min={$evalFormStore.average_averageAuditAnswers &&
										$evalFormStore.average_averageAuditAnswers > 0
											? $evalFormStore.average_averageAuditAnswers + 1
											: 1}
										bind:value={$evalFormStore.notSuccessful_averageAnswers}
										class={$evalErrors.notSuccessful_averageAnswers ? 'border-red-500' : ''}
										placeholder={$t('evaluationConfig.rules.unsuccessful.averagePlaceholder')}
									/>
								</div>
								{#if $evalErrors.notSuccessful_averageAnswers}
									<div class="mt-1 text-xs text-red-500">
										{$evalErrors.notSuccessful_averageAnswers}
									</div>
								{/if}
								<Form.Description class="mt-1 text-xs text-gray-500">
									{$t('evaluationConfig.rules.unsuccessful.averageDescription')}
								</Form.Description>
							{/snippet}
						</Form.Control>
					</Form.Field>

					<Form.Field form={evaluationForm} name="notSuccessful_badAnswers">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>{$t('evaluationConfig.rules.unsuccessful.badCount')}</Form.Label>
								<div class="relative">
									<Input
										{...props}
										type="number"
										min="0"
										bind:value={$evalFormStore.notSuccessful_badAnswers}
										class={$evalErrors.notSuccessful_badAnswers ? 'border-red-500' : ''}
										placeholder={$t('evaluationConfig.rules.unsuccessful.badPlaceholder')}
									/>
								</div>
								{#if $evalErrors.notSuccessful_badAnswers}
									<div class="mt-1 text-xs text-red-500">
										{$evalErrors.notSuccessful_badAnswers}
									</div>
								{/if}
								<Form.Description class="mt-1 text-xs text-gray-500">
									{$t('evaluationConfig.rules.unsuccessful.badDescription')}
								</Form.Description>
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>
				<div class="my-10 rounded-lg border border-yellow-200 bg-yellow-50 p-3">
					<p class="text-sm text-yellow-800">
						<strong>{$t('evaluationConfig.rules.validation.title')}:</strong>
						{$t('evaluationConfig.rules.validation.description')}
					</p>
				</div>
			</div>
		</div>
	</div>
</div>
