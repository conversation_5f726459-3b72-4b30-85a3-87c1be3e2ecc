<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';

	let { evaluationForm } = $props();

	const { form: evalFormStore, errors: evalErrors } = evaluationForm;
</script>

<div class="space-y-6 p-4">
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">
			{$t('evaluationConfig.thresholds.percentages.title')}
		</h3>

		<div class="grid grid-cols-1 items-center justify-center gap-6 md:grid-cols-2">
			<div class="space-y-4">
				<Form.Field form={evaluationForm} name="percentageThreshold_average">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('evaluationConfig.thresholds.percentages.average')}</Form.Label>
							<div class="relative">
								<Input
									{...props}
									type="number"
									min="1"
									max="99"
									bind:value={$evalFormStore.percentageThreshold_average}
									class={$evalErrors.percentageThreshold_average ? 'border-red-500 pr-8' : 'pr-8'}
									placeholder={$t('evaluationConfig.thresholds.percentages.averagePlaceholder')}
								/>
								<span class="absolute top-1/2 right-3 -translate-y-1/2 transform text-gray-500"
									>%</span
								>
							</div>
							{#if $evalErrors.percentageThreshold_average}
								<div class="mt-1 text-xs text-red-500">
									{$evalErrors.percentageThreshold_average}
								</div>
							{/if}
							<Form.Description class="mt-1 text-xs text-gray-500">
								{$t('evaluationConfig.thresholds.percentages.averageDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>

			<div class="space-y-4">
				<Form.Field form={evaluationForm} name="percentageThreshold_success">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('evaluationConfig.thresholds.percentages.success')}</Form.Label>
							<div class="relative">
								<Input
									{...props}
									type="number"
									min={$evalFormStore.percentageThreshold_average
										? $evalFormStore.percentageThreshold_average + 1
										: 2}
									max="100"
									bind:value={$evalFormStore.percentageThreshold_success}
									class={$evalErrors.percentageThreshold_success ? 'border-red-500 pr-8' : 'pr-8'}
									placeholder={$t('evaluationConfig.thresholds.percentages.successPlaceholder')}
								/>
								<span class="absolute top-1/2 right-3 -translate-y-1/2 transform text-gray-500"
									>%</span
								>
							</div>
							{#if $evalErrors.percentageThreshold_success}
								<div class="mt-1 text-xs text-red-500">
									{$evalErrors.percentageThreshold_success}
								</div>
							{/if}
							<Form.Description class="mt-1 text-xs text-gray-500">
								{$t('evaluationConfig.thresholds.percentages.successDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>
		</div>
	</div>

	<div class="space-y-4">
		<h3 class="text-lg font-semibold">{$t('evaluationConfig.thresholds.points.title')}</h3>

		<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
			<div class="space-y-4">
				<Form.Field form={evaluationForm} name="pointsRangeMin">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('evaluationConfig.thresholds.points.min')}</Form.Label>
							<div class="relative">
								<Input
									{...props}
									type="number"
									bind:value={$evalFormStore.pointsRangeMin}
									class={$evalErrors.pointsRangeMin ? 'border-red-500' : ''}
									placeholder={$t('evaluationConfig.thresholds.points.minPlaceholder')}
								/>
							</div>
							{#if $evalErrors.pointsRangeMin}
								<div class="mt-1 text-xs text-red-500">
									{$evalErrors.pointsRangeMin}
								</div>
							{/if}
							<Form.Description class="mt-1 text-xs text-gray-500">
								{$t('evaluationConfig.thresholds.points.minDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>

			<div class="space-y-4">
				<Form.Field form={evaluationForm} name="pointsRangeMax">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('evaluationConfig.thresholds.points.max')}</Form.Label>
							<div class="relative">
								<Input
									{...props}
									type="number"
									bind:value={$evalFormStore.pointsRangeMax}
									class={$evalErrors.pointsRangeMax ? 'border-red-500' : ''}
									placeholder={$t('evaluationConfig.thresholds.points.maxPlaceholder')}
									min={$evalFormStore.pointsRangeMin ? $evalFormStore.pointsRangeMin + 1 : 1}
								/>
							</div>
							{#if $evalErrors.pointsRangeMax}
								<div class="mt-1 text-xs text-red-500">
									{$evalErrors.pointsRangeMax}
								</div>
							{/if}
							<Form.Description class="mt-1 text-xs text-gray-500">
								{$t('evaluationConfig.thresholds.points.maxDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>
		</div>
	</div>

	<div class="space-y-4">
		<h3 class="text-lg font-semibold">
			{$t('evaluationConfig.thresholds.pointsThresholds.title')}
		</h3>

		<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
			<div class="space-y-4">
				<Form.Field form={evaluationForm} name="pointsAvgThreshold">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('evaluationConfig.thresholds.pointsThresholds.average')}</Form.Label>
							<div class="relative">
								<Input
									{...props}
									type="number"
									bind:value={$evalFormStore.pointsAvgThreshold}
									class={$evalErrors.pointsAvgThreshold ? 'border-red-500' : ''}
									placeholder={$t(
										'evaluationConfig.thresholds.pointsThresholds.averagePlaceholder'
									)}
									min={$evalFormStore.pointsRangeMin}
									max={$evalFormStore.pointsRangeMax}
								/>
							</div>
							{#if $evalErrors.pointsAvgThreshold}
								<div class="mt-1 text-xs text-red-500">
									{$evalErrors.pointsAvgThreshold}
								</div>
							{/if}
							<Form.Description class="mt-1 text-xs text-gray-500">
								{$t('evaluationConfig.thresholds.pointsThresholds.averageDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>

			<div class="space-y-4">
				<Form.Field form={evaluationForm} name="pointsSuccessThreshold">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>{$t('evaluationConfig.thresholds.pointsThresholds.success')}</Form.Label>
							<div class="relative">
								<Input
									{...props}
									type="number"
									bind:value={$evalFormStore.pointsSuccessThreshold}
									class={$evalErrors.pointsSuccessThreshold ? 'border-red-500' : ''}
									placeholder={$t(
										'evaluationConfig.thresholds.pointsThresholds.successPlaceholder'
									)}
									min={$evalFormStore.pointsAvgThreshold
										? $evalFormStore.pointsAvgThreshold + 1
										: $evalFormStore.pointsRangeMin}
									max={$evalFormStore.pointsRangeMax}
								/>
							</div>
							{#if $evalErrors.pointsSuccessThreshold}
								<div class="mt-1 text-xs text-red-500">
									{$evalErrors.pointsSuccessThreshold}
								</div>
							{/if}
							<Form.Description class="mt-1 text-xs text-gray-500">
								{$t('evaluationConfig.thresholds.pointsThresholds.successDescription')}
							</Form.Description>
						{/snippet}
					</Form.Control>
				</Form.Field>
			</div>
		</div>
	</div>
</div>
