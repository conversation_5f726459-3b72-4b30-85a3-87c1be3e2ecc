<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { t } from '$lib/translations';

	let { evaluationForm } = $props();

	const { form: evalFormStore, errors: evalErrors } = evaluationForm;
</script>

<div class="space-y-6 p-4">
	<h3 class="text-lg font-semibold">{$t('evaluationConfig.weights.title')}</h3>

	<div class="space-y-6">
		<div class="space-y-4">
			<div class="grid gap-4">
				<div class="flex items-center justify-between rounded-lg bg-gray-50 p-3">
					<div>
						<span class="font-medium">{$t('questions.evaluationType.yesno')}</span>
					</div>
					<div class="text-right">
						<div class="text-sm text-gray-600">
							{$t('evaluationConfig.weights.yesNo.values')}
						</div>
						<div class="text-xs text-gray-500">{$t('evaluationConfig.weights.fixed')}</div>
					</div>
				</div>

				<div class="flex items-center justify-between rounded-lg bg-gray-50 p-3">
					<div>
						<span class="font-medium">{$t('questions.evaluationType.oknok')}</span>
					</div>
					<div class="text-right">
						<div class="text-sm text-gray-600">
							{$t('evaluationConfig.weights.okNokNa.values')}
						</div>
						<div class="text-xs text-gray-500">{$t('evaluationConfig.weights.fixed')}</div>
					</div>
				</div>

				<div class="flex items-center justify-between rounded-lg bg-blue-50 p-3">
					<div>
						<span class="font-medium">{$t('questions.evaluationModes.withReservations')}</span>
						<p class="text-sm text-gray-600">
							{$t('evaluationConfig.weights.withReservations.description')}
						</p>
					</div>
					<div class="w-32">
						<Form.Field form={evaluationForm} name="evalWeight_wReservations">
							<Form.Control>
								{#snippet children({ props })}
									<div class="relative">
										<Input
											{...props}
											type="number"
											step="0.1"
											min="0"
											max="1"
											bind:value={$evalFormStore.evalWeight_wReservations}
											class={$evalErrors.evalWeight_wReservations ? 'border-red-500' : ''}
											placeholder={$t('evaluationConfig.weights.withReservations.placeholder')}
										/>
									</div>
									{#if $evalErrors.evalWeight_wReservations}
										<div class="mt-1 text-xs text-red-500">
											{$evalErrors.evalWeight_wReservations}
										</div>
									{/if}
								{/snippet}
							</Form.Control>
						</Form.Field>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
