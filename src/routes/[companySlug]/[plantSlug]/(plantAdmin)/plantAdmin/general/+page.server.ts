import { PlantAdminService } from '$lib/server/services/tenants';
import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { createPlantAdminGeneralBreadcrumbs } from './_utils/createBreadcrumbs';
import { plantGeneralSettingsSchema } from '$lib/schemas/plantGeneralSettings';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';

export const load: PageServerLoad = async ({ parent, locals }) => {
	if (!locals.user) {
		throw error(401, 'Unauthorized user');
	}

	const { context } = await parent();

	const [plantGeneralSettings] = await Promise.all([
		PlantAdminService.getPlantGeneralSettings(context.plantId)
	]);

	// Create form with initial data
	const form = await superValidate(
		{
			eKaizenFormURL: plantGeneralSettings?.eKaizenFormURL || '',
			tasksEnabled: plantGeneralSettings?.tasksEnabled || false,
			supportedLanguages: plantGeneralSettings?.supportedLanguages || ['en'],
			defaultLanguage: plantGeneralSettings?.defaultLanguage || 'en'
		},
		zod(plantGeneralSettingsSchema)
	);

	return {
		plantGeneralSettings,
		breadcrumbs: createPlantAdminGeneralBreadcrumbs(context.companySlug, context.plantSlug),
		form
	};
};

export const actions: Actions = {
	updateGeneralSettings: async ({ request, params, locals }) => {
		if (!locals.user) {
			throw error(401, 'Unauthorized user');
		}

		const { plantId } = await validatePlantCompanyAccessLight(
			params.companySlug!,
			params.plantSlug!,
			locals.user
		);

		const form = await superValidate(request, zod(plantGeneralSettingsSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await PlantAdminService.updatePlantGeneralSettings(plantId, {
				eKaizenFormURL: form.data.eKaizenFormURL || undefined,
				tasksEnabled: form.data.tasksEnabled,
				supportedLanguages: form.data.supportedLanguages,
				defaultLanguage: form.data.defaultLanguage
			});

			return {
				form,
				success: true
			};
		} catch (error) {
			console.error('Error updating plant general settings:', error);
			return fail(500, {
				form,
				success: false
			});
		}
	}
};
