<script lang="ts">
	import { t } from '$lib/translations/index.js';
	import { fade } from 'svelte/transition';
	import { Input } from '$lib/components/ui/input';
	import { Switch } from '$lib/components/ui/switch';
	import * as Select from '$lib/components/ui/select';
	import * as Form from '$lib/components/ui/form';
	import { plantGeneralSettingsSchema } from '$lib/schemas/plantGeneralSettings';
	import { toast } from 'svelte-sonner';
	import { useForm } from '$lib/hooks/superformValidation';
	import { availableLanguages } from '$lib/constants/languages';
	import { cn } from '$lib/utils';

	let { data }: { data: { form: any } } = $props();

	const form = useForm(
		data.form,
		plantGeneralSettingsSchema,
		'form',
		() => {
			toast.success($t('plantAdmin.general.successMessage'));
		},
		undefined,
		undefined,
		false
	);

	const { form: formData, enhance, errors } = form;

	let uniqueSupportedLanguages = $derived(Array.from(new Set($formData.supportedLanguages || [])));

	function handleSupportedLanguagesChange(value: string[] | string) {
		let newSupported = Array.isArray(value) ? value : value ? [value] : [];
		newSupported = Array.from(new Set(newSupported)); // Remove duplicates
		$formData.supportedLanguages = newSupported;

		//clear supported languages error
		if (newSupported.length > 0 && $errors.supportedLanguages) {
			$errors.supportedLanguages = undefined;
		}

		if (newSupported.length > 0 && !newSupported.includes($formData.defaultLanguage)) {
			$formData.defaultLanguage = newSupported[0];
			if ($errors.defaultLanguage) {
				$errors.defaultLanguage = undefined;
			}
		} else if (newSupported.length === 0) {
			$formData.defaultLanguage = '';
		}
	}

	let defaultLanguageOptions = $derived(
		availableLanguages.filter((lang) => uniqueSupportedLanguages.includes(lang.value))
	);
</script>

<svelte:head>
	<title>LeanAudit - {$t('plantAdmin.general.title')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9"
>
	<div class="flex items-center justify-between p-2">
		<h2 class="text-2xl font-bold">{$t('plantAdmin.general.title')}</h2>
	</div>

	<div class="flex flex-1 flex-col">
		<form
			method="POST"
			action="?/updateGeneralSettings"
			use:enhance
			class="flex flex-1 flex-col overflow-auto px-8 py-6"
		>
			<div class="w-full space-y-12">
				<div class="space-y-6">
					<!-- eKaizen URL -->
					<h3 class="text-xl font-bold text-gray-900">
						{$t('plantAdmin.general.sectionsTitle.ekaizen')}
					</h3>
					<div class="space-y-3">
						<Form.Field {form} name="eKaizenFormURL">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label class="text-sm font-medium text-gray-700"
										>{$t('plantAdmin.general.eKaizenUrl')}</Form.Label
									>
									<Input
										{...props}
										type="url"
										placeholder={$t('plantAdmin.general.eKaizenUrlPlaceholder')}
										bind:value={$formData.eKaizenFormURL}
										class=" max-w-2xl text-lg {$errors.eKaizenFormURL
											? 'border-red-500'
											: 'border-gray-200'}"
									/>
									{#if $errors.eKaizenFormURL}
										<div class="mt-2 text-sm text-red-500">
											{$errors.eKaizenFormURL}
										</div>
									{/if}
								{/snippet}
							</Form.Control>
						</Form.Field>
					</div>
				</div>

				<!-- Tasks Enabled -->
				<div class="space-y-6">
					<h3 class="text-xl font-bold text-gray-900">
						{$t('plantAdmin.general.sectionsTitle.tasks')}
					</h3>

					<div class="space-y-3">
						<Form.Field {form} name="tasksEnabled">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label class="text-sm font-medium text-gray-700"
										>{$t('plantAdmin.general.tasksEnabled')}</Form.Label
									>
									<div class="flex items-center gap-4">
										<Switch
											bind:checked={$formData.tasksEnabled}
											{...props}
											class={cn(
												'relative h-6 w-12 cursor-pointer rounded-full border-0 shadow-sm transition-all duration-500 ease-in-out',
												'data-[state=checked]:bg-[#414E6B] data-[state=unchecked]:bg-[#B1B7C3]',
												'[&>[data-slot="switch-thumb"]]:h-5 [&>[data-slot="switch-thumb"]]:w-5',
												'[&>[data-slot="switch-thumb"]]:shadow-lg [&>[data-slot="switch-thumb"]]:transition-transform',
												'[&>[data-slot="switch-thumb"]]:duration-500 [&>[data-slot="switch-thumb"]]:ease-in-out',
												'[&>[data-slot="switch-thumb"]]:absolute [&>[data-slot="switch-thumb"]]:top-0.5 [&>[data-slot="switch-thumb"]]:rounded-full',
												'[&>[data-slot="switch-thumb"]]:data-[state=checked]:translate-x-6 [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:translate-x-0.5',
												'[&>[data-slot="switch-thumb"]]:data-[state=checked]:bg-white [&>[data-slot="switch-thumb"]]:data-[state=unchecked]:bg-[#2E384F]',
												$errors.tasksEnabled ? 'ring-2 ring-red-500' : ''
											)}
										/>

										<span
											class={cn(
												'rounded-lg px-4 py-1.5 text-center text-sm font-semibold uppercase transition-all duration-500 ease-in-out',
												'inline-block min-w-[100px]',
												$formData.tasksEnabled
													? 'bg-[#414E6B] text-white shadow-sm'
													: 'bg-gray-100 text-gray-600'
											)}
										>
											{$formData.tasksEnabled
												? $t('auditTypes.list.activeStates.true')
												: $t('auditTypes.list.activeStates.false')}
										</span>

										<span class="max-w-sm text-xs text-gray-600"
											>{$t('plantAdmin.general.tasksDescription')}</span
										>

										<input type="hidden" name={props.name} value={$formData.tasksEnabled} />
									</div>
									{#if $errors.tasksEnabled}
										<div class="mt-2 text-sm text-red-500">
											{$errors.tasksEnabled}
										</div>
									{/if}
								{/snippet}
							</Form.Control>
						</Form.Field>
					</div>
				</div>

				<div class="space-y-6">
					<h3 class="text-xl font-bold text-gray-900">
						{$t('plantAdmin.general.sectionsTitle.languages')}
					</h3>

					<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
						<!-- Supported Languages -->
						<div class="space-y-3">
							<Form.Field {form} name="supportedLanguages">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="text-sm font-medium text-gray-700"
											>{$t('plantAdmin.general.supportedLanguages')}</Form.Label
										>
										<Select.Root
											type="multiple"
											bind:value={uniqueSupportedLanguages}
											name={props.name}
											onValueChange={handleSupportedLanguagesChange}
										>
											<Select.Trigger
												class="flex  w-full items-center rounded-md border bg-white px-4 py-3 text-lg {$errors.supportedLanguages
													? 'border-red-500'
													: 'border-gray-200'}"
											>
												{#if uniqueSupportedLanguages && uniqueSupportedLanguages.length > 0}
													<span class="truncate text-sm">
														{uniqueSupportedLanguages
															.map(
																(langValue) =>
																	availableLanguages.find((lang) => lang.value === langValue)
																		?.label ?? langValue
															)
															.join(', ')}
													</span>
												{:else}
													<span class="text-muted-foreground text-sm">
														{$t('plantAdmin.general.placeholders.supportedLanguagesPlaceholder')}
													</span>
												{/if}
											</Select.Trigger>
											<Select.Content>
												{#each availableLanguages as language}
													<Select.Item value={language.value}>
														{language.label}
													</Select.Item>
												{/each}
											</Select.Content>
										</Select.Root>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors class="mt-2 space-y-1 text-sm text-red-500" />
							</Form.Field>
						</div>

						<!-- Default Language -->
						<div class="space-y-3">
							<Form.Field {form} name="defaultLanguage">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="text-sm font-medium text-gray-700"
											>{$t('plantAdmin.general.defaultLanguage')}</Form.Label
										>
										<Select.Root
											type="single"
											bind:value={$formData.defaultLanguage}
											name={props.name}
										>
											<Select.Trigger
												class="flex  w-full items-center rounded-md border bg-white px-4 py-3 text-lg {$errors.defaultLanguage
													? 'border-red-500'
													: 'border-gray-200'}"
											>
												{#if $formData.defaultLanguage}
													<span class="text-sm">
														{defaultLanguageOptions.find(
															(lang) => lang.value === $formData.defaultLanguage
														)?.label || $formData.defaultLanguage}
													</span>
												{:else}
													<span class="text-muted-foreground text-sm">
														{$t('plantAdmin.general.placeholders.defaultLanguagePlaceholder')}
													</span>
												{/if}
											</Select.Trigger>
											<Select.Content>
												{#each defaultLanguageOptions as language}
													<Select.Item value={language.value}>
														{language.label}
													</Select.Item>
												{/each}
											</Select.Content>
										</Select.Root>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors class="mt-2 space-y-1 text-sm text-red-500" />
							</Form.Field>
						</div>
					</div>
				</div>

				<!-- Hidden inputs for supported langs -->
				{#each uniqueSupportedLanguages as langValue (langValue)}
					<input type="hidden" name="supportedLanguages" value={langValue} />
				{/each}
			</div>

			<!-- Save Button -->
			<div class="mt-auto flex justify-end border-t pt-6">
				<Form.Button
					type="submit"
					class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				>
					{$t('common.buttons.save')}
				</Form.Button>
			</div>
		</form>
	</div>
</div>
