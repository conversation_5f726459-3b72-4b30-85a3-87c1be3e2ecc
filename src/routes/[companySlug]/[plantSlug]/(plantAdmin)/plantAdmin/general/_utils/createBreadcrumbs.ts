import type { BreadcrumbModel } from '$lib/models/breadcrumbsModel';

export function createPlantAdminGeneralBreadcrumbs(
	companySlug: string,
	plantSlug: string
): BreadcrumbModel[] {
	const breadcrumbs: BreadcrumbModel[] = [
		{
			kind: 'static',
			label: 'common.navigation.leanAudit',
			isLeanAudit: true
		},
		{
			kind: 'static',
			label: 'common.navigation.plantAdmin',
			href: `/${companySlug}/${plantSlug}/plantAdmin/general`
		},
		{
			kind: 'static',
			label: 'plantAdmin.navigation.general'
		}
	];

	return breadcrumbs;
}
