import { TagsService } from '$lib/server/services/tags';
import { superValidate } from 'sveltekit-superforms';
import type { PageServerLoad } from './$types';
import { createPlantAdminTagsBreadcrumbs } from './_utils/createBreadcrumbs';
import { zod } from 'sveltekit-superforms/adapters';
import { tagsSchema } from '$lib/schemas/tags';
import { validatePlantCompanyAccessLight } from '$lib/server/utils/plantValidation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ parent }) => {
	const { context } = await parent();

	const [tags, tagsForm] = await Promise.all([
		await TagsService.getTags(context.plantId),
		superValidate(zod(tagsSchema))
	]);

	return {
		tags,
		tagsForm,
		breadcrumbs: createPlantAdminTagsBreadcrumbs(context.companySlug, context.plantSlug)
	};
};

export const actions = {
	createTag: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(tagsSchema));

		if (!form.valid) {
			return { form };
		}

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);
			await TagsService.createTag(form.data, context.plantId);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to create tag:', error);
			return fail(500, {
				form,
				message: 'Failed to create tag. Please try again later.'
			});
		}
	},
	updateTag: async ({ request, params, locals }) => {
		const form = await superValidate(request, zod(tagsSchema));

		if (!form.valid) {
			return { form };
		}

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);
			await TagsService.updateTag(context.plantId, form.data);
			return { form, success: true };
		} catch (error) {
			console.error('Failed to update tag:', error);
			return fail(500, {
				form,
				message: 'Failed to update tag. Please try again later.'
			});
		}
	},
	deleteTag: async ({ request, params, locals }) => {
		const formData = await request.formData();
		const tagId = formData.get('id') as string;

		try {
			const context = await validatePlantCompanyAccessLight(
				params.companySlug!,
				params.plantSlug!,
				locals.user!
			);
			await TagsService.deleteTag(tagId, context.plantId);
			return { success: true };
		} catch (error) {
			console.error('Failed to delete tag:', error);
			return fail(500, {
				message: 'Failed to delete tag. Please try again later.'
			});
		}
	}
};
