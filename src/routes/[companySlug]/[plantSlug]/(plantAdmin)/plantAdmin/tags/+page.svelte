<script lang="ts">
	import DataTable from '$lib/components/ui/data-table/data-table.svelte';
	import ButtonIcon from '$lib/customComponents/button-icon.svelte';
	import CustomDialog from '$lib/customComponents/custom-dialog.svelte';
	import SearchInput from '$lib/customComponents/search-input.svelte';
	import { t } from '$lib/translations/index.js';
	import { Plus } from '@lucide/svelte';
	import { fade } from 'svelte/transition';
	import { toast } from 'svelte-sonner';
	import { useForm } from '$lib/hooks/superformValidation.js';
	import { tagsSchema } from '$lib/schemas/tags.js';

	import TagForm from './_components/tag-form.svelte';
	import { createColumns } from './_components/columns';

	let { data } = $props();

	let dialogOpen = $state(false);
	let globalFilter = $state('');
	let isEdit = $state(false);
	let selectedTag = $state(null);

	function handleSearch(value: string) {
		globalFilter = value;
	}

	const form = useForm(data.tagsForm, tagsSchema, 'form', () => {
		dialogOpen = false;
		if (isEdit) {
			toast.success($t('plantAdmin.tags.toast.saved'));
		} else {
			toast.success($t('plantAdmin.tags.toast.created'));
		}
		setTimeout(() => {
			form.reset();
			isEdit = false;
			selectedTag = null;
		}, 200);
	});

	function closeDialog() {
		dialogOpen = false;
		setTimeout(() => {
			form.reset();
			isEdit = false;
			selectedTag = null;
		}, 400);
	}

	function openDialog() {
		form.reset();
		isEdit = false;
		selectedTag = null;
		dialogOpen = true;
	}

	function openEditDialog(tag: any) {
		isEdit = true;
		selectedTag = tag;
		dialogOpen = true;
	}

	const columnsWithCallbacks = createColumns({
		onEdit: openEditDialog
	});
</script>

<svelte:head>
	<title>LeanAudit - {$t('common.navigation.plantAdmin')}</title>
</svelte:head>

<div
	in:fade={{ duration: 300 }}
	class="mt-5 mb-7 flex h-[calc(100vh-140px)] w-full flex-col rounded-2xl bg-white px-8 py-9"
>
	<div class="mb-6 flex items-center justify-between">
		<div class="flex-1">
			<div class="relative flex max-w-sm">
				<SearchInput value={globalFilter} onSearch={handleSearch} />
			</div>
		</div>
		<div class="ml-4">
			<ButtonIcon onClickAction={openDialog} Icon={Plus} />
		</div>
	</div>

	<div class="flex-1 overflow-hidden">
		<DataTable data={data.tags} columns={columnsWithCallbacks} {globalFilter} />
	</div>
</div>

<CustomDialog
	open={dialogOpen}
	title={isEdit ? $t('plantAdmin.tags.dialog.editTitle') : $t('plantAdmin.tags.dialog.createTitle')}
	onClose={closeDialog}
>
	<TagForm {form} onCancel={closeDialog} {isEdit} tag={selectedTag} />
</CustomDialog>
