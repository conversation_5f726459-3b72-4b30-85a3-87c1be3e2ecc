import { renderComponent } from '$lib/components/ui/data-table';
import type { ColumnDef } from '@tanstack/table-core';
import { t } from '$lib/translations';
import { get } from 'svelte/store';
import DataTableActions from './data-table-actions.svelte';
import Cell from '$lib/customComponents/tableFormat/general-cell.svelte';
import type { TagDTO } from '$lib/DTO/tags';
import TagsCell from '$lib/customComponents/tableFormat/tags-cell.svelte';
import CustomHeader from '$lib/customComponents/custom-header.svelte';

export function createColumns(callbacks: {
	onEdit: (tag: TagDTO) => void;
}): ColumnDef<TagDTO, unknown>[] {
	return [
		{
			id: 'name',
			accessorKey: 'name',
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('plantAdmin.tags.list.name'),
					column
				}),
			cell: ({ row }) => {
				const name = row.getValue('name') as string;
				return renderComponent(Cell, {
					content: name
				});
			},
			enableSorting: true
		},

		{
			id: 'preview',
			accessorKey: 'name', //sorting by name for preview
			header: ({ column }) =>
				renderComponent(CustomHeader, {
					text: get(t)('plantAdmin.tags.list.preview'),
					column
				}),
			cell: ({ row }) => {
				return renderComponent(TagsCell, { tags: [row.original] });
			},
			enableSorting: true
		},

		{
			id: 'actions',
			enableSorting: false,
			enableHiding: false,
			meta: {
				headerClass: 'text-right',
				cellClass: 'text-right'
			},
			cell: ({ row }) =>
				renderComponent(DataTableActions, {
					row: row.original,
					onEdit: callbacks.onEdit
				})
		}
	];
}
