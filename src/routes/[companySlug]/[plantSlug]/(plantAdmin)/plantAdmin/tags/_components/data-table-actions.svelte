<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Edit, Ellipsis, Trash, Wrench } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations';
	import DeleteDialog from '$lib/customComponents/delete-dialog.svelte';
	import { toast } from 'svelte-sonner';
	import type { TagDTO } from '$lib/DTO/tags';

	let { row, onEdit }: { row: TagDTO; onEdit: (tag: TagDTO) => void } = $props();
	let open = $state(false);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button class="bg-secondary size-8">
			<Ellipsis />
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="bg-secondary">
		<DropdownMenu.Group class="text-white uppercase">
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => onEdit(row)}
			>
				<Edit class="text-white" />
				<span>{$t('common.buttons.edit')}</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
				class="data-[highlighted]:hover:bg-primary/20 text-xs data-highlighted:hover:text-white"
				onclick={() => (open = true)}
			>
				<Trash class="size-4 text-white" />
				<span>{$t('common.buttons.delete')}</span>
			</DropdownMenu.Item>
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<DeleteDialog
	{open}
	id={row.id!}
	title={$t('plantAdmin.tags.dialog.deleteTitle')}
	description={$t('plantAdmin.tags.dialog.deleteDescription')}
	formAction="?/deleteTag"
	onDelete={() => toast.success($t('plantAdmin.tags.toast.deleted'))}
	onClose={() => (open = false)}
/>
