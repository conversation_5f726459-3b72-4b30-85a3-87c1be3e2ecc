<script lang="ts">
	import * as Form from '$lib/components/ui/form/index';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { t } from '$lib/translations';
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import type { AuditorDTO } from '$lib/DTO/auditor';
	import type { AuditTypeDTO } from '$lib/DTO/auditTypes/auditTypes';
	import type { WorkplaceDTO } from '$lib/DTO/workplaces/workplaces';
	import type { DateValue } from '@internationalized/date';
	import { Button } from '$lib/components/ui/button';
	import { type AuditListDTO } from '$lib/DTO/audits/audits';
	import { parseDate, today, getLocalTimeZone } from '@internationalized/date';
	import DatePickerWrapper from '$lib/components/ui/date-picker-wrapper.svelte';
	import { page } from '$app/state';

	let {
		form,
		audits = [],
		auditors,
		auditTypes,
		workplaces,
		onClose,
		isEditMode,
		selectedAuditId
	} = $props<{
		form: any;
		audits?: AuditListDTO[];
		onClose: () => void;
		isEditMode: boolean;
		selectedAuditId: string | null;
		auditors: AuditorDTO[];
		auditTypes: AuditTypeDTO[];
		workplaces: WorkplaceDTO[];
	}>();

	const { form: formData, enhance, errors } = form;

	let auditTypeOpen = $state(false);
	let workplaceOpen = $state(false);
	let responsiblePersonOpen = $state(false);

	let datePickerOpen = $state(false);
	let datePickerValue = $state<DateValue>(today(getLocalTimeZone()));

	// Load audit data when component mounts
	$effect(() => {
		if (isEditMode && selectedAuditId) {
			const auditToEdit = audits.find((audit: AuditListDTO) => audit.id === selectedAuditId);
			if (auditToEdit) {
				$formData.id = selectedAuditId;
				$formData.auditTypeId = auditToEdit.auditType?.id || '';
				$formData.workplaceId = auditToEdit.workplace?.id || '';
				$formData.responsiblePersonId = auditToEdit.auditor?.id || '';
				$formData.plannedDate = auditToEdit.plannedDate;

				if (auditToEdit.plannedDate) {
					try {
						const date = new Date(auditToEdit.plannedDate);
						datePickerValue = parseDate(
							`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
								date.getDate()
							).padStart(2, '0')}`
						);
					} catch {
						datePickerValue = today(getLocalTimeZone());
					}
				}
			}
		} else if (!$formData.plannedDate) {
			datePickerValue = today(getLocalTimeZone());
			const date = datePickerValue.toDate(getLocalTimeZone());
			$formData.plannedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
				date.getDate()
			).padStart(2, '0')}`;
		}
	});

	function onValueChange(value: DateValue | undefined) {
		if (value) {
			datePickerValue = value;
			const date = value.toDate(getLocalTimeZone());
			$formData.plannedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
				date.getDate()
			).padStart(2, '0')}`;
		}
		datePickerOpen = false;
	}

	// Debug submit
	function handleSubmit(event: SubmitEvent) {
		const requiredFields = ['auditTypeId', 'workplaceId', 'responsiblePersonId', 'plannedDate'];
		const missingFields = requiredFields.filter((field) => !$formData[field]);

		if (missingFields.length > 0) {
			event.preventDefault();

			missingFields.forEach((field) => {
				$errors[field] = [$t('errors.validation.required')];
			});

			return false;
		}
	}
</script>

<form
	method="POST"
	action={`/${page.params.companySlug}/${page.params.plantSlug}/audits?/${isEditMode ? 'updateTag' : 'createTag'}`}
	use:enhance={() => {
		return async ({
			result,
			update
		}: {
			result: { type: string };
			update: () => Promise<void>;
		}) => {
			if (result.type === 'success') {
				onClose();
			} else if (result.type === 'failure') {
				await update();
			} else {
				await update();
			}
		};
	}}
	onsubmit={handleSubmit}
>
	<div class="space-y-2">
		{#if isEditMode && selectedAuditId}
			<input type="hidden" name="id" value={selectedAuditId} />
		{/if}

		<div class="relative pb-6">
			<Form.Field {form} name="auditTypeId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>{$t('audits.newInstance.auditTypeLabel')}</Form.Label>
						<Popover.Root bind:open={auditTypeOpen}>
							<Popover.Trigger
								class={cn(
									'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50',
									'items-center justify-between',
									!$formData.auditTypeId && 'text-muted-foreground',
									$errors.auditTypeId && 'border-red-500'
								)}
								role="combobox"
								{...props}
								disabled={isEditMode}
							>
								{#if $formData.auditTypeId && auditTypes}
									{auditTypes.find((type: AuditTypeDTO) => type.id === $formData.auditTypeId)
										?.name || $t('audits.newInstance.auditTypePlaceholder')}
								{:else}
									{$t('audits.newInstance.auditTypePlaceholder')}
								{/if}
								<ChevronsUpDown class="h-4 w-4 opacity-50" />
							</Popover.Trigger>
							<input hidden value={$formData.auditTypeId} name={props.name} />
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								align="start"
								sideOffset={5}
							>
								<Command.Root class="w-full">
									<Command.Input
										autofocus
										placeholder={$t('audits.newInstance.auditTypePlaceholder')}
										class="h-9"
									/>
									<Command.List>
										<Command.Empty>{$t('audits.setupSettings.noAuditTypeFound')}</Command.Empty>
										<Command.Group>
											{#each auditTypes as auditType}
												<Command.Item
													value={auditType.name}
													onSelect={() => {
														$formData.auditTypeId = auditType.id;
														auditTypeOpen = false;
													}}
												>
													<span>{auditType.name}</span>
													{#if auditType.id === $formData.auditTypeId}
														<Check class="ml-auto h-4 w-4" />
													{:else}
														<div class="ml-auto h-4 w-4"></div>
													{/if}
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors class="text-destructive absolute right-0 bottom-0 text-xs" />
			</Form.Field>
		</div>

		<div class="flex justify-end gap-4">
			<Button
				type="button"
				variant="outline"
				class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
				onclick={() => {
					onClose();
				}}
			>
				{$t('common.buttons.cancel')}
			</Button>

			<Form.Button
				type="submit"
				class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			>
				{isEditMode ? $t('common.buttons.save') : $t('common.buttons.create')}
			</Form.Button>
		</div>
	</div>
</form>
