<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import { t } from '$lib/translations/index.js';
	import TagPreview from '$lib/customComponents/tags/tag-preview.svelte';

	let {
		form,
		onCancel,
		isEdit = false,
		tag = null
	}: {
		form: any;
		onCancel: () => void;
		isEdit?: boolean;
		tag?: any;
	} = $props();

	const { form: formData, enhance, errors } = form;

	$effect(() => {
		if (isEdit && tag.id) {
			$formData.name = tag.name;
			$formData.color = tag.color;
			$formData.textColor = tag.textColor;
			$formData.id = tag.id;
		}
		if (!isEdit) {
			$formData.name = '';
			$formData.color = '#3B82F6';
			$formData.textColor = 'white';
			$formData.id = undefined;
		}
	});
</script>

<form method="POST" action={isEdit ? '?/updateTag' : '?/createTag'} use:enhance class="space-y-6">
	{#if isEdit && $formData.id}
		<input type="hidden" name="id" value={$formData.id} />
	{/if}

	<input type="hidden" name="color" bind:value={$formData.color} />
	<input type="hidden" name="textColor" bind:value={$formData.textColor} />

	<div class="space-y-3">
		<Form.Field {form} name="name">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium text-gray-700">
						{$t('plantAdmin.tags.form.name')}
					</Form.Label>
					<Input
						{...props}
						bind:value={$formData.name}
						class="h-10 text-lg {errors.name ? 'border-red-500' : 'border-gray-200'}"
						placeholder={$t('plantAdmin.tags.form.namePlaceholder')}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="text-destructive absolute right-8 text-xs" />
		</Form.Field>
	</div>

	<div class="space-y-3">
		<Form.Field {form} name="color">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium text-gray-700">
						{$t('plantAdmin.tags.form.backgroundColor')}
					</Form.Label>
					<div class="flex items-center gap-3">
						<Input
							{...props}
							type="color"
							bind:value={$formData.color}
							class="h-10 w-20 cursor-pointer border-gray-200"
						/>
						<Input
							type="text"
							bind:value={$formData.color}
							class="h-10 border-gray-200 text-lg"
							placeholder="#3B82F6"
						/>
					</div>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="text-destructive absolute right-8 text-xs" />
		</Form.Field>
	</div>

	<div class="space-y-3">
		<Form.Field {form} name="textColor">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label class="text-sm font-medium text-gray-700"
						>{$t('plantAdmin.tags.form.textColor')}</Form.Label
					>
					<div class="flex items-center gap-3">
						<Select.Root type="single" bind:value={$formData.textColor} name={props.name}>
							<Select.Trigger
								{...props}
								class="flex h-10 w-full items-center rounded-md border bg-white px-4 py-3 {errors.textColor
									? 'border-red-500'
									: 'border-gray-200'}"
							>
								{$formData.textColor
									? $t(`plantAdmin.tags.form.textColors.${$formData.textColor}`)
									: $t('plantAdmin.tags.form.textColorPlaceholder')}
							</Select.Trigger>
							<Select.Content>
								<Select.Item value="white"
									>{$t('plantAdmin.tags.form.textColors.white')}</Select.Item
								>
								<Select.Item value="black"
									>{$t('plantAdmin.tags.form.textColors.black')}</Select.Item
								>
							</Select.Content>
						</Select.Root>
					</div>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors class="text-destructive absolute right-8 text-xs" />
		</Form.Field>
	</div>

	<div class="space-y-3">
		<div class="text-sm">{$t('plantAdmin.tags.form.tagPreview')}</div>
		<div
			class="flex flex-row items-center justify-center rounded-xl border border-gray-200 bg-gray-50 p-8"
		>
			{#if $formData.name !== ''}
				<TagPreview
					text={$formData.name || 'Název tagu'}
					backgroundColor={$formData.color || '#ffffff'}
					textColor={$formData.textColor || 'black'}
				/>
			{:else}
				<div class="text-sm text-gray-400">{$t('plantAdmin.tags.form.tagPreviewDescription')}</div>
			{/if}
		</div>
	</div>

	<!-- Buttons -->
	<div class="flex justify-end gap-4 border-t pt-4">
		<Button
			type="button"
			variant="outline"
			class="bg-secondary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
			onclick={onCancel}
		>
			{$t('common.buttons.cancel')}
		</Button>

		<Button
			type="submit"
			class="bg-primary font-titillium min-w-[120px] rounded-xl p-7 text-sm font-semibold uppercase"
		>
			{isEdit ? $t('common.buttons.save') : $t('common.buttons.create')}
		</Button>
	</div>
</form>
