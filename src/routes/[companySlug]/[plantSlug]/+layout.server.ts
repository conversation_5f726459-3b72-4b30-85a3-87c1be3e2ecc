import { error, redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { CompanyService, PlantsService } from '$lib/server/services/tenants';
import { RolesService } from '$lib/server/services/roles';
import { Authorization } from '$lib/server/utils/authorization';

export const load: LayoutServerLoad = async ({ params, locals, parent }) => {
	const { companySlug, plantSlug } = params;

	if (!locals.user) {
		throw redirect(302, `/login`);
	}

	const { language } = await parent();

	// Get the company by code (slug)
	const company = await CompanyService.getCompanyBySlug(companySlug);
	if (!company) {
		throw error(404, {
			message: 'Company not found',
			code: 404
		});
	}

	// Get the plant by slug
	const plant = await PlantsService.getPlantBySlug(plantSlug);

	if (!plant) {
		throw error(404, {
			message: 'Plant not found',
			code: 404
		});
	}

	// Check if the plant belongs to the company
	if (plant.companyId !== company.id) {
		throw error(404, {
			message: 'Plant not found in this company',
			code: 404
		});
	}

	// Check if the user has access to plant
	const hasAccess = locals.user.plants.includes(plant.id);

	if (!hasAccess) {
		// Redirect to main plant instead of error
		if (locals.user.plants.length > 0) {
			const mainPlant = await PlantsService.getPlantById(locals.user.plants[0]);
			if (mainPlant) {
				// Get the correct company for the main plant
				const mainPlantCompany = await CompanyService.getCompanyById(mainPlant.companyId);
				if (mainPlantCompany) {
					return redirect(302, `/${mainPlantCompany.slug}/${mainPlant.slug}/dashboard`);
				}
			}
		}
		throw redirect(302, '/login');
	}

	const isSuperAdmin = locals.plantRoles?.some((pr) => pr.plantId === null);

	if (isSuperAdmin) {
		const superAdminRole = locals.plantRoles?.find((pr) => pr.plantId === null);
		if (superAdminRole) {
			const role = await RolesService.loadPlantRole(locals.user.id, null, superAdminRole.roleId);
			locals.role = role;
			locals.authorization = new Authorization(role?.permissions ?? {});
		}
	} else {
		const userPlantRole = locals.plantRoles?.find((pr) => pr.plantId === plant.id);
		if (!userPlantRole) {
			throw error(403, {
				message: 'You do not have any permissions in this plant',
				code: 403
			});
		}

		const role = await RolesService.loadPlantRole(locals.user.id, plant.id, userPlantRole.roleId);

		locals.role = role;
		locals.authorization = new Authorization(role?.permissions ?? {});
	}

	// Return the company, plant context, user and language for child routes
	return {
		language,
		company,
		plant,
		user: locals.user,
		role: locals.role,
		context: {
			companyId: company.id,
			companySlug: company.slug,
			plantId: plant.id,
			plantSlug: plant.slug
		}
	};
};
