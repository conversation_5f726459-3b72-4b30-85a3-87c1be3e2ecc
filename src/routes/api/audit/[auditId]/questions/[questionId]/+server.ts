import { json, error } from '@sveltejs/kit';
import { AuditEvaluateService } from '$lib/server/services/audits';
import { getFileSignedUrl } from '$lib/server/storage';

export async function PUT({ params, request }) {
	try {
		const { auditId, questionId } = params;
		const body = await request.json();

		const { evaluationValue, note, attachments, filesToRemove, realDuration } = body;

		if (!questionId) {
			throw error(400, 'Missing questionId');
		}

		// Save question answer with auto-save
		const result = await AuditEvaluateService.saveQuestionAnswer(
			auditId,
			questionId,
			evaluationValue ?? null,
			note || '',
			realDuration || 0, // Use provided duration or 0 for auto-save
			attachments || [],
			filesToRemove || [] // Pass files to remove
		);

		// Generate signed URLs for response
		const filesWithSignedUrls = await Promise.all(
			result.files.map(async (file) => {
				const signedUrl = await getFileSignedUrl(file.url);
				return {
					id: file.id,
					url: signedUrl,
					filename: file.filename,
					type: file.type
				};
			})
		);

		return json({
			success: true,
			files: filesWithSignedUrls
		});
	} catch (err) {
		console.error('Auto-save error:', err);

		if (err instanceof Error && 'status' in err) {
			throw err; // Re-throw SvelteKit errors
		}

		throw error(500, 'Auto-save failed');
	}
}

//Get files
export async function GET({ params }) {
	const { auditId, questionId } = params;
	if (!auditId || !questionId) throw error(400, 'Missing auditId or questionId');

	const answer = await AuditEvaluateService.getAuditAnswerForQuestion(auditId, questionId);

	// Vygeneruj presigned URL pro každý soubor
	const filesWithSignedUrls = answer?.files
		? await Promise.all(
				answer.files.map(async (file) => ({
					id: file.id,
					url: await getFileSignedUrl(file.url),
					filename: file.filename,
					type: file.type
				}))
			)
		: [];

	return json({
		files: filesWithSignedUrls
	});
}

//Delete attachment by ID
export async function DELETE({ params, url }) {
	try {
		const { auditId, questionId } = params;
		const attachmentId = url.searchParams.get('attachmentId');

		if (!auditId || !questionId || !attachmentId) {
			throw error(400, 'Missing auditId, questionId or attachmentId');
		}

		await AuditEvaluateService.deleteAttachment(auditId, questionId, attachmentId);

		return json({
			success: true,
			message: 'Attachment deleted successfully'
		});
	} catch (err) {
		console.error('Delete attachment error:', err);

		if (err instanceof Error && 'status' in err) {
			throw err; // Re-throw SvelteKit errors
		}

		throw error(500, 'Failed to delete attachment');
	}
}
