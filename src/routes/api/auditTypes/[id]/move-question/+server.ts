import { json } from '@sveltejs/kit';
import { db } from '$lib/db/db.server';
import { auditTypesTable } from '$lib/db/schema/audits';
import { eq } from 'drizzle-orm';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export async function POST({ params, request }) {
	try {
		const { questionId, sourceCategoryId, targetCategoryId } = await request.json();
		const { id: auditTypeId } = params;

		if (!questionId || !sourceCategoryId || !targetCategoryId) {
			return json({ success: false, message: 'Missing required parameters' }, { status: 400 });
		}

		const [auditType] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!auditType) {
			return json({ success: false, message: 'Audit type not found' }, { status: 404 });
		}

		const currentQuestions = { ...auditType.questions } as TemplateQuestions;

		if (!currentQuestions[sourceCategoryId]) {
			return json({ success: false, message: 'Source category not found' }, { status: 404 });
		}

		if (!currentQuestions[targetCategoryId]) {
			return json({ success: false, message: 'Target category not found' }, { status: 404 });
		}

		const questionIndex = currentQuestions[sourceCategoryId].questions.findIndex(
			(q) => q.questionId === questionId
		);

		if (questionIndex === -1) {
			return json(
				{ success: false, message: 'Question not found in source category' },
				{ status: 404 }
			);
		}

		const [question] = currentQuestions[sourceCategoryId].questions.splice(questionIndex, 1);

		currentQuestions[targetCategoryId].questions.push(question);

		await db
			.update(auditTypesTable)
			.set({ questions: currentQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId));

		return json({ success: true });
	} catch (error) {
		console.error('Failed to move question:', error);
		return json({ success: false, message: 'Failed to move question' }, { status: 500 });
	}
}
