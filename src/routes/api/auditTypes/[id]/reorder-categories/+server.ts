import { json } from '@sveltejs/kit';
import { db } from '$lib/db/db.server';
import { auditTypesTable } from '$lib/db/schema/audits';
import { eq } from 'drizzle-orm';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export async function PUT({ params, request }) {
	try {
		const { categoryOrder } = await request.json();
		const { id: auditTypeId } = params;

		if (!categoryOrder || !Array.isArray(categoryOrder)) {
			return json({ success: false, message: 'Missing required parameters' }, { status: 400 });
		}

		const [auditType] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!auditType) {
			return json({ success: false, message: 'Audit type not found' }, { status: 404 });
		}

		const currentQuestions = { ...auditType.questions } as TemplateQuestions;
		const updatedQuestions: TemplateQuestions = {};

		categoryOrder.forEach((categoryId, index) => {
			const newCategoryId = (index + 1).toString();

			if (currentQuestions[categoryId]) {
				updatedQuestions[newCategoryId] = currentQuestions[categoryId];
			}
		});

		await db
			.update(auditTypesTable)
			.set({ questions: updatedQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId))
			.returning();

		return json({ success: true });
	} catch (error) {
		console.error('Failed to update category order:', error);
		return json({ success: false, message: 'Failed to update category order' }, { status: 500 });
	}
}
