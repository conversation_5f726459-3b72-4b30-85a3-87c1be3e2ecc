import { json } from '@sveltejs/kit';
import { db } from '$lib/db/db.server';
import { auditTypesTable } from '$lib/db/schema/audits';
import { eq } from 'drizzle-orm';
import type { TemplateQuestions } from '$lib/schemas/audits/auditQuestions';

export async function POST({ params, request }) {
	try {
		const { questions, categoryId } = await request.json();
		const { id: auditTypeId } = params;

		if (!questions || !Array.isArray(questions) || !categoryId) {
			return json({ success: false, message: 'Missing required parameters' }, { status: 400 });
		}

		const [auditType] = await db
			.select()
			.from(auditTypesTable)
			.where(eq(auditTypesTable.id, auditTypeId));

		if (!auditType || !auditType.questions) {
			return json({ success: false, message: 'Audit type not found' }, { status: 404 });
		}

		const auditTypeQuestions = { ...auditType.questions } as TemplateQuestions;

		if (!auditTypeQuestions[categoryId]) {
			return json({ success: false, message: 'Category not found' }, { status: 404 });
		}

		auditTypeQuestions[categoryId].questions = questions.map((question) => ({
			questionId: question.questionId,
			required: question.required !== undefined ? question.required : true
		}));

		await db
			.update(auditTypesTable)
			.set({ questions: auditTypeQuestions, updatedAt: new Date() })
			.where(eq(auditTypesTable.id, auditTypeId));

		return json({ success: true });
	} catch (error) {
		console.error('Failed to update question order:', error);
		return json({ success: false, message: 'Failed to update question order' }, { status: 500 });
	}
}
