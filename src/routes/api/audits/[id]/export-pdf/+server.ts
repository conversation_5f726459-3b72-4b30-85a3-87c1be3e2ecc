import { json } from '@sveltejs/kit';
import { AuditResultService } from '$lib/server/services/audits';
import { QuestionsService } from '$lib/server/services/questions';
import { t, loadTranslations } from '$lib/translations';
import { createAuditPdfDefinition } from '$lib/server/templates/audit-pdf';
import { ScoreService } from '$lib/server/services/scoreNormalization';

export async function GET({ params, url }) {
	try {
		const auditId = params.id;
		const userLanguage = url.searchParams.get('lang') || 'en';
		const plantSlug = url.searchParams.get('plantSlug');
		const companySlug = url.searchParams.get('companySlug');

		if (!plantSlug) {
			return json({ error: 'Plant slug is required' }, { status: 400 });
		}

		if (!companySlug) {
			return json({ error: 'Company slug is required' }, { status: 400 });
		}

		await loadTranslations(userLanguage);

		// Get services
		const { PlantsService, CompanyService } = await import('$lib/server/services/tenants');
		
		// Get plantId and companyId
		const plantId = await PlantsService.getPlantIdBySlug(plantSlug);
		const expectedCompany = await CompanyService.getCompanyBySlug(companySlug);
		const actualCompanyId = await PlantsService.getCompanyIdByPlantId(plantId);

		// Validate that the plant belongs to the expected company
		if (!expectedCompany || !actualCompanyId || expectedCompany.id !== actualCompanyId) {
			return json({ error: 'Plant does not belong to the specified company' }, { status: 403 });
		}

		// Get company details
		let companyInfo = null;
		if (actualCompanyId) {
			companyInfo = await CompanyService.getCompanyById(actualCompanyId);
		}

		// Dynamický import pdfMake a fontů
		const pdfMake = (await import('pdfmake/build/pdfmake')).default;
		const pdfFonts = (await import('pdfmake/build/vfs_fonts')).default;

		// Inicializace VFS
		pdfMake.vfs = pdfFonts.vfs;

		// Definice fontů
		const fonts = {
			Roboto: {
				normal: 'Roboto-Regular.ttf',
				bold: 'Roboto-Medium.ttf',
				italics: 'Roboto-Italic.ttf',
				bolditalics: 'Roboto-MediumItalic.ttf'
			}
		};

		const [auditInfo, templateQuestions, answers, allQuestions, auditSuccessRate] =
			await Promise.all([
				AuditResultService.getAuditInfo(auditId),
				AuditResultService.getResultsQuestions(auditId),
				AuditResultService.getAnswers(auditId),
				QuestionsService.getQuestions(plantId),
				ScoreService.computeSuccessRateOfAudit(auditId)
			]);

		if (!auditInfo) {
			return json({ error: 'Audit not found' }, { status: 404 });
		}

		const questionsMap: { [key: string]: (typeof allQuestions)[number] } = {};
		allQuestions.forEach((q) => {
			if (q.id) {
				questionsMap[q.id] = q;
			}
		});

		const flatQuestions: {
			id: string;
			text: string;
			subtext: string;
			answer: { value: string; note: string } | null;
		}[] = [];
		if (templateQuestions) {
			Object.values(templateQuestions).forEach((category) => {
				if (category && category.questions && Array.isArray(category.questions)) {
					category.questions.forEach((question) => {
						if (question && question.questionId) {
							const questionDetails = questionsMap[question.questionId];
							const answer = answers.find((a) => a.questionId === question.questionId);

							flatQuestions.push({
								id: question.questionId,
								text: questionDetails?.text || t.get('audits.results.unknownQuestion'),
								subtext: questionDetails?.subtext || '',
								answer: answer
									? {
											value: answer.evaluationValue,
											note: answer.note || ''
										}
									: null
							});
						}
					});
				}
			});
		}

		const docDefinition = await createAuditPdfDefinition(
			auditInfo,
			flatQuestions,
			userLanguage,
			plantId,
			auditSuccessRate,
			companyInfo
		);
		const pdfDoc = pdfMake.createPdf(docDefinition, undefined, fonts);
		const pdfBuffer = await new Promise<Buffer>((resolve) => {
			pdfDoc.getBuffer((buffer: Buffer) => {
				resolve(buffer);
			});
		});

		return new Response(new Uint8Array(pdfBuffer), {
			headers: {
				'Content-Type': 'application/pdf',
				'Content-Disposition': `attachment; filename="audit-${auditInfo.code || auditId}.pdf"`
			}
		});
	} catch (error) {
		console.error('Error generating PDF:', error);
		return json({ error: 'Failed to generate PDF' }, { status: 500 });
	}
}
