import { json } from '@sveltejs/kit';
import { getTags, createTag } from '$lib/server/services/tags/tags';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
	try {
		const plantSlug = url.searchParams.get('plantSlug');

		if (!plantSlug) {
			return json({ error: 'Plant slug is required' }, { status: 400 });
		}

		// Get plantId from slug
		const { PlantsService } = await import('$lib/server/services/tenants');
		const plantId = await PlantsService.getPlantIdBySlug(plantSlug);

		const tags = await getTags(plantId);
		return json(tags);
	} catch (error) {
		console.error('Failed to fetch tags:', error);
		return json({ error: 'Failed to fetch tags' }, { status: 500 });
	}
};

export const POST: RequestHandler = async ({ request, url, locals }) => {
	try {
		const { name } = await request.json();
		const plantSlug = url.searchParams.get('plantSlug');

		if (!name || typeof name !== 'string') {
			return json({ error: 'Tag name is required' }, { status: 400 });
		}

		if (!plantSlug) {
			return json({ error: 'Plant slug is required' }, { status: 400 });
		}

		if (!locals.user) {
			return json({ error: 'User not authenticated' }, { status: 401 });
		}

		// Get plantId from slug
		const { PlantsService } = await import('$lib/server/services/tenants');
		const plantId = await PlantsService.getPlantIdBySlug(plantSlug);

		const tag = await createTag(
			{
				name: name.trim(),
				color: '',
				textColor: ''
			},
			plantId
		);

		if (!tag) {
			return json({ error: 'Failed to create tag' }, { status: 500 });
		}

		return json(tag, { status: 201 });
	} catch (error) {
		console.error('Failed to create tag:', error);
		return json({ error: 'Failed to create tag' }, { status: 500 });
	}
};
