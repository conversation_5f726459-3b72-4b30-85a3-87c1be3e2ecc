import { json, error } from '@sveltejs/kit';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
	SECRET_AWS_ACCESS_KEY_ID,
	SECRET_AWS_SECRET_ACCESS_KEY,
	SECRET_AWS_REGION,
	SECRET_AWS_BUCKET
} from '$env/static/private';
import { S3Client } from '@aws-sdk/client-s3';
import { getStorageEnvironmentPrefix, S3_FOLDERS } from '$lib/server/storage/index.js';
import { PlantsService } from '$lib/server/services/tenants/index.js';
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from '$lib/constants/file.js';
import crypto from 'crypto';
import { CompanyService } from '$lib/server/services/tenants';

const s3Client = new S3Client({
	credentials: {
		accessKeyId: SECRET_AWS_ACCESS_KEY_ID,
		secretAccessKey: SECRET_AWS_SECRET_ACCESS_KEY
	},
	region: SECRET_AWS_REGION
});

const BUCKET_NAME = SECRET_AWS_BUCKET;

export async function POST({ request }) {
	try {
		const body = await request.json();
		const {
			fileName,
			fileSize,
			contentType,
			companySlug,
			plantSlug,
			context = 'audits_attachments'
		} = body;

		// Validation
		if (!fileName || !fileSize || !contentType) {
			throw error(400, 'Missing required fields: fileName, fileSize, contentType');
		}

		if (fileSize > MAX_FILE_SIZE) {
			throw error(400, `File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024} MB`);
		}

		if (!ALLOWED_FILE_TYPES.includes(contentType)) {
			throw error(400, 'Unsupported file type');
		}

		let companyId: string | undefined = undefined;
		if (companySlug) {
			const company = await CompanyService.getCompanyBySlug(companySlug);
			if (!company) {
				throw error(404, `Company with slug "${companySlug}" not found`);
			}
			companyId = company.id;
		}
		let plantId: string | undefined = undefined;
		if (plantSlug) {
			plantId = await PlantsService.getPlantIdBySlug(plantSlug);
		}

		// Generate unique S3 key
		const fileExtension = fileName.split('.').pop() || '';
		const uniqueFileName = `${crypto.randomBytes(16).toString('hex')}.${fileExtension}`;

		// Build S3 key with environment prefix
		const environmentPrefix = getStorageEnvironmentPrefix();
		const s3Key =
			plantId && companyId
				? `${environmentPrefix}/${companyId}/${S3_FOLDERS.PLANTS}/${plantId}/${context}/${uniqueFileName}`
				: `${environmentPrefix}/${context}/${uniqueFileName}`;

		// Create presigned URL for PUT operation
		const command = new PutObjectCommand({
			Bucket: BUCKET_NAME,
			Key: s3Key,
			ContentType: contentType,
			ContentLength: fileSize
		});

		const presignedUrl = await getSignedUrl(s3Client, command, {
			expiresIn: 15 * 60 // 15 minutes
		});

		return json({
			uploadUrl: presignedUrl,
			s3Key,
			fileName: uniqueFileName,
			expiresIn: 15 * 60
		});
	} catch (err) {
		console.error('Error generating presigned URL:', err);

		if (err instanceof Error && 'status' in err) {
			throw err; // Re-throw SvelteKit errors
		}

		throw error(500, 'Failed to generate upload URL');
	}
}
