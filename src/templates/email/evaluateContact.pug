doctype html
html(lang=locale)
    head
        meta(charset="UTF-8")
        meta(name="viewport" content="width=device-width, initial-scale=1.0")
        title= headerTitle
        style.
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f4f4f4;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                padding: 0;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #536D9A 0%, #2E384F 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
                font-weight: 600;
            }
            .content {
                padding: 30px;
            }
            .greeting {
                font-size: 16px;
                margin-bottom: 20px;
                color: #2E384F;
            }
            .intro {
                font-size: 14px;
                margin-bottom: 25px;
                color: #666;
            }
            .audit-info {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 6px;
                margin-bottom: 25px;
                border-left: 4px solid #536D9A;
            }
            .audit-name {
                font-size: 18px;
                font-weight: 600;
                color: #2E384F;
                margin-bottom: 15px;
            }
            .question-section, .message-section, .sender-section {
                margin-bottom: 20px;
            }
            .label {
                font-weight: 600;
                color: #2E384F;
                margin-bottom: 8px;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .question-text {
                background-color: #fff;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
                font-style: italic;
                color: #555;
            }
            .message-text {
                background-color: #fff;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
                color: #333;
                white-space: pre-wrap;
            }
            .sender-info {
                background-color: #fff;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
                color: #333;
                font-weight: 500;
            }
            .footer {
                background-color: #f8f9fa;
                padding: 20px 30px;
                text-align: center;
                font-size: 12px;
                color: #888;
                border-top: 1px solid #e0e0e0;
            }
            .divider {
                height: 1px;
                background-color: #e0e0e0;
                margin: 25px 0;
            }

    body
        .container
            .header
                h1 #{headerTitle}
            
            .content
                p.greeting #{greeting}
                
                p.intro #{intro}
                
                .audit-info
                    .audit-name #{auditTypeName}
                    
                    if questionText
                        .question-section
                            .label #{questionLabel}
                            .question-text #{questionText}
                    
                    .divider
                    
                    .message-section
                        .label #{messageLabel}
                        .message-text #{message}
                    
                    if senderName
                        .sender-section
                            .label #{senderLabel}
                            .sender-info #{senderName}
            
            .footer
                p #{footer}

