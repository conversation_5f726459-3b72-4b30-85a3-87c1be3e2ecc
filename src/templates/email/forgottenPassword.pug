doctype html
html(lang=locale)
    head
        meta(charset="utf-8")
        meta(name="viewport", content="width=device-width, initial-scale=1.0")
        title #{subject}
        style.
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f4f4f4;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 40px auto;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #536D9A 0%, #2E384F 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 600;
            }
            .header p {
                margin: 10px 0 0;
                font-size: 18px;
                opacity: 0.9;
            }
            .content {
                padding: 40px 30px;
            }
            .content p {
                margin: 0 0 20px 0;
                font-size: 16px;
                line-height: 1.6;
            }
            .button {
                display: inline-block;
                background: #e9ecef;
                color: white;
                text-decoration: none;
                padding: 15px 30px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 16px;
                margin: 20px 0;
                transition: transform 0.2s, box-shadow 0.2s;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            .button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }
            .footer {
                background: #f8f9fa;
                padding: 20px 30px;
                text-align: center;
                border-top: 1px solid #e9ecef;
            }
            .footer p {
                margin: 0;
                font-size: 14px;
                color: #6c757d;
            }
            .security-notice {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 6px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }
            .link-text {
                word-break: break-all;
                color: #536D9A;
                background: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
                font-family: monospace;
                font-size: 14px;
            }
            .logo {
                font-size: 32px;
                font-weight: 800;
                letter-spacing: -1px;
                margin-bottom: 5px;
            }
    body
        .container
            .header
                .logo LeanAudit
                h1 #{headerTitle}
            
            .content
                p #{text}
                
                p(style="text-align: center;")
                    a.button(href=link) #{buttonText}
                
                .security-notice
                    strong= securityNoticeTitle
                    br
                    | #{additionalInfo}
                
                p= fallbackText
                p.link-text #{link}
                
                p #{regards}
                br
                | #{teamName}
            
            .footer
                p #{footerInfo}
                p #{supportText}
