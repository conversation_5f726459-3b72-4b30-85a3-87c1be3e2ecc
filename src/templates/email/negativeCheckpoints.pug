doctype html
html(lang="cs")
  head
    meta(charset="UTF-8")
    meta(name="viewport" content="width=device-width, initial-scale=1.0")
    title= headerTitle
    style.
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f4f4f4;
      }
      .container {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
      }
      .header {
        border-bottom: 3px solid #007bff;
        padding-bottom: 20px;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #007bff;
        margin: 0;
        font-size: 28px;
      }
      .audit-info {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        border-left: 4px solid #007bff;
      }
      .audit-info h3 {
        margin-top: 0;
        color: #007bff;
      }
      .checkpoint {
        background-color: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #e53e3e;
      }
      .checkpoint-header {
        font-weight: bold;
        color: #e53e3e;
        margin-bottom: 10px;
      }
      .checkpoint-value {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        color: white;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 10px;
      }
      .checkpoint-note {
        background-color: #f7fafc;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
        font-style: italic;
        border-left: 3px solid #cbd5e0;
      }
      .button {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 6px;
        font-weight: bold;
        margin: 20px 0;
      }
      .footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        font-size: 14px;
        color: #666;
      }
  body
    .container
      .header
        h1= headerTitle
      
      p= greeting + " " + recipientName + ","
      
      p= intro
      
      .audit-info
        h3= auditInfoTitle
        p
          strong= auditLabel + ": "
          | #{auditTitle} (#{auditCode})
        p
          strong= workplaceLabel + ": "
          | #{workplaceName}
        p
          strong= auditorLabel + ": "
          | #{auditorName}
        p
          strong= dateLabel + ": "
          | #{auditDate}
      
      p= negativeFound.replace('{count}', totalNegativeCount)
      
      h3= checkpointsHeader
      
      each checkpoint in negativeCheckpoints
        .checkpoint
          .checkpoint-header
            | #{checkpoint.questionText}
            if checkpoint.evaluationType === 'points'
              span.checkpoint-value(style="background-color: #e53e3e")= checkpoint.evaluationValue
            else if checkpoint.evaluationType === 'percentage'
              span.checkpoint-value(style="background-color: #e53e3e")= checkpoint.evaluationValue + '%'
            else
              span.checkpoint-value(style="background-color: #e53e3e")= checkpoint.evaluationValue
          
          if checkpoint.note
            .checkpoint-note
              strong= noteLabel + ": "
              | #{checkpoint.note}
      
      p(style="text-align: center; margin: 30px 0;")
        a.button(href=auditUrl)= viewAuditButton
      
      .footer
        p= footer
        p= regards
        p
          strong= auditTeam
